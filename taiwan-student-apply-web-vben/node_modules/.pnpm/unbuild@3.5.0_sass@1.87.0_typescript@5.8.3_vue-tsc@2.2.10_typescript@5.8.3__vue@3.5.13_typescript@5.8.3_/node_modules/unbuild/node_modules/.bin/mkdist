#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules/mkdist/dist/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules/mkdist/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules/mkdist/dist/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules/mkdist/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules/mkdist/dist/cli.cjs" "$@"
else
  exec node  "$basedir/../../../../../mkdist@2.3.0_sass@1.87.0_typescript@5.8.3_vue-tsc@2.2.10_typescript@5.8.3__vue@3.5.13_typescript@5.8.3_/node_modules/mkdist/dist/cli.cjs" "$@"
fi
