#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/vitest@3.1.2_@types+node@22.15.3_happy-dom@17.4.6_jiti@2.4.2_less@4.3.0_sass@1.87.0_terser@5.39.0_yaml@2.7.1/node_modules/vitest/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/vitest@3.1.2_@types+node@22.15.3_happy-dom@17.4.6_jiti@2.4.2_less@4.3.0_sass@1.87.0_terser@5.39.0_yaml@2.7.1/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/vitest@3.1.2_@types+node@22.15.3_happy-dom@17.4.6_jiti@2.4.2_less@4.3.0_sass@1.87.0_terser@5.39.0_yaml@2.7.1/node_modules/vitest/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/vitest@3.1.2_@types+node@22.15.3_happy-dom@17.4.6_jiti@2.4.2_less@4.3.0_sass@1.87.0_terser@5.39.0_yaml@2.7.1/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
