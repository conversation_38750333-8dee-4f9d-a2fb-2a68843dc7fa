#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/crc-32@1.2.2/node_modules/crc-32/bin/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/crc-32@1.2.2/node_modules/crc-32/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/crc-32@1.2.2/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/crc-32@1.2.2/node_modules/crc-32/bin/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/crc-32@1.2.2/node_modules/crc-32/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/crc-32@1.2.2/node_modules:/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../crc-32@1.2.2/node_modules/crc-32/bin/crc32.njs" "$@"
else
  exec node  "$basedir/../../../../../crc-32@1.2.2/node_modules/crc-32/bin/crc32.njs" "$@"
fi
