hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@6.0.0':
    '@ant-design/colors': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/icons-vue@7.0.1(vue@3.5.13(typescript@5.8.3))':
    '@ant-design/icons-vue': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@ast-grep/napi-darwin-arm64@0.37.0':
    '@ast-grep/napi-darwin-arm64': private
  '@ast-grep/napi-darwin-x64@0.37.0':
    '@ast-grep/napi-darwin-x64': private
  '@ast-grep/napi-linux-arm64-gnu@0.37.0':
    '@ast-grep/napi-linux-arm64-gnu': private
  '@ast-grep/napi-linux-arm64-musl@0.37.0':
    '@ast-grep/napi-linux-arm64-musl': private
  '@ast-grep/napi-linux-x64-gnu@0.37.0':
    '@ast-grep/napi-linux-x64-gnu': private
  '@ast-grep/napi-linux-x64-musl@0.37.0':
    '@ast-grep/napi-linux-x64-musl': private
  '@ast-grep/napi-win32-arm64-msvc@0.37.0':
    '@ast-grep/napi-win32-arm64-msvc': private
  '@ast-grep/napi-win32-ia32-msvc@0.37.0':
    '@ast-grep/napi-win32-ia32-msvc': private
  '@ast-grep/napi-win32-x64-msvc@0.37.0':
    '@ast-grep/napi-win32-x64-msvc': private
  '@ast-grep/napi@0.37.0':
    '@ast-grep/napi': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': private
  '@babel/core@7.26.10':
    '@babel/core': private
  '@babel/generator@7.27.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.25.9':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.0':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.0(@babel/core@7.26.10)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.0(@babel/core@7.26.10)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.26.10)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.25.9':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.25.9':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.10)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.25.9':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.0':
    '@babel/helpers': private
  '@babel/parser@7.27.0':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-decorators@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.10)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-decorators@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.10)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.26.8(@babel/core@7.26.10)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.26.5(@babel/core@7.26.10)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.26.3(@babel/core@7.26.10)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.26.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6(@babel/core@7.26.10)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.26.8(@babel/core@7.26.10)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.26.9(@babel/core@7.26.10)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.10)':
    '@babel/preset-modules': private
  '@babel/preset-typescript@7.27.0(@babel/core@7.26.10)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@babel/template@7.27.0':
    '@babel/template': private
  '@babel/traverse@7.27.0':
    '@babel/traverse': private
  '@babel/types@7.27.0':
    '@babel/types': private
  '@changesets/errors@0.2.0':
    '@changesets/errors': private
  '@changesets/git@3.0.4':
    '@changesets/git': private
  '@changesets/types@4.1.0':
    '@changesets/types': private
  '@clack/core@0.4.2':
    '@clack/core': private
  '@clack/prompts@0.10.1':
    '@clack/prompts': private
  '@cloudflare/kv-asset-handler@0.4.0':
    '@cloudflare/kv-asset-handler': private
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@commitlint/cli@19.8.0(@types/node@22.15.3)(typescript@5.8.3)':
    '@commitlint/cli': public
  '@commitlint/config-conventional@19.8.0':
    '@commitlint/config-conventional': public
  '@commitlint/config-validator@19.8.0':
    '@commitlint/config-validator': public
  '@commitlint/ensure@19.8.0':
    '@commitlint/ensure': public
  '@commitlint/execute-rule@19.8.0':
    '@commitlint/execute-rule': public
  '@commitlint/format@19.8.0':
    '@commitlint/format': public
  '@commitlint/is-ignored@19.8.0':
    '@commitlint/is-ignored': public
  '@commitlint/lint@19.8.0':
    '@commitlint/lint': public
  '@commitlint/load@19.8.0(@types/node@22.15.3)(typescript@5.8.3)':
    '@commitlint/load': public
  '@commitlint/message@19.8.0':
    '@commitlint/message': public
  '@commitlint/parse@19.8.0':
    '@commitlint/parse': public
  '@commitlint/read@19.8.0':
    '@commitlint/read': public
  '@commitlint/resolve-extends@19.8.0':
    '@commitlint/resolve-extends': public
  '@commitlint/rules@19.8.0':
    '@commitlint/rules': public
  '@commitlint/to-lines@19.8.0':
    '@commitlint/to-lines': public
  '@commitlint/top-level@19.8.0':
    '@commitlint/top-level': public
  '@commitlint/types@19.8.0':
    '@commitlint/types': public
  '@csstools/cascade-layer-name-parser@2.0.4(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/cascade-layer-name-parser': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.3(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.9(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.3':
    '@csstools/css-tokenizer': private
  '@csstools/media-query-list-parser@3.0.1(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/media-query-list-parser': private
  '@csstools/postcss-cascade-layers@5.0.1(postcss@8.5.3)':
    '@csstools/postcss-cascade-layers': public
  '@csstools/postcss-color-function@4.0.9(postcss@8.5.3)':
    '@csstools/postcss-color-function': public
  '@csstools/postcss-color-mix-function@3.0.9(postcss@8.5.3)':
    '@csstools/postcss-color-mix-function': public
  '@csstools/postcss-content-alt-text@2.0.5(postcss@8.5.3)':
    '@csstools/postcss-content-alt-text': public
  '@csstools/postcss-exponential-functions@2.0.8(postcss@8.5.3)':
    '@csstools/postcss-exponential-functions': public
  '@csstools/postcss-font-format-keywords@4.0.0(postcss@8.5.3)':
    '@csstools/postcss-font-format-keywords': public
  '@csstools/postcss-gamut-mapping@2.0.9(postcss@8.5.3)':
    '@csstools/postcss-gamut-mapping': public
  '@csstools/postcss-gradients-interpolation-method@5.0.9(postcss@8.5.3)':
    '@csstools/postcss-gradients-interpolation-method': public
  '@csstools/postcss-hwb-function@4.0.9(postcss@8.5.3)':
    '@csstools/postcss-hwb-function': public
  '@csstools/postcss-ic-unit@4.0.1(postcss@8.5.3)':
    '@csstools/postcss-ic-unit': public
  '@csstools/postcss-initial@2.0.1(postcss@8.5.3)':
    '@csstools/postcss-initial': public
  '@csstools/postcss-is-pseudo-class@5.0.1(postcss@8.5.3)':
    '@csstools/postcss-is-pseudo-class': public
  '@csstools/postcss-light-dark-function@2.0.8(postcss@8.5.3)':
    '@csstools/postcss-light-dark-function': public
  '@csstools/postcss-logical-float-and-clear@3.0.0(postcss@8.5.3)':
    '@csstools/postcss-logical-float-and-clear': public
  '@csstools/postcss-logical-overflow@2.0.0(postcss@8.5.3)':
    '@csstools/postcss-logical-overflow': public
  '@csstools/postcss-logical-overscroll-behavior@2.0.0(postcss@8.5.3)':
    '@csstools/postcss-logical-overscroll-behavior': public
  '@csstools/postcss-logical-resize@3.0.0(postcss@8.5.3)':
    '@csstools/postcss-logical-resize': public
  '@csstools/postcss-logical-viewport-units@3.0.3(postcss@8.5.3)':
    '@csstools/postcss-logical-viewport-units': public
  '@csstools/postcss-media-minmax@2.0.8(postcss@8.5.3)':
    '@csstools/postcss-media-minmax': public
  '@csstools/postcss-media-queries-aspect-ratio-number-values@3.0.4(postcss@8.5.3)':
    '@csstools/postcss-media-queries-aspect-ratio-number-values': public
  '@csstools/postcss-nested-calc@4.0.0(postcss@8.5.3)':
    '@csstools/postcss-nested-calc': public
  '@csstools/postcss-normalize-display-values@4.0.0(postcss@8.5.3)':
    '@csstools/postcss-normalize-display-values': public
  '@csstools/postcss-oklab-function@4.0.9(postcss@8.5.3)':
    '@csstools/postcss-oklab-function': public
  '@csstools/postcss-progressive-custom-properties@4.0.1(postcss@8.5.3)':
    '@csstools/postcss-progressive-custom-properties': public
  '@csstools/postcss-random-function@2.0.0(postcss@8.5.3)':
    '@csstools/postcss-random-function': public
  '@csstools/postcss-relative-color-syntax@3.0.9(postcss@8.5.3)':
    '@csstools/postcss-relative-color-syntax': public
  '@csstools/postcss-scope-pseudo-class@4.0.1(postcss@8.5.3)':
    '@csstools/postcss-scope-pseudo-class': public
  '@csstools/postcss-sign-functions@1.1.3(postcss@8.5.3)':
    '@csstools/postcss-sign-functions': public
  '@csstools/postcss-stepped-value-functions@4.0.8(postcss@8.5.3)':
    '@csstools/postcss-stepped-value-functions': public
  '@csstools/postcss-text-decoration-shorthand@4.0.2(postcss@8.5.3)':
    '@csstools/postcss-text-decoration-shorthand': public
  '@csstools/postcss-trigonometric-functions@4.0.8(postcss@8.5.3)':
    '@csstools/postcss-trigonometric-functions': public
  '@csstools/postcss-unset-value@4.0.0(postcss@8.5.3)':
    '@csstools/postcss-unset-value': public
  '@csstools/selector-resolve-nested@3.0.0(postcss-selector-parser@7.1.0)':
    '@csstools/selector-resolve-nested': private
  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.1.0)':
    '@csstools/selector-specificity': private
  '@csstools/utilities@2.0.0(postcss@8.5.3)':
    '@csstools/utilities': private
  '@ctrl/tinycolor@4.1.0':
    '@ctrl/tinycolor': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@dependents/detective-less@4.1.0':
    '@dependents/detective-less': private
  '@dual-bundle/import-meta-resolve@4.1.0':
    '@dual-bundle/import-meta-resolve': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/unitless@0.8.1':
    '@emotion/unitless': private
  '@es-joy/jsdoccomment@0.50.0':
    '@es-joy/jsdoccomment': private
  '@esbuild/aix-ppc64@0.25.3':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.3':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.3':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.3':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.3':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.3':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.3':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.3':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.3':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.3':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.3':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.3':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.3':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.3':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.3':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.3':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.3':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.3':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.3':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.3':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.3':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.3':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.3':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.3':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.3':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.6.1(eslint@9.26.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.13.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.26.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': private
  '@fastify/busboy@3.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@floating-ui/vue@1.1.6(vue@3.5.13(typescript@5.8.3))':
    '@floating-ui/vue': private
  '@gar/promisify@1.1.3':
    '@gar/promisify': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@iconify/json@2.2.334':
    '@iconify/json': private
  '@iconify/tailwind@1.2.0':
    '@iconify/tailwind': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/vue@5.0.0(vue@3.5.13(typescript@5.8.3))':
    '@iconify/vue': private
  '@internationalized/date@3.8.0':
    '@internationalized/date': private
  '@internationalized/number@3.6.1':
    '@internationalized/number': private
  '@intlify/bundle-utils@10.0.1(vue-i18n@11.1.3(vue@3.5.13(typescript@5.8.3)))':
    '@intlify/bundle-utils': private
  '@intlify/core-base@11.1.3':
    '@intlify/core-base': private
  '@intlify/message-compiler@11.1.3':
    '@intlify/message-compiler': private
  '@intlify/shared@11.1.3':
    '@intlify/shared': private
  '@intlify/unplugin-vue-i18n@6.0.8(@vue/compiler-dom@3.5.13)(eslint@9.26.0(jiti@2.4.2))(rollup@4.40.1)(typescript@5.8.3)(vue-i18n@11.1.3(vue@3.5.13(typescript@5.8.3)))(vue@3.5.13(typescript@5.8.3))':
    '@intlify/unplugin-vue-i18n': private
  '@intlify/vue-i18n-extensions@8.0.0(@intlify/shared@11.1.3)(@vue/compiler-dom@3.5.13)(vue-i18n@11.1.3(vue@3.5.13(typescript@5.8.3)))(vue@3.5.13(typescript@5.8.3))':
    '@intlify/vue-i18n-extensions': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@jspm/generator@2.5.1':
    '@jspm/generator': private
  '@jspm/import-map@1.1.0':
    '@jspm/import-map': private
  '@keyv/serialize@1.0.3':
    '@keyv/serialize': private
  '@manypkg/find-root@3.0.0':
    '@manypkg/find-root': private
  '@manypkg/get-packages@3.0.0':
    '@manypkg/get-packages': private
  '@manypkg/tools@2.0.0':
    '@manypkg/tools': private
  '@mapbox/node-pre-gyp@2.0.0(encoding@0.1.13)':
    '@mapbox/node-pre-gyp': private
  '@microsoft/api-extractor-model@7.30.5(@types/node@22.15.3)':
    '@microsoft/api-extractor-model': private
  '@microsoft/api-extractor@7.52.5(@types/node@22.15.3)':
    '@microsoft/api-extractor': private
  '@microsoft/tsdoc-config@0.17.1':
    '@microsoft/tsdoc-config': private
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': private
  '@modelcontextprotocol/sdk@1.11.0':
    '@modelcontextprotocol/sdk': private
  '@netlify/binary-info@1.0.0':
    '@netlify/binary-info': private
  '@netlify/blobs@8.2.0':
    '@netlify/blobs': private
  '@netlify/dev-utils@1.1.0':
    '@netlify/dev-utils': private
  '@netlify/functions@3.1.2(encoding@0.1.13)(rollup@4.40.1)':
    '@netlify/functions': private
  '@netlify/node-cookies@0.1.0':
    '@netlify/node-cookies': private
  '@netlify/open-api@2.37.0':
    '@netlify/open-api': private
  '@netlify/serverless-functions-api@1.33.0':
    '@netlify/serverless-functions-api': private
  '@netlify/zip-it-and-ship-it@9.43.1(encoding@0.1.13)(rollup@4.40.1)':
    '@netlify/zip-it-and-ship-it': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@npmcli/fs@1.1.1':
    '@npmcli/fs': private
  '@npmcli/move-file@1.1.2':
    '@npmcli/move-file': private
  '@nuxt/kit@3.17.0(magicast@0.3.5)':
    '@nuxt/kit': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-wasm@2.5.1':
    '@parcel/watcher-wasm': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.4':
    '@pkgr/core': private
  '@pnpm/config.env-replace@1.1.0':
    '@pnpm/config.env-replace': private
  '@pnpm/constants@1001.1.0':
    '@pnpm/constants': private
  '@pnpm/error@1000.0.2':
    '@pnpm/error': private
  '@pnpm/network.ca-file@1.0.2':
    '@pnpm/network.ca-file': private
  '@pnpm/npm-conf@2.3.1':
    '@pnpm/npm-conf': private
  '@pnpm/types@1000.5.0':
    '@pnpm/types': private
  '@pnpm/workspace.read-manifest@1000.1.4':
    '@pnpm/workspace.read-manifest': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@poppinss/colors@4.1.4':
    '@poppinss/colors': private
  '@poppinss/dumper@0.6.3':
    '@poppinss/dumper': private
  '@poppinss/exception@1.2.1':
    '@poppinss/exception': private
  '@publint/pack@0.1.2':
    '@publint/pack': private
  '@rollup/plugin-alias@5.1.1(rollup@4.40.1)':
    '@rollup/plugin-alias': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.26.10)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-commonjs@28.0.3(rollup@4.40.1)':
    '@rollup/plugin-commonjs': private
  '@rollup/plugin-inject@5.0.5(rollup@4.40.1)':
    '@rollup/plugin-inject': private
  '@rollup/plugin-json@6.1.0(rollup@4.40.1)':
    '@rollup/plugin-json': private
  '@rollup/plugin-node-resolve@16.0.1(rollup@4.40.1)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@6.0.2(rollup@4.40.1)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-terser@0.4.4(rollup@4.40.1)':
    '@rollup/plugin-terser': private
  '@rollup/pluginutils@5.1.4(rollup@4.40.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.40.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.40.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.40.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.40.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.40.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.40.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.40.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.40.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.40.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.40.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.40.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.40.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.40.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.40.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.40.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.40.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.40.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.40.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.40.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.40.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@rushstack/node-core-library@5.13.0(@types/node@22.15.3)':
    '@rushstack/node-core-library': private
  '@rushstack/rig-package@0.5.3':
    '@rushstack/rig-package': private
  '@rushstack/terminal@0.15.2(@types/node@22.15.3)':
    '@rushstack/terminal': private
  '@rushstack/ts-command-line@5.0.0(@types/node@22.15.3)':
    '@rushstack/ts-command-line': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@simonwep/pickr@1.8.2':
    '@simonwep/pickr': private
  '@sindresorhus/is@7.0.1':
    '@sindresorhus/is': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': private
  '@stylistic/stylelint-plugin@3.1.2(stylelint@16.19.1(typescript@5.8.3))':
    '@stylistic/stylelint-plugin': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/nesting@0.0.0-insiders.565cd3e(postcss@8.5.3)':
    '@tailwindcss/nesting': private
  '@tailwindcss/typography@0.5.16(tailwindcss@3.4.17)':
    '@tailwindcss/typography': private
  '@tanstack/store@0.7.0':
    '@tanstack/store': private
  '@tanstack/virtual-core@3.13.6':
    '@tanstack/virtual-core': private
  '@tanstack/vue-store@0.7.0(vue@3.5.13(typescript@5.8.3))':
    '@tanstack/vue-store': private
  '@tanstack/vue-virtual@3.13.6(vue@3.5.13(typescript@5.8.3))':
    '@tanstack/vue-virtual': private
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/archiver@6.0.3':
    '@types/archiver': private
  '@types/argparse@1.0.38':
    '@types/argparse': private
  '@types/bintrees@1.0.6':
    '@types/bintrees': private
  '@types/conventional-commits-parser@5.0.1':
    '@types/conventional-commits-parser': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/html-minifier-terser@7.0.2':
    '@types/html-minifier-terser': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/lodash.clonedeep@4.5.9':
    '@types/lodash.clonedeep': private
  '@types/lodash.get@4.4.9':
    '@types/lodash.get': private
  '@types/lodash.isequal@4.5.8':
    '@types/lodash.isequal': private
  '@types/lodash.set@4.3.9':
    '@types/lodash.set': private
  '@types/lodash@4.17.16':
    '@types/lodash': private
  '@types/minimatch@3.0.5':
    '@types/minimatch': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/nprogress@0.2.3':
    '@types/nprogress': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/postcss-import@14.0.3':
    '@types/postcss-import': public
  '@types/qrcode@1.5.5':
    '@types/qrcode': private
  '@types/qs@6.9.18':
    '@types/qs': private
  '@types/readdir-glob@1.1.5':
    '@types/readdir-glob': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/sortablejs@1.15.8':
    '@types/sortablejs': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/eslint-plugin@8.31.1(@typescript-eslint/parser@8.31.1(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.31.1(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.31.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.31.1(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.31.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.31.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.31.1(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.31.1':
    '@typescript-eslint/visitor-keys': private
  '@unrs/resolver-binding-darwin-arm64@1.7.2':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.7.2':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.7.2':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.2':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.7.2':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.7.2':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.7.2':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.7.2':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.7.2':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.7.2':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.7.2':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.7.2':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.7.2':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.7.2':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.7.2':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.7.2':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.7.2':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vee-validate/zod@4.15.0(vue@3.5.13(typescript@5.8.3))(zod@3.24.3)':
    '@vee-validate/zod': private
  '@vercel/nft@0.29.2(encoding@0.1.13)(rollup@4.40.1)':
    '@vercel/nft': private
  '@vitest/expect@3.1.2':
    '@vitest/expect': private
  '@vitest/mocker@3.1.2(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.1.2':
    '@vitest/pretty-format': private
  '@vitest/runner@3.1.2':
    '@vitest/runner': private
  '@vitest/snapshot@3.1.2':
    '@vitest/snapshot': private
  '@vitest/spy@3.1.2':
    '@vitest/spy': private
  '@vitest/utils@3.1.2':
    '@vitest/utils': private
  '@volar/language-core@2.4.13':
    '@volar/language-core': private
  '@volar/source-map@2.4.13':
    '@volar/source-map': private
  '@volar/typescript@2.4.13':
    '@volar/typescript': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.26.10)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.26.10)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.13':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.13':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.13':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.13':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.5':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.6(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1))(vue@3.5.13(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.6':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.6':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.0(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.13':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.13':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.13':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.13':
    '@vue/shared': private
  '@vueuse/core@13.1.0(vue@3.5.13(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/integrations@13.1.0(async-validator@4.2.5)(axios@1.9.0)(focus-trap@7.6.4)(jwt-decode@4.0.0)(nprogress@0.2.0)(qrcode@1.5.4)(sortablejs@1.15.6)(vue@3.5.13(typescript@5.8.3))':
    '@vueuse/integrations': private
  '@vueuse/metadata@13.1.0':
    '@vueuse/metadata': private
  '@vueuse/motion@3.0.3(magicast@0.3.5)(vue@3.5.13(typescript@5.8.3))':
    '@vueuse/motion': private
  '@vueuse/shared@13.1.0(vue@3.5.13(typescript@5.8.3))':
    '@vueuse/shared': private
  '@vxe-ui/core@4.1.0(vue@3.5.13(typescript@5.8.3))':
    '@vxe-ui/core': private
  '@whatwg-node/disposablestack@0.0.6':
    '@whatwg-node/disposablestack': private
  '@whatwg-node/fetch@0.10.6':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.7.18':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/promise-helpers@1.3.1':
    '@whatwg-node/promise-helpers': private
  '@whatwg-node/server@0.9.71':
    '@whatwg-node/server': private
  JSONStream@1.3.5:
    JSONStream: private
  abbrev@3.0.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@2.0.0:
    accepts: private
  acorn-import-attributes@1.9.5(acorn@8.14.1):
    acorn-import-attributes: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-draft-04@1.0.0(ajv@8.13.0):
    ajv-draft-04: private
  ajv-formats@3.0.1(ajv@8.13.0):
    ajv-formats: private
  ajv@6.12.6:
    ajv: private
  alien-signals@0.4.14:
    alien-signals: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ant-design-vue@4.2.6(vue@3.5.13(typescript@5.8.3)):
    ant-design-vue: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  apps/web-antd:
    '@vben/web-antd': private
  aproba@2.0.0:
    aproba: private
  archiver-utils@5.0.2:
    archiver-utils: private
  archiver@7.0.1:
    archiver: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  arg@5.0.2:
    arg: private
  argparse@1.0.10:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-differ@3.0.0:
    array-differ: private
  array-ify@1.0.0:
    array-ify: private
  array-tree-filter@2.1.0:
    array-tree-filter: private
  array-union@2.1.0:
    array-union: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  arrify@2.0.1:
    arrify: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-module-types@5.0.0:
    ast-module-types: private
  astral-regex@2.0.0:
    astral-regex: private
  async-function@1.0.0:
    async-function: private
  async-sema@3.1.1:
    async-sema: private
  async-validator@4.2.5:
    async-validator: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  atomically@2.0.3:
    atomically: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios-mock-adapter@2.1.0(axios@1.9.0):
    axios-mock-adapter: private
  axios@1.9.0:
    axios: private
  b4a@1.6.7:
    b4a: private
  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.26.10):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.26.10):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.26.10):
    babel-plugin-polyfill-regenerator: private
  balanced-match@2.0.0:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  base64-js@1.5.1:
    base64-js: private
  better-path-resolve@1.0.0:
    better-path-resolve: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bindings@1.5.0:
    bindings: private
  bintrees@1.0.2:
    bintrees: private
  birpc@2.3.0:
    birpc: private
  body-parser@2.2.0:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  boxen@8.0.1:
    boxen: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  builtin-modules@5.0.0:
    builtin-modules: private
  bundle-name@4.1.0:
    bundle-name: private
  bytes@3.1.2:
    bytes: private
  c12@3.0.3(magicast@0.3.5):
    c12: private
  cac@6.7.14:
    cac: private
  cacache@15.3.0:
    cacache: private
  cacheable@1.8.10:
    cacheable: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsite@1.0.0:
    callsite: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001715:
    caniuse-lite: private
  chai@5.2.0:
    chai: private
  chalk@5.4.1:
    chalk: private
  check-error@2.1.1:
    check-error: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.0.0:
    cheerio: private
  chokidar@4.0.3:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  ci-info@4.2.0:
    ci-info: private
  circular-dependency-scanner@2.3.0:
    circular-dependency-scanner: private
  citty@0.1.6:
    citty: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  clean-css@5.3.3:
    clean-css: private
  clean-regexp@1.0.0:
    clean-regexp: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-boxes@3.0.0:
    cli-boxes: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-truncate@4.0.0:
    cli-truncate: private
  clipboard@2.0.11:
    clipboard: private
  clipboardy@4.0.0:
    clipboardy: private
  cliui@6.0.0:
    cliui: private
  clsx@2.1.1:
    clsx: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color-support@1.1.3:
    color-support: private
  color@3.2.1:
    color: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@12.1.0:
    commander: private
  comment-parser@1.4.1:
    comment-parser: private
  commitlint-plugin-function-rules@4.0.1(@commitlint/lint@19.8.0):
    commitlint-plugin-function-rules: private
  common-path-prefix@3.0.0:
    common-path-prefix: private
  common-tags@1.8.2:
    common-tags: private
  commondir@1.0.1:
    commondir: private
  compare-func@2.0.0:
    compare-func: private
  compare-versions@6.1.1:
    compare-versions: private
  compatx@0.2.0:
    compatx: private
  compress-commons@6.0.2:
    compress-commons: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.2.2:
    confbox: private
  config-chain@1.1.13:
    config-chain: private
  configstore@7.0.0:
    configstore: private
  connect-history-api-fallback@1.6.0:
    connect-history-api-fallback: private
  consola@3.4.2:
    consola: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  conventional-changelog-angular@7.0.0:
    conventional-changelog-angular: private
  conventional-changelog-conventionalcommits@7.0.2:
    conventional-changelog-conventionalcommits: private
  conventional-commits-parser@5.0.0:
    conventional-commits-parser: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-es@2.0.0:
    cookie-es: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@1.0.2:
    cookie: private
  copy-anything@2.0.6:
    copy-anything: private
  core-js-compat@3.41.0:
    core-js-compat: private
  core-js@3.41.0:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cosmiconfig-typescript-loader@6.1.0(@types/node@22.15.3)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    cosmiconfig-typescript-loader: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  cp-file@10.0.0:
    cp-file: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@6.0.0:
    crc32-stream: private
  cron-parser@4.9.0:
    cron-parser: private
  croner@9.0.0:
    croner: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crossws@0.3.4:
    crossws: private
  crypto-js@4.2.0:
    crypto-js: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-blank-pseudo@7.0.1(postcss@8.5.3):
    css-blank-pseudo: private
  css-declaration-sorter@7.2.0(postcss@8.5.3):
    css-declaration-sorter: private
  css-functions-list@3.2.3:
    css-functions-list: private
  css-has-pseudo@7.0.2(postcss@8.5.3):
    css-has-pseudo: private
  css-prefers-color-scheme@10.0.0(postcss@8.5.3):
    css-prefers-color-scheme: private
  css-select@5.1.0:
    css-select: private
  css-tree@3.1.0:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssdb@8.2.5:
    cssdb: private
  cssesc@3.0.0:
    cssesc: private
  cssnano-preset-default@7.0.6(postcss@8.5.3):
    cssnano-preset-default: private
  cssnano-utils@5.0.0(postcss@8.5.3):
    cssnano-utils: private
  cssnano@7.0.6(postcss@8.5.3):
    cssnano: private
  csso@5.0.5:
    csso: private
  csstype@3.1.3:
    csstype: private
  cz-git@1.11.1:
    cz-git: private
  czg@1.11.1:
    czg: public
  dargs@8.1.0:
    dargs: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dayjs@1.11.13:
    dayjs: private
  db0@0.3.2:
    db0: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.0:
    debug: private
  decache@4.6.2:
    decache: private
  decamelize@1.2.0:
    decamelize: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deep-pick-omit@1.2.1:
    deep-pick-omit: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegate@3.2.0:
    delegate: private
  delegates@1.0.0:
    delegates: private
  denque@2.1.0:
    denque: private
  depcheck@1.4.7:
    depcheck: private
  depd@2.0.0:
    depd: private
  deps-regex@0.2.0:
    deps-regex: private
  destr@2.0.5:
    destr: private
  detect-file@1.0.0:
    detect-file: private
  detect-libc@1.0.3:
    detect-libc: private
  detective-amd@5.0.2:
    detective-amd: private
  detective-cjs@5.0.1:
    detective-cjs: private
  detective-es6@4.0.1:
    detective-es6: private
  detective-postcss@6.1.3:
    detective-postcss: public
  detective-sass@5.0.3:
    detective-sass: private
  detective-scss@4.0.3:
    detective-scss: private
  detective-stylus@4.0.0:
    detective-stylus: private
  detective-typescript@11.2.0:
    detective-typescript: private
  didyoumean@1.2.2:
    didyoumean: private
  dijkstrajs@1.0.3:
    dijkstrajs: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  dom-align@1.12.4:
    dom-align: private
  dom-scroll-into-view@2.0.1:
    dom-scroll-into-view: private
  dom-serializer@2.0.0:
    dom-serializer: private
  dom-zindex@1.0.6:
    dom-zindex: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dot-prop@9.0.0:
    dot-prop: private
  dotenv-expand@8.0.3:
    dotenv-expand: private
  dotenv@16.5.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  echarts@5.6.0:
    echarts: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.143:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enabled@2.0.0:
    enabled: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding-sniffer@0.2.0:
    encoding-sniffer: private
  encoding@0.1.13:
    encoding: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  environment@1.1.0:
    environment: private
  err-code@2.0.3:
    err-code: private
  errno@0.1.8:
    errno: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: private
  errx@0.1.0:
    errx: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.3:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-goat@4.0.0:
    escape-goat: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-compat-utils@0.6.5(eslint@9.26.0(jiti@2.4.2)):
    eslint-compat-utils: private
  eslint-config-turbo@2.5.2(eslint@9.26.0(jiti@2.4.2))(turbo@2.5.2):
    eslint-config-turbo: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-json-compat-utils@0.2.1(eslint@9.26.0(jiti@2.4.2))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: private
  eslint-plugin-command@3.2.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-es-x: private
  eslint-plugin-eslint-comments@3.2.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-eslint-comments: private
  eslint-plugin-import-x@4.11.0(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-import-x: private
  eslint-plugin-jsdoc@50.6.11(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.20.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.17.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-n: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-perfectionist@4.12.3(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-perfectionist: private
  eslint-plugin-prettier@5.2.6(@types/eslint@9.6.1)(eslint@9.26.0(jiti@2.4.2))(prettier@3.5.3):
    eslint-plugin-prettier: private
  eslint-plugin-regexp@2.7.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-regexp: private
  eslint-plugin-turbo@2.5.2(eslint@9.26.0(jiti@2.4.2))(turbo@2.5.2):
    eslint-plugin-turbo: private
  eslint-plugin-unicorn@59.0.0(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.31.1(@typescript-eslint/parser@8.31.1(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.26.0(jiti@2.4.2)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vitest@0.5.4(@typescript-eslint/eslint-plugin@8.31.1(@typescript-eslint/parser@8.31.1(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.26.0(jiti@2.4.2))(typescript@5.8.3)(vitest@3.1.2(@types/node@22.15.3)(happy-dom@17.4.6)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    eslint-plugin-vitest: private
  eslint-plugin-vue@10.1.0(eslint@9.26.0(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.26.0(jiti@2.4.2))):
    eslint-plugin-vue: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  eslint@9.26.0(jiti@2.4.2):
    eslint: public
  espree@10.3.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  eventsource-parser@3.0.1:
    eventsource-parser: private
  eventsource@3.0.6:
    eventsource: private
  execa@9.5.2:
    execa: private
  expand-tilde@2.0.2:
    expand-tilde: private
  expect-type@1.2.1:
    expect-type: private
  express-rate-limit@7.5.0(express@5.1.0):
    express-rate-limit: private
  express@5.1.0:
    express: private
  exsolve@1.0.5:
    exsolve: private
  extendable-error@0.1.7:
    extendable-error: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-string-compare@3.0.0:
    fast-string-compare: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  fecha@4.2.3:
    fecha: private
  fetch-blob@3.2.0:
    fetch-blob: private
  figures@6.1.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@5.1.0:
    filter-obj: private
  finalhandler@2.1.0:
    finalhandler: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@7.0.0:
    find-up: private
  findup-sync@5.0.0:
    findup-sync: private
  fix-dts-default-cjs-exports@1.0.1:
    fix-dts-default-cjs-exports: private
  flat-cache@6.1.8:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fn.name@1.1.0:
    fn.name: private
  focus-trap@7.6.4:
    focus-trap: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.2:
    form-data: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  framesync@6.1.2:
    framesync: private
  fresh@2.0.0:
    fresh: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gauge@3.0.2:
    gauge: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-amd-module-type@5.0.1:
    get-amd-module-type: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-port-please@3.1.2:
    get-port-please: private
  get-port@7.1.0:
    get-port: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@9.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  giget@2.0.0:
    giget: private
  git-raw-commits@4.0.0:
    git-raw-commits: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@11.0.2:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  globals@16.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@14.1.0:
    globby: private
  globjoin@0.1.4:
    globjoin: private
  gonzales-pe@4.3.0:
    gonzales-pe: private
  good-listener@1.2.2:
    good-listener: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graph-cycles@3.0.0:
    graph-cycles: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@7.0.0:
    gzip-size: private
  h3@1.15.3:
    h3: private
  happy-dom@17.4.6:
    happy-dom: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hey-listen@1.0.8:
    hey-listen: private
  homedir-polyfill@1.0.3:
    homedir-polyfill: private
  hookable@5.5.3:
    hookable: private
  hookified@1.8.2:
    hookified: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  html-minifier-terser@7.2.0:
    html-minifier-terser: private
  html-tags@3.3.1:
    html-tags: private
  htmlparser2@9.1.0:
    htmlparser2: private
  http-cache-semantics@4.1.1:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@4.0.1:
    http-proxy-agent: private
  http-shutdown@1.2.2:
    http-shutdown: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  httpxy@0.1.7:
    httpxy: private
  human-signals@8.0.1:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  idb@7.1.1:
    idb: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-size@0.5.5:
    image-size: private
  immutable@5.1.1:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-lazy@4.0.0:
    import-lazy: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  index-to-position@1.1.0:
    index-to-position: private
  infer-owner@1.0.4:
    infer-owner: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  internal/lint-configs/commitlint-config:
    '@vben/commitlint-config': private
  internal/lint-configs/stylelint-config:
    '@vben/stylelint-config': private
  internal/node-utils:
    '@vben/node-utils': private
  ioredis@5.6.1:
    ioredis: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-in-ci@1.0.0:
    is-in-ci: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-installed-globally@1.0.0:
    is-installed-globally: private
  is-interactive@2.0.0:
    is-interactive: private
  is-lambda@1.0.1:
    is-lambda: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-npm@6.0.0:
    is-npm: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-path-inside@4.0.0:
    is-path-inside: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-promise@4.0.0:
    is-promise: private
  is-reference@1.2.1:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@4.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-subdir@1.2.0:
    is-subdir: private
  is-symbol@1.1.1:
    is-symbol: private
  is-text-path@2.0.0:
    is-text-path: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-url-superb@4.0.0:
    is-url-superb: private
  is-url@1.2.4:
    is-url: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@3.14.1:
    is-what: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@2.2.0:
    is-wsl: private
  is64bit@2.0.0:
    is64bit: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  jackspeak@4.1.0:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jiti@2.4.2:
    jiti: private
  jju@1.4.0:
    jju: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonparse@1.3.1:
    jsonparse: private
  jsonpointer@5.0.1:
    jsonpointer: private
  junk@4.0.1:
    junk: private
  jwt-decode@4.0.0:
    jwt-decode: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@4.1.5:
    kleur: private
  klona@2.0.6:
    klona: private
  knitwork@1.2.0:
    knitwork: private
  known-css-properties@0.35.0:
    known-css-properties: private
  kolorist@1.8.0:
    kolorist: private
  kuler@2.0.0:
    kuler: private
  ky@1.8.1:
    ky: private
  lambda-local@2.2.0:
    lambda-local: private
  latest-version@9.0.0:
    latest-version: private
  lazystream@1.0.1:
    lazystream: private
  less@4.3.0:
    less: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listhen@1.9.0:
    listhen: private
  listr2@8.3.2:
    listr2: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@7.2.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash.set@4.3.2:
    lodash.set: private
  lodash.snakecase@4.1.1:
    lodash.snakecase: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.startcase@4.4.0:
    lodash.startcase: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: private
  lodash@4.17.21:
    lodash: private
  log-symbols@6.0.0:
    log-symbols: private
  log-update@6.1.0:
    log-update: private
  logform@2.7.0:
    logform: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@3.1.3:
    loupe: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@6.0.0:
    lru-cache: private
  lucide-vue-next@0.507.0(vue@3.5.13(typescript@5.8.3)):
    lucide-vue-next: private
  luxon@3.6.1:
    luxon: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  make-dir@2.1.0:
    make-dir: private
  make-fetch-happen@8.0.14:
    make-fetch-happen: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mathml-tag-names@2.1.3:
    mathml-tag-names: private
  mdn-data@2.21.0:
    mdn-data: private
  media-typer@1.1.0:
    media-typer: private
  meow@13.2.0:
    meow: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  merge-options@3.0.4:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micro-api-client@3.3.0:
    micro-api-client: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@4.0.7:
    mime: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass-collect@1.0.2:
    minipass-collect: private
  minipass-fetch@1.4.1:
    minipass-fetch: private
  minipass-flush@1.0.5:
    minipass-flush: private
  minipass-pipeline@1.2.4:
    minipass-pipeline: private
  minipass-sized@1.0.3:
    minipass-sized: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp@1.0.4:
    mkdirp: private
  mkdist@2.3.0(sass@1.87.0)(typescript@5.8.3)(vue-tsc@2.2.10(typescript@5.8.3))(vue@3.5.13(typescript@5.8.3)):
    mkdist: private
  mlly@1.7.4:
    mlly: private
  module-definition@5.0.1:
    module-definition: private
  mri@1.2.0:
    mri: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  multimatch@5.0.0:
    multimatch: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  nanopop@2.4.2:
    nanopop: private
  napi-postinstall@0.2.2:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  needle@3.3.1:
    needle: private
  negotiator@1.0.0:
    negotiator: private
  nested-error-stacks@2.1.1:
    nested-error-stacks: private
  netlify@13.3.5:
    netlify: private
  nitropack@2.11.11(@netlify/blobs@8.2.0)(encoding@0.1.13):
    nitropack: private
  no-case@3.0.4:
    no-case: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-cleanup@2.1.2:
    node-cleanup: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-fetch@2.7.0(encoding@0.1.13):
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-html-parser@5.4.2:
    node-html-parser: private
  node-mock-http@1.0.0:
    node-mock-http: private
  node-releases@2.0.19:
    node-releases: private
  node-source-walk@6.0.2:
    node-source-walk: private
  nopt@8.1.0:
    nopt: private
  normalize-package-data@6.0.2:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@6.0.0:
    npm-run-path: private
  npmlog@5.0.1:
    npmlog: private
  nprogress@0.2.0:
    nprogress: private
  nth-check@2.1.1:
    nth-check: private
  nypm@0.6.0:
    nypm: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  onetime@7.0.0:
    onetime: private
  open@8.4.2:
    open: private
  optionator@0.9.4:
    optionator: private
  ora@8.2.0:
    ora: private
  own-keys@1.0.1:
    own-keys: private
  p-event@5.0.1:
    p-event: private
  p-limit@4.0.0:
    p-limit: private
  p-locate@6.0.0:
    p-locate: private
  p-map@7.0.3:
    p-map: private
  p-timeout@5.1.0:
    p-timeout: private
  p-try@2.2.0:
    p-try: private
  p-wait-for@5.0.2:
    p-wait-for: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-json@10.0.1:
    package-json: private
  package-manager-detector@1.2.0:
    package-manager-detector: private
  packages/@core/base/design:
    '@vben-core/design': private
  packages/@core/base/icons:
    '@vben-core/icons': private
  packages/@core/base/shared:
    '@vben-core/shared': private
  packages/@core/base/typings:
    '@vben-core/typings': private
  packages/@core/composables:
    '@vben-core/composables': private
  packages/@core/preferences:
    '@vben-core/preferences': private
  packages/@core/ui-kit/form-ui:
    '@vben-core/form-ui': private
  packages/@core/ui-kit/layout-ui:
    '@vben-core/layout-ui': private
  packages/@core/ui-kit/menu-ui:
    '@vben-core/menu-ui': private
  packages/@core/ui-kit/popup-ui:
    '@vben-core/popup-ui': private
  packages/@core/ui-kit/shadcn-ui:
    '@vben-core/shadcn-ui': private
  packages/@core/ui-kit/tabs-ui:
    '@vben-core/tabs-ui': private
  packages/constants:
    '@vben/constants': private
  packages/effects/access:
    '@vben/access': private
  packages/effects/common-ui:
    '@vben/common-ui': private
  packages/effects/hooks:
    '@vben/hooks': private
  packages/effects/layouts:
    '@vben/layouts': private
  packages/effects/plugins:
    '@vben/plugins': private
  packages/effects/request:
    '@vben/request': private
  packages/icons:
    '@vben/icons': private
  packages/locales:
    '@vben/locales': private
  packages/preferences:
    '@vben/preferences': private
  packages/stores:
    '@vben/stores': private
  packages/styles:
    '@vben/styles': private
  packages/types:
    '@vben/types': private
  packages/utils:
    '@vben/utils': private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-json@5.2.0:
    parse-json: private
  parse-ms@4.0.0:
    parse-ms: private
  parse-node-version@1.0.1:
    parse-node-version: private
  parse-passwd@1.0.0:
    parse-passwd: private
  parse-statements@1.0.11:
    parse-statements: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@5.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  pathval@2.0.0:
    pathval: private
  pend@1.2.0:
    pend: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pify@2.3.0:
    pify: private
  pinia-plugin-persistedstate@4.2.0(magicast@0.3.5)(pinia@3.0.2(typescript@5.8.3)(vue@3.5.13(typescript@5.8.3))):
    pinia-plugin-persistedstate: private
  pinia@3.0.2(typescript@5.8.3)(vue@3.5.13(typescript@5.8.3)):
    pinia: private
  pirates@4.0.7:
    pirates: private
  pkce-challenge@5.0.0:
    pkce-challenge: private
  pkg-types@2.1.0:
    pkg-types: private
  please-upgrade-node@3.2.0:
    please-upgrade-node: private
  pluralize@8.0.0:
    pluralize: private
  pngjs@5.0.0:
    pngjs: private
  popmotion@11.0.5:
    popmotion: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-antd-fixes@0.2.0(postcss@8.5.3):
    postcss-antd-fixes: public
  postcss-attribute-case-insensitive@7.0.1(postcss@8.5.3):
    postcss-attribute-case-insensitive: public
  postcss-calc@10.1.1(postcss@8.5.3):
    postcss-calc: public
  postcss-clamp@4.1.0(postcss@8.5.3):
    postcss-clamp: public
  postcss-color-functional-notation@7.0.9(postcss@8.5.3):
    postcss-color-functional-notation: public
  postcss-color-hex-alpha@10.0.0(postcss@8.5.3):
    postcss-color-hex-alpha: public
  postcss-color-rebeccapurple@10.0.0(postcss@8.5.3):
    postcss-color-rebeccapurple: public
  postcss-colormin@7.0.2(postcss@8.5.3):
    postcss-colormin: public
  postcss-convert-values@7.0.4(postcss@8.5.3):
    postcss-convert-values: public
  postcss-custom-media@11.0.5(postcss@8.5.3):
    postcss-custom-media: public
  postcss-custom-properties@14.0.4(postcss@8.5.3):
    postcss-custom-properties: public
  postcss-custom-selectors@8.0.4(postcss@8.5.3):
    postcss-custom-selectors: public
  postcss-dir-pseudo-class@9.0.1(postcss@8.5.3):
    postcss-dir-pseudo-class: public
  postcss-discard-comments@7.0.3(postcss@8.5.3):
    postcss-discard-comments: public
  postcss-discard-duplicates@7.0.1(postcss@8.5.3):
    postcss-discard-duplicates: public
  postcss-discard-empty@7.0.0(postcss@8.5.3):
    postcss-discard-empty: public
  postcss-discard-overridden@7.0.0(postcss@8.5.3):
    postcss-discard-overridden: public
  postcss-double-position-gradients@6.0.1(postcss@8.5.3):
    postcss-double-position-gradients: public
  postcss-focus-visible@10.0.1(postcss@8.5.3):
    postcss-focus-visible: public
  postcss-focus-within@9.0.1(postcss@8.5.3):
    postcss-focus-within: public
  postcss-font-variant@5.0.0(postcss@8.5.3):
    postcss-font-variant: public
  postcss-gap-properties@6.0.0(postcss@8.5.3):
    postcss-gap-properties: public
  postcss-html@1.8.0:
    postcss-html: public
  postcss-image-set-function@7.0.0(postcss@8.5.3):
    postcss-image-set-function: public
  postcss-import@16.1.0(postcss@8.5.3):
    postcss-import: public
  postcss-js@4.0.1(postcss@8.5.3):
    postcss-js: public
  postcss-lab-function@7.0.9(postcss@8.5.3):
    postcss-lab-function: public
  postcss-load-config@4.0.2(postcss@8.5.3):
    postcss-load-config: public
  postcss-logical@8.1.0(postcss@8.5.3):
    postcss-logical: public
  postcss-media-query-parser@0.2.3:
    postcss-media-query-parser: public
  postcss-merge-longhand@7.0.4(postcss@8.5.3):
    postcss-merge-longhand: public
  postcss-merge-rules@7.0.4(postcss@8.5.3):
    postcss-merge-rules: public
  postcss-minify-font-values@7.0.0(postcss@8.5.3):
    postcss-minify-font-values: public
  postcss-minify-gradients@7.0.0(postcss@8.5.3):
    postcss-minify-gradients: public
  postcss-minify-params@7.0.2(postcss@8.5.3):
    postcss-minify-params: public
  postcss-minify-selectors@7.0.4(postcss@8.5.3):
    postcss-minify-selectors: public
  postcss-nested@5.0.6(postcss@8.5.3):
    postcss-nested: public
  postcss-nesting@13.0.1(postcss@8.5.3):
    postcss-nesting: public
  postcss-normalize-charset@7.0.0(postcss@8.5.3):
    postcss-normalize-charset: public
  postcss-normalize-display-values@7.0.0(postcss@8.5.3):
    postcss-normalize-display-values: public
  postcss-normalize-positions@7.0.0(postcss@8.5.3):
    postcss-normalize-positions: public
  postcss-normalize-repeat-style@7.0.0(postcss@8.5.3):
    postcss-normalize-repeat-style: public
  postcss-normalize-string@7.0.0(postcss@8.5.3):
    postcss-normalize-string: public
  postcss-normalize-timing-functions@7.0.0(postcss@8.5.3):
    postcss-normalize-timing-functions: public
  postcss-normalize-unicode@7.0.2(postcss@8.5.3):
    postcss-normalize-unicode: public
  postcss-normalize-url@7.0.0(postcss@8.5.3):
    postcss-normalize-url: public
  postcss-normalize-whitespace@7.0.0(postcss@8.5.3):
    postcss-normalize-whitespace: public
  postcss-opacity-percentage@3.0.0(postcss@8.5.3):
    postcss-opacity-percentage: public
  postcss-ordered-values@7.0.1(postcss@8.5.3):
    postcss-ordered-values: public
  postcss-overflow-shorthand@6.0.0(postcss@8.5.3):
    postcss-overflow-shorthand: public
  postcss-page-break@3.0.4(postcss@8.5.3):
    postcss-page-break: public
  postcss-place@10.0.0(postcss@8.5.3):
    postcss-place: public
  postcss-preset-env@10.1.6(postcss@8.5.3):
    postcss-preset-env: public
  postcss-pseudo-class-any-link@10.0.1(postcss@8.5.3):
    postcss-pseudo-class-any-link: public
  postcss-reduce-initial@7.0.2(postcss@8.5.3):
    postcss-reduce-initial: public
  postcss-reduce-transforms@7.0.0(postcss@8.5.3):
    postcss-reduce-transforms: public
  postcss-replace-overflow-wrap@4.0.0(postcss@8.5.3):
    postcss-replace-overflow-wrap: public
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: public
  postcss-safe-parser@6.0.0(postcss@8.5.3):
    postcss-safe-parser: public
  postcss-scss@4.0.9(postcss@8.5.3):
    postcss-scss: public
  postcss-selector-not@8.0.1(postcss@8.5.3):
    postcss-selector-not: public
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: public
  postcss-sorting@9.1.0(postcss@8.5.3):
    postcss-sorting: public
  postcss-svgo@7.0.1(postcss@8.5.3):
    postcss-svgo: public
  postcss-unique-selectors@7.0.3(postcss@8.5.3):
    postcss-unique-selectors: public
  postcss-value-parser@4.2.0:
    postcss-value-parser: public
  postcss-values-parser@6.0.2(postcss@8.5.3):
    postcss-values-parser: public
  postcss@8.5.3:
    postcss: public
  precinct@11.0.5:
    precinct: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prettier-plugin-tailwindcss@0.6.11(prettier@3.5.3):
    prettier-plugin-tailwindcss: public
  prettier@3.5.3:
    prettier: public
  pretty-bytes@6.1.1:
    pretty-bytes: private
  pretty-ms@9.2.0:
    pretty-ms: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  promise-inflight@1.0.1:
    promise-inflight: private
  promise-retry@2.0.1:
    promise-retry: private
  proto-list@1.2.4:
    proto-list: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  prr@1.0.1:
    prr: private
  publint@0.3.12:
    publint: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  pupa@3.1.0:
    pupa: private
  qrcode@1.5.4:
    qrcode: private
  qs@6.14.0:
    qs: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quote-unquote@1.0.0:
    quote-unquote: private
  radix-vue@1.9.17(vue@3.5.13(typescript@5.8.3)):
    radix-vue: private
  radix3@1.1.2:
    radix3: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  rc9@2.1.2:
    rc9: private
  rc@1.2.8:
    rc: private
  read-cache@1.0.0:
    read-cache: private
  read-package-up@11.0.0:
    read-package-up: private
  read-pkg@9.0.1:
    read-pkg: private
  read-yaml-file@2.1.0:
    read-yaml-file: private
  readable-stream@4.7.0:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  refa@0.12.1:
    refa: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regenerator-transform@0.15.2:
    regenerator-transform: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  registry-auth-token@5.1.0:
    registry-auth-token: private
  registry-url@6.0.1:
    registry-url: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  relateurl@0.2.7:
    relateurl: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-main-filename@2.0.0:
    require-main-filename: private
  require-package-name@2.0.1:
    require-package-name: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-dir@1.0.1:
    resolve-dir: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  retry@0.12.0:
    retry: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup-plugin-dts@6.2.1(rollup@4.40.1)(typescript@5.8.3):
    rollup-plugin-dts: private
  rollup-plugin-visualizer@5.14.0(rollup@4.40.1):
    rollup-plugin-visualizer: private
  rollup@4.40.1:
    rollup: private
  rotated-array-set@3.0.0:
    rotated-array-set: private
  router@2.2.0:
    router: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  sade@1.8.1:
    sade: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass@1.87.0:
    sass: private
  sax@1.4.1:
    sax: private
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: private
  scslre@0.3.0:
    scslre: private
  scule@1.3.0:
    scule: private
  secure-ls@2.0.0:
    secure-ls: private
  select@1.1.2:
    select: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.7.1:
    semver: private
  send@1.2.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-placeholder@2.0.2:
    serve-placeholder: private
  serve-static@2.2.0:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallow-equal@1.2.1:
    shallow-equal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  short-tree@3.0.0:
    short-tree: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sirv@3.0.1:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  smob@1.5.0:
    smob: private
  socks-proxy-agent@5.0.1:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  sortablejs@1.15.6:
    sortablejs: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  spawndamnit@3.0.1:
    spawndamnit: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  speakingurl@14.0.1:
    speakingurl: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssri@8.0.1:
    ssri: private
  stable-hash@0.0.5:
    stable-hash: private
  stack-trace@0.0.10:
    stack-trace: private
  stackback@0.0.2:
    stackback: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@2.0.1:
    statuses: private
  std-env@3.9.0:
    std-env: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  streamx@2.22.0:
    streamx: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-final-newline@4.0.0:
    strip-final-newline: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@3.0.0:
    strip-literal: private
  stubborn-fs@1.2.5:
    stubborn-fs: private
  style-search@0.1.0:
    style-search: private
  style-value-types@5.1.2:
    style-value-types: private
  stylehacks@7.0.4(postcss@8.5.3):
    stylehacks: private
  stylelint-config-html@1.1.0(postcss-html@1.8.0)(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-config-html: private
  stylelint-config-recess-order@6.0.0(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-config-recess-order: private
  stylelint-config-recommended-scss@14.1.0(postcss@8.5.3)(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-config-recommended-scss: private
  stylelint-config-recommended-vue@1.6.0(postcss-html@1.8.0)(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-config-recommended-vue: private
  stylelint-config-recommended@16.0.0(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-config-recommended: private
  stylelint-config-standard@38.0.0(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-config-standard: private
  stylelint-order@7.0.0(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-order: private
  stylelint-prettier@5.0.3(prettier@3.5.3)(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-prettier: private
  stylelint-scss@6.11.1(stylelint@16.19.1(typescript@5.8.3)):
    stylelint-scss: private
  stylelint@16.19.1(typescript@5.8.3):
    stylelint: public
  stylis@4.3.6:
    stylis: private
  sucrase@3.35.0:
    sucrase: private
  superjson@2.2.2:
    superjson: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  sver@1.8.4:
    sver: private
  svg-tags@1.0.0:
    svg-tags: private
  svgo@3.3.2:
    svgo: private
  synckit@0.10.3:
    synckit: private
  system-architecture@0.1.0:
    system-architecture: private
  tabbable@6.2.0:
    tabbable: private
  table@6.9.0:
    table: private
  tailwind-merge@2.6.0:
    tailwind-merge: private
  tailwindcss-animate@1.0.7(tailwindcss@3.4.17):
    tailwindcss-animate: private
  tapable@2.2.1:
    tapable: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@7.4.3:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  tempy@0.6.0:
    tempy: private
  terser@5.39.0:
    terser: private
  text-decoder@1.2.3:
    text-decoder: private
  text-extensions@2.4.0:
    text-extensions: private
  text-hex@1.0.0:
    text-hex: private
  theme-colors@0.1.0:
    theme-colors: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  through@2.3.8:
    through: private
  tiny-emitter@2.1.0:
    tiny-emitter: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.13:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  tippy.js@6.3.7:
    tippy.js: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toml@3.0.0:
    toml: private
  totalist@3.0.1:
    totalist: private
  tr46@1.0.1:
    tr46: private
  triple-beam@1.4.1:
    triple-beam: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.3.0:
    tslib: private
  tsutils@3.21.0(typescript@5.8.3):
    tsutils: private
  turbo-darwin-64@2.5.2:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.2:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.2:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.2:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.2:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.2:
    turbo-windows-arm64: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.40.1:
    type-fest: private
  type-is@2.0.1:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  ufo@1.6.1:
    ufo: private
  ultrahtml@1.6.0:
    ultrahtml: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  uncrypto@0.1.3:
    uncrypto: private
  unctx@2.4.1:
    unctx: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.2:
    undici: private
  unenv@2.0.0-rc.15:
    unenv: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  unimport@5.0.0:
    unimport: private
  unique-filename@1.1.1:
    unique-filename: private
  unique-slug@2.0.2:
    unique-slug: private
  unique-string@2.0.0:
    unique-string: private
  universalify@2.0.1:
    universalify: private
  unixify@1.0.0:
    unixify: private
  unpipe@1.0.0:
    unpipe: private
  unplugin-utils@0.2.4:
    unplugin-utils: private
  unplugin@1.16.1:
    unplugin: private
  unrs-resolver@1.7.2:
    unrs-resolver: private
  unstorage@1.16.0(@netlify/blobs@8.2.0)(db0@0.3.2)(ioredis@5.6.1):
    unstorage: private
  untun@0.1.3:
    untun: private
  untyped@2.0.0:
    untyped: private
  unwasm@0.3.9:
    unwasm: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  update-notifier@7.3.1:
    update-notifier: private
  uqr@0.1.2:
    uqr: private
  uri-js@4.4.1:
    uri-js: private
  urlpattern-polyfill@8.0.2:
    urlpattern-polyfill: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@11.1.0:
    uuid: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vary@1.1.2:
    vary: private
  vee-validate@4.15.0(vue@3.5.13(typescript@5.8.3)):
    vee-validate: private
  vite-hot-client@2.0.4(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    vite-hot-client: private
  vite-node@3.1.2(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1):
    vite-node: private
  vite-plugin-compression@0.5.1(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    vite-plugin-compression: private
  vite-plugin-dts@4.5.3(@types/node@22.15.3)(rollup@4.40.1)(typescript@5.8.3)(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    vite-plugin-dts: private
  vite-plugin-html@3.2.2(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    vite-plugin-html: private
  vite-plugin-inspect@0.8.9(rollup@4.40.1)(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    vite-plugin-inspect: private
  vite-plugin-lazy-import@1.0.7:
    vite-plugin-lazy-import: private
  vite-plugin-pwa@1.0.0(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1))(workbox-build@7.3.0)(workbox-window@7.3.0):
    vite-plugin-pwa: private
  vite-plugin-vue-devtools@7.7.6(rollup@4.40.1)(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1))(vue@3.5.13(typescript@5.8.3)):
    vite-plugin-vue-devtools: private
  vite-plugin-vue-inspector@5.3.1(vite@6.3.4(@types/node@22.15.3)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1)):
    vite-plugin-vue-inspector: private
  vitest@3.1.2(@types/node@22.15.3)(happy-dom@17.4.6)(jiti@2.4.2)(less@4.3.0)(sass@1.87.0)(terser@5.39.0)(yaml@2.7.1):
    vitest: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.14.10(vue@3.5.13(typescript@5.8.3)):
    vue-demi: private
  vue-eslint-parser@10.1.3(eslint@9.26.0(jiti@2.4.2)):
    vue-eslint-parser: private
  vue-i18n@11.1.3(vue@3.5.13(typescript@5.8.3)):
    vue-i18n: private
  vue-json-viewer@3.0.4(vue@3.5.13(typescript@5.8.3)):
    vue-json-viewer: private
  vue-router@4.5.1(vue@3.5.13(typescript@5.8.3)):
    vue-router: private
  vue-tippy@6.7.0(vue@3.5.13(typescript@5.8.3)):
    vue-tippy: private
  vue-types@3.0.2(vue@3.5.13(typescript@5.8.3)):
    vue-types: private
  vxe-pc-ui@4.5.35(vue@3.5.13(typescript@5.8.3)):
    vxe-pc-ui: private
  vxe-table@4.13.16(vue@3.5.13(typescript@5.8.3)):
    vxe-table: private
  warning@4.0.3:
    warning: private
  watermark-js-plus@1.6.0:
    watermark-js-plus: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@7.1.0:
    whatwg-url: private
  when-exit@2.1.4:
    when-exit: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  wide-align@1.1.5:
    wide-align: private
  widest-line@5.0.0:
    widest-line: private
  winston-transport@4.9.0:
    winston-transport: private
  winston@3.17.0:
    winston: private
  word-wrap@1.2.5:
    word-wrap: private
  workbox-background-sync@7.3.0:
    workbox-background-sync: private
  workbox-broadcast-update@7.3.0:
    workbox-broadcast-update: private
  workbox-build@7.3.0:
    workbox-build: private
  workbox-cacheable-response@7.3.0:
    workbox-cacheable-response: private
  workbox-core@7.3.0:
    workbox-core: private
  workbox-expiration@7.3.0:
    workbox-expiration: private
  workbox-google-analytics@7.3.0:
    workbox-google-analytics: private
  workbox-navigation-preload@7.3.0:
    workbox-navigation-preload: private
  workbox-precaching@7.3.0:
    workbox-precaching: private
  workbox-range-requests@7.3.0:
    workbox-range-requests: private
  workbox-recipes@7.3.0:
    workbox-recipes: private
  workbox-routing@7.3.0:
    workbox-routing: private
  workbox-strategies@7.3.0:
    workbox-strategies: private
  workbox-streams@7.3.0:
    workbox-streams: private
  workbox-sw@7.3.0:
    workbox-sw: private
  workbox-window@7.3.0:
    workbox-window: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  xdg-basedir@5.1.0:
    xdg-basedir: private
  xe-utils@3.7.4:
    xe-utils: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  y18n@4.0.3:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.7.1:
    yaml: private
  yargs-parser@18.1.3:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yocto-queue@1.2.1:
    yocto-queue: private
  yoctocolors@2.1.1:
    yoctocolors: private
  youch-core@0.3.2:
    youch-core: private
  youch@4.1.0-beta.7:
    youch: private
  zip-stream@6.0.1:
    zip-stream: private
  zod-defaults@0.1.3(zod@3.24.3):
    zod-defaults: private
  zod-to-json-schema@3.24.5(zod@3.24.3):
    zod-to-json-schema: private
  zod@3.24.3:
    zod: private
  zrender@5.6.1:
    zrender: private
  zx@8.5.3:
    zx: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Mon, 23 Jun 2025 03:39:46 GMT
publicHoistPattern:
  - lefthook
  - eslint
  - prettier
  - prettier-plugin-tailwindcss
  - stylelint
  - '*postcss*'
  - '@commitlint/*'
  - czg
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@ast-grep/napi-darwin-x64@0.37.0'
  - '@ast-grep/napi-linux-arm64-gnu@0.37.0'
  - '@ast-grep/napi-linux-arm64-musl@0.37.0'
  - '@ast-grep/napi-linux-x64-gnu@0.37.0'
  - '@ast-grep/napi-linux-x64-musl@0.37.0'
  - '@ast-grep/napi-win32-arm64-msvc@0.37.0'
  - '@ast-grep/napi-win32-ia32-msvc@0.37.0'
  - '@ast-grep/napi-win32-x64-msvc@0.37.0'
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.25.3'
  - '@esbuild/android-arm64@0.25.3'
  - '@esbuild/android-arm@0.25.3'
  - '@esbuild/android-x64@0.25.3'
  - '@esbuild/darwin-x64@0.25.3'
  - '@esbuild/freebsd-arm64@0.25.3'
  - '@esbuild/freebsd-x64@0.25.3'
  - '@esbuild/linux-arm64@0.25.3'
  - '@esbuild/linux-arm@0.25.3'
  - '@esbuild/linux-ia32@0.25.3'
  - '@esbuild/linux-loong64@0.25.3'
  - '@esbuild/linux-mips64el@0.25.3'
  - '@esbuild/linux-ppc64@0.25.3'
  - '@esbuild/linux-riscv64@0.25.3'
  - '@esbuild/linux-s390x@0.25.3'
  - '@esbuild/linux-x64@0.25.3'
  - '@esbuild/netbsd-arm64@0.25.3'
  - '@esbuild/netbsd-x64@0.25.3'
  - '@esbuild/openbsd-arm64@0.25.3'
  - '@esbuild/openbsd-x64@0.25.3'
  - '@esbuild/sunos-x64@0.25.3'
  - '@esbuild/win32-arm64@0.25.3'
  - '@esbuild/win32-ia32@0.25.3'
  - '@esbuild/win32-x64@0.25.3'
  - '@napi-rs/wasm-runtime@0.2.9'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.40.1'
  - '@rollup/rollup-android-arm64@4.40.1'
  - '@rollup/rollup-darwin-x64@4.40.1'
  - '@rollup/rollup-freebsd-arm64@4.40.1'
  - '@rollup/rollup-freebsd-x64@4.40.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.1'
  - '@rollup/rollup-linux-arm64-gnu@4.40.1'
  - '@rollup/rollup-linux-arm64-musl@4.40.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.1'
  - '@rollup/rollup-linux-riscv64-musl@4.40.1'
  - '@rollup/rollup-linux-s390x-gnu@4.40.1'
  - '@rollup/rollup-linux-x64-gnu@4.40.1'
  - '@rollup/rollup-linux-x64-musl@4.40.1'
  - '@rollup/rollup-win32-arm64-msvc@4.40.1'
  - '@rollup/rollup-win32-ia32-msvc@4.40.1'
  - '@rollup/rollup-win32-x64-msvc@4.40.1'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-x64@1.7.2'
  - '@unrs/resolver-binding-freebsd-x64@1.7.2'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.2'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.2'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.2'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.2'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.2'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.2'
  - '@unrs/resolver-binding-win32-x64-msvc@1.7.2'
  - lefthook-darwin-x64@1.11.12
  - lefthook-freebsd-arm64@1.11.12
  - lefthook-freebsd-x64@1.11.12
  - lefthook-linux-arm64@1.11.12
  - lefthook-linux-x64@1.11.12
  - lefthook-openbsd-arm64@1.11.12
  - lefthook-openbsd-x64@1.11.12
  - lefthook-windows-arm64@1.11.12
  - lefthook-windows-x64@1.11.12
  - turbo-darwin-64@2.5.2
  - turbo-linux-64@2.5.2
  - turbo-linux-arm64@2.5.2
  - turbo-windows-64@2.5.2
  - turbo-windows-arm64@2.5.2
storeDir: /Volumes/Data/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
