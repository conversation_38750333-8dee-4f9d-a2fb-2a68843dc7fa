import {
  button_default
} from "./chunk-KYSURFPK.js";
import "./chunk-WSKNJIPT.js";
import "./chunk-SZAZ35M7.js";
import "./chunk-GUBNAPS4.js";
import "./chunk-7BLNBWQR.js";
import {
  dynamicApp
} from "./chunk-XD6QLL5Q.js";
import {
  VxeUI
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-button/index.js
var vxe_button_default = button_default2;
export {
  Button,
  VxeButton,
  vxe_button_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-button_index__js.js.map
