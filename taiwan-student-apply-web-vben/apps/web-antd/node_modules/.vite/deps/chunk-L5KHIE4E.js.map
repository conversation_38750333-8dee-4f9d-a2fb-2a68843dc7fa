{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/src/select.js"], "sourcesContent": ["import { defineComponent, h, Teleport, ref, inject, computed, provide, onUnmounted, reactive, nextTick, watch, onMounted } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { getConfig, getIcon, getI18n, globalEvents, GLOBAL_EVENT_KEYS, createEvent, useSize, renderEmptyElement } from '../../ui';\nimport { getEventTargetNode, getAbsolutePos } from '../../ui/src/dom';\nimport { getLastZIndex, nextZIndex, getFuncText } from '../../ui/src/utils';\nimport { getSlotVNs } from '../../ui/src/vn';\nimport VxeInputComponent from '../../input/src/input';\nfunction isOptionVisible(option) {\n    return option.visible !== false;\n}\nfunction getOptUniqueId() {\n    return XEUtils.uniqueId('opt_');\n}\nexport default defineComponent({\n    name: 'VxeSelect',\n    props: {\n        modelValue: [String, Number, Boolean, Array],\n        defaultConfig: Object,\n        clearable: Boolean,\n        placeholder: String,\n        readonly: {\n            type: Boolean,\n            default: null\n        },\n        loading: Boolean,\n        disabled: {\n            type: Boolean,\n            default: null\n        },\n        multiple: Boolean,\n        multiCharOverflow: {\n            type: [Number, String],\n            default: () => getConfig().select.multiCharOverflow\n        },\n        prefixIcon: String,\n        allowCreate: {\n            type: Boolean,\n            default: () => getConfig().select.allowCreate\n        },\n        placement: String,\n        options: Array,\n        optionProps: Object,\n        optionGroups: Array,\n        optionGroupProps: Object,\n        optionConfig: Object,\n        className: [String, Function],\n        popupClassName: [String, Function],\n        max: {\n            type: [String, Number],\n            default: null\n        },\n        size: {\n            type: String,\n            default: () => getConfig().select.size || getConfig().size\n        },\n        filterable: Boolean,\n        filterMethod: Function,\n        remote: Boolean,\n        // 已废弃，被 remote-config.queryMethod 替换\n        remoteMethod: Function,\n        remoteConfig: Object,\n        emptyText: String,\n        transfer: {\n            type: Boolean,\n            default: null\n        },\n        virtualYConfig: Object,\n        scrollY: Object,\n        // 已废弃，被 option-config.keyField 替换\n        optionId: {\n            type: String,\n            default: () => getConfig().select.optionId\n        },\n        // 已废弃，被 option-config.useKey 替换\n        optionKey: Boolean\n    },\n    emits: [\n        'update:modelValue',\n        'change',\n        'clear',\n        'blur',\n        'focus',\n        'click',\n        'scroll',\n        'visible-change'\n    ],\n    setup(props, context) {\n        const { slots, emit } = context;\n        const $xeModal = inject('$xeModal', null);\n        const $xeDrawer = inject('$xeDrawer', null);\n        const $xeTable = inject('$xeTable', null);\n        const $xeForm = inject('$xeForm', null);\n        const formItemInfo = inject('xeFormItemInfo', null);\n        const xID = XEUtils.uniqueId();\n        const refElem = ref();\n        const refInput = ref();\n        const refInpSearch = ref();\n        const refVirtualWrapper = ref();\n        const refOptionPanel = ref();\n        const refVirtualBody = ref();\n        const { computeSize } = useSize(props);\n        const reactData = reactive({\n            initialized: false,\n            scrollYLoad: false,\n            bodyHeight: 0,\n            topSpaceHeight: 0,\n            optList: [],\n            afterVisibleList: [],\n            staticOptions: [],\n            reactFlag: 0,\n            currentOption: null,\n            searchValue: '',\n            searchLoading: false,\n            panelIndex: 0,\n            panelStyle: {},\n            panelPlacement: null,\n            triggerFocusPanel: false,\n            visiblePanel: false,\n            isAniVisible: false,\n            isActivated: false\n        });\n        const internalData = {\n            synchData: [],\n            fullData: [],\n            optAddMaps: {},\n            optGroupKeyMaps: {},\n            optFullValMaps: {},\n            remoteValMaps: {},\n            lastScrollLeft: 0,\n            lastScrollTop: 0,\n            scrollYStore: {\n                startIndex: 0,\n                endIndex: 0,\n                visibleSize: 0,\n                offsetSize: 0,\n                rowHeight: 0\n            },\n            lastScrollTime: 0,\n            hpTimeout: undefined\n        };\n        const refMaps = {\n            refElem\n        };\n        const $xeSelect = {\n            xID,\n            props,\n            context,\n            reactData,\n            internalData,\n            getRefMaps: () => refMaps\n        };\n        const computeFormReadonly = computed(() => {\n            const { readonly } = props;\n            if (readonly === null) {\n                if ($xeForm) {\n                    return $xeForm.props.readonly;\n                }\n                return false;\n            }\n            return readonly;\n        });\n        const computeIsDisabled = computed(() => {\n            const { disabled } = props;\n            if (disabled === null) {\n                if ($xeForm) {\n                    return $xeForm.props.disabled;\n                }\n                return false;\n            }\n            return disabled;\n        });\n        const computeBtnTransfer = computed(() => {\n            const { transfer } = props;\n            if (transfer === null) {\n                const globalTransfer = getConfig().select.transfer;\n                if (XEUtils.isBoolean(globalTransfer)) {\n                    return globalTransfer;\n                }\n                if ($xeTable || $xeModal || $xeDrawer || $xeForm) {\n                    return true;\n                }\n            }\n            return transfer;\n        });\n        const computeInpPlaceholder = computed(() => {\n            const { placeholder } = props;\n            if (placeholder) {\n                return getFuncText(placeholder);\n            }\n            const globalPlaceholder = getConfig().select.placeholder;\n            if (globalPlaceholder) {\n                return getFuncText(globalPlaceholder);\n            }\n            return getI18n('vxe.base.pleaseSelect');\n        });\n        const computeDefaultOpts = computed(() => {\n            return Object.assign({}, props.defaultConfig);\n        });\n        const computePropsOpts = computed(() => {\n            return Object.assign({}, props.optionProps);\n        });\n        const computeGroupPropsOpts = computed(() => {\n            return Object.assign({}, props.optionGroupProps);\n        });\n        const computeLabelField = computed(() => {\n            const propsOpts = computePropsOpts.value;\n            return propsOpts.label || 'label';\n        });\n        const computeValueField = computed(() => {\n            const propsOpts = computePropsOpts.value;\n            return propsOpts.value || 'value';\n        });\n        const computeGroupLabelField = computed(() => {\n            const groupPropsOpts = computeGroupPropsOpts.value;\n            return groupPropsOpts.label || 'label';\n        });\n        const computeGroupOptionsField = computed(() => {\n            const groupPropsOpts = computeGroupPropsOpts.value;\n            return groupPropsOpts.options || 'options';\n        });\n        const computeIsMaximize = computed(() => {\n            const { modelValue, multiple, max } = props;\n            if (multiple && max) {\n                return (XEUtils.isArray(modelValue) ? modelValue.length : (XEUtils.eqNull(modelValue) ? 0 : 1)) >= XEUtils.toNumber(max);\n            }\n            return false;\n        });\n        const computeSYOpts = computed(() => {\n            return Object.assign({}, getConfig().select.virtualYConfig || getConfig().select.scrollY, props.virtualYConfig || props.scrollY);\n        });\n        const computeRemoteOpts = computed(() => {\n            return Object.assign({}, getConfig().select.remoteConfig, props.remoteConfig);\n        });\n        const computeOptionOpts = computed(() => {\n            return Object.assign({}, getConfig().select.optionConfig, props.optionConfig);\n        });\n        const computeMultiMaxCharNum = computed(() => {\n            return XEUtils.toNumber(props.multiCharOverflow);\n        });\n        const computeSelectLabel = computed(() => {\n            const { modelValue, remote, multiple } = props;\n            const { reactFlag } = reactData;\n            const multiMaxCharNum = computeMultiMaxCharNum.value;\n            if (XEUtils.eqNull(modelValue)) {\n                return '';\n            }\n            const vals = XEUtils.isArray(modelValue) ? modelValue : [modelValue];\n            if (remote && reactFlag) {\n                return vals.map(val => getRemoteSelectLabel(val)).join(', ');\n            }\n            return vals.map((val) => {\n                const label = getSelectLabel(val);\n                if (multiple && multiMaxCharNum > 0 && label.length > multiMaxCharNum) {\n                    return `${label.substring(0, multiMaxCharNum)}...`;\n                }\n                return label;\n            }).join(', ');\n        });\n        const callSlot = (slotFunc, params) => {\n            if (slotFunc) {\n                if (XEUtils.isString(slotFunc)) {\n                    slotFunc = slots[slotFunc] || null;\n                }\n                if (XEUtils.isFunction(slotFunc)) {\n                    return getSlotVNs(slotFunc(params));\n                }\n            }\n            return [];\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $select: $xeSelect }, params));\n        };\n        const emitModel = (value) => {\n            emit('update:modelValue', value);\n        };\n        const getOptKey = () => {\n            const optionOpts = computeOptionOpts.value;\n            return optionOpts.keyField || props.optionId || '_X_OPTION_KEY';\n        };\n        const getOptId = (option) => {\n            const optid = option[getOptKey()];\n            return optid ? encodeURIComponent(optid) : '';\n        };\n        const getRemoteSelectLabel = (value) => {\n            const { remoteValMaps, optFullValMaps } = internalData;\n            const labelField = computeLabelField.value;\n            const remoteItem = remoteValMaps[value] || optFullValMaps[value];\n            const item = remoteItem ? remoteItem.item : null;\n            return XEUtils.toValueString(item ? item[labelField] : value);\n        };\n        const getSelectLabel = (value) => {\n            const { optFullValMaps } = internalData;\n            const labelField = computeLabelField.value;\n            const cacheItem = reactData.reactFlag ? optFullValMaps[value] : null;\n            return cacheItem ? cacheItem.item[labelField] : XEUtils.toValueString(value);\n        };\n        const cacheItemMap = (datas) => {\n            const groupOptionsField = computeGroupOptionsField.value;\n            const valueField = computeValueField.value;\n            const key = getOptKey();\n            const groupKeyMaps = {};\n            const fullKeyMaps = {};\n            const list = [];\n            const handleOptItem = (item) => {\n                list.push(item);\n                let optid = getOptId(item);\n                if (!optid) {\n                    optid = getOptUniqueId();\n                    item[key] = optid;\n                }\n                fullKeyMaps[item[valueField]] = {\n                    key: optid,\n                    item,\n                    _index: -1\n                };\n            };\n            datas.forEach((group) => {\n                handleOptItem(group);\n                if (group[groupOptionsField]) {\n                    groupKeyMaps[group[key]] = group;\n                    group[groupOptionsField].forEach(handleOptItem);\n                }\n            });\n            internalData.fullData = list;\n            internalData.optGroupKeyMaps = groupKeyMaps;\n            internalData.optFullValMaps = fullKeyMaps;\n            reactData.reactFlag++;\n            handleOption();\n        };\n        /**\n         * 处理选项，当选项被动态显示/隐藏时可能会用到\n         */\n        const handleOption = () => {\n            const { modelValue, filterable, filterMethod } = props;\n            const { searchValue } = reactData;\n            const { fullData, optFullValMaps } = internalData;\n            const labelField = computeLabelField.value;\n            const valueField = computeValueField.value;\n            const searchStr = `${searchValue || ''}`.toLowerCase();\n            let avList = [];\n            if (filterable && filterMethod) {\n                avList = fullData.filter(option => isOptionVisible(option) && filterMethod({ $select: $xeSelect, group: null, option, searchValue, value: modelValue }));\n            }\n            else if (filterable) {\n                avList = fullData.filter(option => isOptionVisible(option) && (!searchStr || `${option[labelField]}`.toLowerCase().indexOf(searchStr) > -1));\n            }\n            else {\n                avList = fullData.filter(isOptionVisible);\n            }\n            avList.forEach((item, index) => {\n                const cacheItem = optFullValMaps[item[valueField]];\n                if (cacheItem) {\n                    cacheItem._index = index;\n                }\n            });\n            reactData.afterVisibleList = avList;\n            return nextTick();\n        };\n        const setCurrentOption = (option) => {\n            if (option) {\n                reactData.currentOption = option;\n            }\n        };\n        const updateZIndex = () => {\n            if (reactData.panelIndex < getLastZIndex()) {\n                reactData.panelIndex = nextZIndex();\n            }\n        };\n        const updatePlacement = () => {\n            return nextTick().then(() => {\n                const { placement } = props;\n                const { panelIndex } = reactData;\n                const el = refElem.value;\n                const panelElem = refOptionPanel.value;\n                const btnTransfer = computeBtnTransfer.value;\n                if (panelElem && el) {\n                    const targetHeight = el.offsetHeight;\n                    const targetWidth = el.offsetWidth;\n                    const panelHeight = panelElem.offsetHeight;\n                    const panelWidth = panelElem.offsetWidth;\n                    const marginSize = 5;\n                    const panelStyle = {\n                        zIndex: panelIndex\n                    };\n                    const { boundingTop, boundingLeft, visibleHeight, visibleWidth } = getAbsolutePos(el);\n                    let panelPlacement = 'bottom';\n                    if (btnTransfer) {\n                        let left = boundingLeft;\n                        let top = boundingTop + targetHeight;\n                        if (placement === 'top') {\n                            panelPlacement = 'top';\n                            top = boundingTop - panelHeight;\n                        }\n                        else if (!placement) {\n                            // 如果下面不够放，则向上\n                            if (top + panelHeight + marginSize > visibleHeight) {\n                                panelPlacement = 'top';\n                                top = boundingTop - panelHeight;\n                            }\n                            // 如果上面不够放，则向下（优先）\n                            if (top < marginSize) {\n                                panelPlacement = 'bottom';\n                                top = boundingTop + targetHeight;\n                            }\n                        }\n                        // 如果溢出右边\n                        if (left + panelWidth + marginSize > visibleWidth) {\n                            left -= left + panelWidth + marginSize - visibleWidth;\n                        }\n                        // 如果溢出左边\n                        if (left < marginSize) {\n                            left = marginSize;\n                        }\n                        Object.assign(panelStyle, {\n                            left: `${left}px`,\n                            top: `${top}px`,\n                            minWidth: `${targetWidth}px`\n                        });\n                    }\n                    else {\n                        if (placement === 'top') {\n                            panelPlacement = 'top';\n                            panelStyle.bottom = `${targetHeight}px`;\n                        }\n                        else if (!placement) {\n                            // 如果下面不够放，则向上\n                            if (boundingTop + targetHeight + panelHeight > visibleHeight) {\n                                // 如果上面不够放，则向下（优先）\n                                if (boundingTop - targetHeight - panelHeight > marginSize) {\n                                    panelPlacement = 'top';\n                                    panelStyle.bottom = `${targetHeight}px`;\n                                }\n                            }\n                        }\n                    }\n                    reactData.panelStyle = panelStyle;\n                    reactData.panelPlacement = panelPlacement;\n                    return nextTick();\n                }\n            });\n        };\n        const showOptionPanel = () => {\n            const { loading, filterable, remote } = props;\n            const { fullData, hpTimeout } = internalData;\n            const isDisabled = computeIsDisabled.value;\n            const remoteOpts = computeRemoteOpts.value;\n            if (!loading && !isDisabled) {\n                if (hpTimeout) {\n                    clearTimeout(hpTimeout);\n                    internalData.hpTimeout = undefined;\n                }\n                if (!reactData.initialized) {\n                    reactData.initialized = true;\n                }\n                reactData.isActivated = true;\n                reactData.isAniVisible = true;\n                if (filterable) {\n                    if (remote && remoteOpts.enabled && remoteOpts.autoLoad && !fullData.length) {\n                        triggerSearchEvent();\n                    }\n                    else {\n                        handleOption();\n                        updateYData();\n                    }\n                }\n                setTimeout(() => {\n                    reactData.visiblePanel = true;\n                    handleFocusSearch();\n                    recalculate().then(() => refreshScroll());\n                }, 10);\n                setTimeout(() => {\n                    recalculate().then(() => refreshScroll());\n                }, 100);\n                updateZIndex();\n                updatePlacement();\n                dispatchEvent('visible-change', { visible: true }, null);\n            }\n        };\n        const hideOptionPanel = () => {\n            reactData.searchValue = '';\n            reactData.searchLoading = false;\n            reactData.visiblePanel = false;\n            internalData.hpTimeout = setTimeout(() => {\n                reactData.isAniVisible = false;\n            }, 350);\n            dispatchEvent('visible-change', { visible: false }, null);\n        };\n        const changeEvent = (evnt, selectValue) => {\n            emitModel(selectValue);\n            if (selectValue !== props.modelValue) {\n                dispatchEvent('change', { value: selectValue }, evnt);\n                // 自动更新校验状态\n                if ($xeForm && formItemInfo) {\n                    $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, selectValue);\n                }\n            }\n        };\n        const clearValueEvent = (evnt, selectValue) => {\n            internalData.remoteValMaps = {};\n            changeEvent(evnt, selectValue);\n            dispatchEvent('clear', { value: selectValue }, evnt);\n        };\n        const clearEvent = (params) => {\n            const { $event } = params;\n            clearValueEvent($event, null);\n            hideOptionPanel();\n        };\n        const changeOptionEvent = (evnt, option) => {\n            const { modelValue, multiple } = props;\n            const { remoteValMaps } = internalData;\n            const valueField = computeValueField.value;\n            const selectValue = option[valueField];\n            const remoteItem = remoteValMaps[selectValue];\n            if (!reactData.visiblePanel) {\n                return;\n            }\n            if (remoteItem) {\n                remoteItem.item = option;\n            }\n            else {\n                remoteValMaps[selectValue] = {\n                    key: getOptId(option),\n                    item: option,\n                    _index: -1\n                };\n            }\n            if (multiple) {\n                let multipleValue = [];\n                const selectVals = XEUtils.eqNull(modelValue) ? [] : (XEUtils.isArray(modelValue) ? modelValue : [modelValue]);\n                const index = XEUtils.findIndexOf(selectVals, val => val === selectValue);\n                if (index === -1) {\n                    multipleValue = selectVals.concat([selectValue]);\n                }\n                else {\n                    multipleValue = selectVals.filter((val) => val !== selectValue);\n                }\n                changeEvent(evnt, multipleValue);\n            }\n            else {\n                changeEvent(evnt, selectValue);\n                hideOptionPanel();\n            }\n            reactData.reactFlag++;\n        };\n        const handleGlobalMousewheelEvent = (evnt) => {\n            const { visiblePanel } = reactData;\n            const isDisabled = computeIsDisabled.value;\n            if (!isDisabled) {\n                if (visiblePanel) {\n                    const panelElem = refOptionPanel.value;\n                    if (getEventTargetNode(evnt, panelElem).flag) {\n                        updatePlacement();\n                    }\n                    else {\n                        hideOptionPanel();\n                    }\n                }\n            }\n        };\n        const handleGlobalMousedownEvent = (evnt) => {\n            const { visiblePanel } = reactData;\n            const isDisabled = computeIsDisabled.value;\n            if (!isDisabled) {\n                const el = refElem.value;\n                const panelElem = refOptionPanel.value;\n                reactData.isActivated = getEventTargetNode(evnt, el).flag || getEventTargetNode(evnt, panelElem).flag;\n                if (visiblePanel && !reactData.isActivated) {\n                    hideOptionPanel();\n                }\n            }\n        };\n        const validOffsetOption = (option) => {\n            const isDisabled = option.disabled;\n            const optid = getOptId(option);\n            if (!isDisabled && !hasOptGroupById(optid)) {\n                return true;\n            }\n            return false;\n        };\n        const findOffsetOption = (option, isDwArrow) => {\n            const { allowCreate } = props;\n            const { afterVisibleList, optList } = reactData;\n            const { optFullValMaps, optAddMaps } = internalData;\n            const valueField = computeValueField.value;\n            let fullList = afterVisibleList;\n            let offsetAddIndex = 0;\n            if (allowCreate && optList.length) {\n                const firstItem = optList[0];\n                const optid = getOptId(firstItem);\n                if (optAddMaps[optid]) {\n                    offsetAddIndex = 1;\n                    fullList = [optAddMaps[optid]].concat(fullList);\n                }\n            }\n            if (!option) {\n                if (isDwArrow) {\n                    for (let i = 0; i < fullList.length; i++) {\n                        const item = fullList[i];\n                        if (validOffsetOption(item)) {\n                            return item;\n                        }\n                    }\n                }\n                else {\n                    for (let len = fullList.length - 1; len >= 0; len--) {\n                        const item = fullList[len];\n                        if (validOffsetOption(item)) {\n                            return item;\n                        }\n                    }\n                }\n            }\n            let avIndex = 0;\n            const cacheItem = option ? optFullValMaps[option[valueField]] : null;\n            if (cacheItem) {\n                avIndex = cacheItem._index + offsetAddIndex;\n            }\n            if (avIndex > -1) {\n                if (isDwArrow) {\n                    for (let i = avIndex + 1; i <= fullList.length - 1; i++) {\n                        const item = fullList[i];\n                        if (validOffsetOption(item)) {\n                            return item;\n                        }\n                    }\n                }\n                else {\n                    if (avIndex > 0) {\n                        for (let len = avIndex - 1; len >= 0; len--) {\n                            const item = fullList[len];\n                            if (validOffsetOption(item)) {\n                                return item;\n                            }\n                        }\n                    }\n                }\n            }\n            return null;\n        };\n        const handleGlobalKeydownEvent = (evnt) => {\n            const { clearable } = props;\n            const { visiblePanel, currentOption } = reactData;\n            const isDisabled = computeIsDisabled.value;\n            if (!isDisabled) {\n                const isTab = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.TAB);\n                const isEnter = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ENTER);\n                const isEsc = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ESCAPE);\n                const isUpArrow = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ARROW_UP);\n                const isDwArrow = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ARROW_DOWN);\n                const isDel = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.DELETE);\n                const isSpacebar = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.SPACEBAR);\n                if (isTab) {\n                    reactData.isActivated = false;\n                }\n                if (visiblePanel) {\n                    if (isEsc || isTab) {\n                        hideOptionPanel();\n                    }\n                    else if (isEnter) {\n                        if (currentOption) {\n                            evnt.preventDefault();\n                            evnt.stopPropagation();\n                            changeOptionEvent(evnt, currentOption);\n                        }\n                    }\n                    else if (isUpArrow || isDwArrow) {\n                        evnt.preventDefault();\n                        let offsetOption = findOffsetOption(currentOption, isDwArrow);\n                        // 如果不匹配，默认最接近一个\n                        if (!offsetOption) {\n                            offsetOption = findOffsetOption(null, isDwArrow);\n                        }\n                        if (offsetOption) {\n                            setCurrentOption(offsetOption);\n                            handleScrollToOption(offsetOption, isDwArrow);\n                        }\n                    }\n                    else if (isSpacebar) {\n                        evnt.preventDefault();\n                    }\n                }\n                else if ((isUpArrow || isDwArrow || isEnter || isSpacebar) && reactData.isActivated) {\n                    evnt.preventDefault();\n                    showOptionPanel();\n                }\n                if (reactData.isActivated) {\n                    if (isDel && clearable) {\n                        clearValueEvent(evnt, null);\n                    }\n                }\n            }\n        };\n        const handleGlobalBlurEvent = () => {\n            hideOptionPanel();\n        };\n        const handleFocusSearch = () => {\n            if (props.filterable) {\n                nextTick(() => {\n                    const inpSearch = refInpSearch.value;\n                    if (inpSearch) {\n                        inpSearch.focus();\n                    }\n                });\n            }\n        };\n        const focusEvent = (evnt) => {\n            const isDisabled = computeIsDisabled.value;\n            if (!isDisabled) {\n                if (!reactData.visiblePanel) {\n                    reactData.triggerFocusPanel = true;\n                    showOptionPanel();\n                    setTimeout(() => {\n                        reactData.triggerFocusPanel = false;\n                    }, 500);\n                }\n            }\n            dispatchEvent('focus', {}, evnt);\n        };\n        const clickEvent = (evnt) => {\n            togglePanelEvent(evnt);\n            dispatchEvent('click', { triggerButton: false, visible: reactData.visiblePanel }, evnt);\n        };\n        const blurEvent = (evnt) => {\n            reactData.isActivated = false;\n            dispatchEvent('blur', {}, evnt);\n        };\n        const suffixClickEvent = (evnt) => {\n            togglePanelEvent(evnt);\n            dispatchEvent('click', { triggerButton: true, visible: reactData.visiblePanel }, evnt);\n        };\n        const modelSearchEvent = (value) => {\n            reactData.searchValue = value;\n        };\n        const focusSearchEvent = () => {\n            reactData.isActivated = true;\n        };\n        const handleSearchEvent = () => {\n            const { modelValue, remote, remoteMethod } = props;\n            const { searchValue } = reactData;\n            const remoteOpts = computeRemoteOpts.value;\n            const queryMethod = remoteOpts.queryMethod || remoteMethod;\n            if (remote && queryMethod && remoteOpts.enabled) {\n                reactData.searchLoading = true;\n                Promise.resolve(queryMethod({ $select: $xeSelect, searchValue, value: modelValue })).then(() => nextTick())\n                    .catch(() => nextTick())\n                    .finally(() => {\n                    reactData.searchLoading = false;\n                    handleOption();\n                    updateYData();\n                });\n            }\n            else {\n                handleOption();\n                updateYData();\n            }\n        };\n        const triggerSearchEvent = XEUtils.debounce(handleSearchEvent, 350, { trailing: true });\n        const togglePanelEvent = (params) => {\n            const { $event } = params;\n            $event.preventDefault();\n            if (reactData.triggerFocusPanel) {\n                reactData.triggerFocusPanel = false;\n            }\n            else {\n                if (reactData.visiblePanel) {\n                    hideOptionPanel();\n                }\n                else {\n                    showOptionPanel();\n                }\n            }\n        };\n        const checkOptionDisabled = (isSelected, option, group) => {\n            if (option.disabled) {\n                return true;\n            }\n            if (group && group.disabled) {\n                return true;\n            }\n            const isMaximize = computeIsMaximize.value;\n            if (isMaximize && !isSelected) {\n                return true;\n            }\n            return false;\n        };\n        const updateYSpace = () => {\n            const { scrollYLoad, afterVisibleList } = reactData;\n            const { scrollYStore } = internalData;\n            reactData.bodyHeight = scrollYLoad ? afterVisibleList.length * scrollYStore.rowHeight : 0;\n            reactData.topSpaceHeight = scrollYLoad ? Math.max(scrollYStore.startIndex * scrollYStore.rowHeight, 0) : 0;\n        };\n        const handleData = () => {\n            const { filterable, allowCreate } = props;\n            const { scrollYLoad, afterVisibleList, searchValue } = reactData;\n            const { optAddMaps, scrollYStore } = internalData;\n            const labelField = computeLabelField.value;\n            const valueField = computeValueField.value;\n            const restList = scrollYLoad ? afterVisibleList.slice(scrollYStore.startIndex, scrollYStore.endIndex) : afterVisibleList.slice(0);\n            if (filterable && allowCreate && searchValue) {\n                if (!restList.some(option => option[labelField] === searchValue)) {\n                    const addItem = optAddMaps[searchValue] || reactive({\n                        [getOptKey()]: searchValue,\n                        [labelField]: searchValue,\n                        [valueField]: searchValue\n                    });\n                    optAddMaps[searchValue] = addItem;\n                    restList.unshift(addItem);\n                }\n            }\n            reactData.optList = restList;\n            return nextTick();\n        };\n        const updateYData = () => {\n            handleData();\n            updateYSpace();\n        };\n        const computeScrollLoad = () => {\n            return nextTick().then(() => {\n                const { scrollYLoad } = reactData;\n                const { scrollYStore } = internalData;\n                const virtualBodyElem = refVirtualBody.value;\n                const sYOpts = computeSYOpts.value;\n                let rowHeight = 0;\n                let firstItemElem;\n                if (virtualBodyElem) {\n                    if (sYOpts.sItem) {\n                        firstItemElem = virtualBodyElem.querySelector(sYOpts.sItem);\n                    }\n                    if (!firstItemElem) {\n                        firstItemElem = virtualBodyElem.children[0];\n                    }\n                }\n                if (firstItemElem) {\n                    rowHeight = firstItemElem.offsetHeight;\n                }\n                rowHeight = Math.max(20, rowHeight);\n                scrollYStore.rowHeight = rowHeight;\n                // 计算 Y 逻辑\n                if (scrollYLoad) {\n                    const scrollBodyElem = refVirtualWrapper.value;\n                    const visibleYSize = Math.max(8, scrollBodyElem ? Math.ceil(scrollBodyElem.clientHeight / rowHeight) : 0);\n                    const offsetYSize = Math.max(0, Math.min(2, XEUtils.toNumber(sYOpts.oSize)));\n                    scrollYStore.offsetSize = offsetYSize;\n                    scrollYStore.visibleSize = visibleYSize;\n                    scrollYStore.endIndex = Math.max(scrollYStore.startIndex, visibleYSize + offsetYSize, scrollYStore.endIndex);\n                    updateYData();\n                }\n                else {\n                    updateYSpace();\n                }\n            });\n        };\n        const handleScrollToOption = (option, isDwArrow) => {\n            const { scrollYLoad } = reactData;\n            const { optFullValMaps, scrollYStore } = internalData;\n            const valueField = computeValueField.value;\n            const cacheItem = optFullValMaps[option[valueField]];\n            if (cacheItem) {\n                const optid = cacheItem.key;\n                const avIndex = cacheItem._index;\n                if (avIndex > -1) {\n                    const optWrapperElem = refVirtualWrapper.value;\n                    const panelElem = refOptionPanel.value;\n                    const optElem = panelElem.querySelector(`[optid='${optid}']`);\n                    if (optWrapperElem) {\n                        if (optElem) {\n                            const wrapperHeight = optWrapperElem.offsetHeight;\n                            const offsetPadding = 1;\n                            if (isDwArrow) {\n                                if (optElem.offsetTop + optElem.offsetHeight - optWrapperElem.scrollTop > wrapperHeight) {\n                                    optWrapperElem.scrollTop = optElem.offsetTop + optElem.offsetHeight - wrapperHeight;\n                                }\n                                else if (optElem.offsetTop + offsetPadding < optWrapperElem.scrollTop || optElem.offsetTop + offsetPadding > optWrapperElem.scrollTop + optWrapperElem.clientHeight) {\n                                    optWrapperElem.scrollTop = optElem.offsetTop - offsetPadding;\n                                }\n                            }\n                            else {\n                                if (optElem.offsetTop + offsetPadding < optWrapperElem.scrollTop || optElem.offsetTop + offsetPadding > optWrapperElem.scrollTop + optWrapperElem.clientHeight) {\n                                    optWrapperElem.scrollTop = optElem.offsetTop - offsetPadding;\n                                }\n                                else if (optElem.offsetTop + optElem.offsetHeight - optWrapperElem.scrollTop > wrapperHeight) {\n                                    optWrapperElem.scrollTop = optElem.offsetTop + optElem.offsetHeight - wrapperHeight;\n                                }\n                            }\n                        }\n                        else if (scrollYLoad) {\n                            if (isDwArrow) {\n                                optWrapperElem.scrollTop = avIndex * scrollYStore.rowHeight - optWrapperElem.clientHeight + scrollYStore.rowHeight;\n                            }\n                            else {\n                                optWrapperElem.scrollTop = avIndex * scrollYStore.rowHeight;\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        /**\n         * 如果有滚动条，则滚动到对应的位置\n         * @param {Number} scrollLeft 左距离\n         * @param {Number} scrollTop 上距离\n         */\n        const scrollTo = (scrollLeft, scrollTop) => {\n            const scrollBodyElem = refVirtualWrapper.value;\n            if (scrollBodyElem) {\n                if (XEUtils.isNumber(scrollLeft)) {\n                    scrollBodyElem.scrollLeft = scrollLeft;\n                }\n                if (XEUtils.isNumber(scrollTop)) {\n                    scrollBodyElem.scrollTop = scrollTop;\n                }\n            }\n            if (reactData.scrollYLoad) {\n                return new Promise(resolve => {\n                    setTimeout(() => {\n                        nextTick(() => {\n                            resolve();\n                        });\n                    }, 50);\n                });\n            }\n            return nextTick();\n        };\n        /**\n         * 刷新滚动条\n         */\n        const refreshScroll = () => {\n            const { lastScrollLeft, lastScrollTop } = internalData;\n            return clearScroll().then(() => {\n                if (lastScrollLeft || lastScrollTop) {\n                    internalData.lastScrollLeft = 0;\n                    internalData.lastScrollTop = 0;\n                    return scrollTo(lastScrollLeft, lastScrollTop);\n                }\n            });\n        };\n        /**\n         * 重新计算列表\n         */\n        const recalculate = () => {\n            const el = refElem.value;\n            if (el && el.clientWidth && el.clientHeight) {\n                return computeScrollLoad();\n            }\n            return Promise.resolve();\n        };\n        const loadYData = (evnt) => {\n            const { scrollYStore } = internalData;\n            const { startIndex, endIndex, visibleSize, offsetSize, rowHeight } = scrollYStore;\n            const scrollBodyElem = evnt.target;\n            const scrollTop = scrollBodyElem.scrollTop;\n            const toVisibleIndex = Math.floor(scrollTop / rowHeight);\n            const offsetStartIndex = Math.max(0, toVisibleIndex - 1 - offsetSize);\n            const offsetEndIndex = toVisibleIndex + visibleSize + offsetSize;\n            if (toVisibleIndex <= startIndex || toVisibleIndex >= endIndex - visibleSize - 1) {\n                if (startIndex !== offsetStartIndex || endIndex !== offsetEndIndex) {\n                    scrollYStore.startIndex = offsetStartIndex;\n                    scrollYStore.endIndex = offsetEndIndex;\n                    updateYData();\n                }\n            }\n        };\n        // 滚动、拖动过程中不需要触发\n        const isVMScrollProcess = () => {\n            const delayHover = 250;\n            const { lastScrollTime } = internalData;\n            return !!(lastScrollTime && Date.now() < lastScrollTime + delayHover);\n        };\n        const scrollEvent = (evnt) => {\n            const scrollBodyElem = evnt.target;\n            const scrollTop = scrollBodyElem.scrollTop;\n            const scrollLeft = scrollBodyElem.scrollLeft;\n            const isX = scrollLeft !== internalData.lastScrollLeft;\n            const isY = scrollTop !== internalData.lastScrollTop;\n            internalData.lastScrollTop = scrollTop;\n            internalData.lastScrollLeft = scrollLeft;\n            if (reactData.scrollYLoad) {\n                loadYData(evnt);\n            }\n            internalData.lastScrollTime = Date.now();\n            dispatchEvent('scroll', { scrollLeft, scrollTop, isX, isY }, evnt);\n        };\n        /**\n         * 加载数据\n         * @param {Array} datas 数据\n         */\n        const loadData = (datas) => {\n            cacheItemMap(datas || []);\n            const { isLoaded, fullData, scrollYStore } = internalData;\n            const defaultOpts = computeDefaultOpts.value;\n            const sYOpts = computeSYOpts.value;\n            const valueField = computeValueField.value;\n            Object.assign(scrollYStore, {\n                startIndex: 0,\n                endIndex: 1,\n                visibleSize: 0\n            });\n            internalData.synchData = datas || [];\n            // 如果gt为0，则总是启用\n            reactData.scrollYLoad = !!sYOpts.enabled && sYOpts.gt > -1 && (sYOpts.gt === 0 || sYOpts.gt <= fullData.length);\n            handleData();\n            if (!isLoaded) {\n                const { selectMode } = defaultOpts;\n                if (datas.length > 0 && XEUtils.eqNull(props.modelValue)) {\n                    if (selectMode === 'first' || selectMode === 'last') {\n                        const selectItem = XEUtils[selectMode](datas);\n                        if (selectItem) {\n                            nextTick(() => {\n                                if (XEUtils.eqNull(props.modelValue)) {\n                                    emitModel(selectItem[valueField]);\n                                }\n                            });\n                        }\n                    }\n                    internalData.isLoaded = true;\n                }\n            }\n            return computeScrollLoad().then(() => {\n                refreshScroll();\n            });\n        };\n        const clearScroll = () => {\n            const scrollBodyElem = refVirtualWrapper.value;\n            if (scrollBodyElem) {\n                scrollBodyElem.scrollTop = 0;\n                scrollBodyElem.scrollLeft = 0;\n            }\n            internalData.lastScrollTop = 0;\n            internalData.lastScrollLeft = 0;\n            return nextTick();\n        };\n        const hasOptGroupById = (optid) => {\n            const { optGroupKeyMaps } = internalData;\n            return !!optGroupKeyMaps[optid];\n        };\n        const selectMethods = {\n            dispatchEvent,\n            loadData,\n            reloadData(datas) {\n                internalData.isLoaded = false;\n                clearScroll();\n                return loadData(datas);\n            },\n            isPanelVisible() {\n                return reactData.visiblePanel;\n            },\n            togglePanel() {\n                if (reactData.visiblePanel) {\n                    hideOptionPanel();\n                }\n                else {\n                    showOptionPanel();\n                }\n                return nextTick();\n            },\n            hidePanel() {\n                if (reactData.visiblePanel) {\n                    hideOptionPanel();\n                }\n                return nextTick();\n            },\n            showPanel() {\n                if (!reactData.visiblePanel) {\n                    showOptionPanel();\n                }\n                return nextTick();\n            },\n            refreshOption() {\n                handleOption();\n                updateYData();\n                return nextTick();\n            },\n            focus() {\n                const $input = refInput.value;\n                reactData.isActivated = true;\n                $input.blur();\n                return nextTick();\n            },\n            blur() {\n                const $input = refInput.value;\n                $input.blur();\n                reactData.isActivated = false;\n                return nextTick();\n            },\n            recalculate,\n            clearScroll\n        };\n        Object.assign($xeSelect, selectMethods);\n        const renderOption = (list, group) => {\n            const { allowCreate, optionKey, modelValue } = props;\n            const { currentOption } = reactData;\n            const { optAddMaps } = internalData;\n            const optionOpts = computeOptionOpts.value;\n            const labelField = computeLabelField.value;\n            const valueField = computeValueField.value;\n            const groupLabelField = computeGroupLabelField.value;\n            const { useKey } = optionOpts;\n            const optionSlot = slots.option;\n            return list.map((option, cIndex) => {\n                const { slots, className } = option;\n                const optid = getOptId(option);\n                const optionValue = option[valueField];\n                const isOptGroup = hasOptGroupById(optid);\n                const isAdd = !!(allowCreate && optAddMaps[optid]);\n                const isSelected = !isAdd && (XEUtils.isArray(modelValue) ? modelValue.indexOf(optionValue) > -1 : modelValue === optionValue);\n                const isVisible = isAdd || (!isOptGroup || isOptionVisible(option));\n                const isDisabled = !isAdd && checkOptionDisabled(isSelected, option, group);\n                const defaultSlot = slots ? slots.default : null;\n                const optParams = { option, group: null, $select: $xeSelect };\n                const optVNs = optionSlot ? callSlot(optionSlot, optParams) : (defaultSlot ? callSlot(defaultSlot, optParams) : getFuncText(option[(isOptGroup ? groupLabelField : labelField)]));\n                return isVisible\n                    ? h('div', {\n                        key: useKey || optionKey ? optid : cIndex,\n                        class: ['vxe-select-option', className ? (XEUtils.isFunction(className) ? className(optParams) : className) : '', {\n                                'vxe-select-optgroup': isOptGroup,\n                                'is--disabled': isDisabled,\n                                'is--selected': isSelected,\n                                'is--add': isAdd,\n                                'is--hover': currentOption && getOptId(currentOption) === optid\n                            }],\n                        // attrs\n                        optid: optid,\n                        // event\n                        onMousedown: (evnt) => {\n                            const isLeftBtn = evnt.button === 0;\n                            if (isLeftBtn) {\n                                evnt.stopPropagation();\n                            }\n                        },\n                        onClick: (evnt) => {\n                            if (!isDisabled && !isOptGroup) {\n                                changeOptionEvent(evnt, option);\n                            }\n                        },\n                        onMouseenter: () => {\n                            if (!isDisabled && !isOptGroup && !isVMScrollProcess()) {\n                                setCurrentOption(option);\n                            }\n                        }\n                    }, allowCreate\n                        ? [\n                            h('span', {\n                                key: 1,\n                                class: 'vxe-select-option--label'\n                            }, optVNs),\n                            isAdd\n                                ? h('span', {\n                                    key: 2,\n                                    class: 'vxe-select-option--add-icon'\n                                }, [\n                                    h('i', {\n                                        class: getIcon().ADD_OPTION\n                                    })\n                                ])\n                                : renderEmptyElement($xeSelect)\n                        ]\n                        : optVNs)\n                    : renderEmptyElement($xeSelect);\n            });\n        };\n        const renderOpts = () => {\n            const { optList, searchLoading } = reactData;\n            if (searchLoading) {\n                return [\n                    h('div', {\n                        class: 'vxe-select--search-loading'\n                    }, [\n                        h('i', {\n                            class: ['vxe-select--search-icon', getIcon().SELECT_LOADED]\n                        }),\n                        h('span', {\n                            class: 'vxe-select--search-text'\n                        }, getI18n('vxe.select.loadingText'))\n                    ])\n                ];\n            }\n            if (optList.length) {\n                return renderOption(optList);\n            }\n            return [\n                h('div', {\n                    class: 'vxe-select--empty-placeholder'\n                }, props.emptyText || getI18n('vxe.select.emptyText'))\n            ];\n        };\n        const renderVN = () => {\n            const { className, popupClassName, loading, filterable } = props;\n            const { initialized, isActivated, isAniVisible, visiblePanel, bodyHeight, topSpaceHeight } = reactData;\n            const vSize = computeSize.value;\n            const isDisabled = computeIsDisabled.value;\n            const selectLabel = computeSelectLabel.value;\n            const btnTransfer = computeBtnTransfer.value;\n            const formReadonly = computeFormReadonly.value;\n            const inpPlaceholder = computeInpPlaceholder.value;\n            const defaultSlot = slots.default;\n            const headerSlot = slots.header;\n            const footerSlot = slots.footer;\n            const prefixSlot = slots.prefix;\n            if (formReadonly) {\n                return h('div', {\n                    ref: refElem,\n                    class: ['vxe-select--readonly', className]\n                }, [\n                    h('div', {\n                        class: 'vxe-select-slots',\n                        ref: 'hideOption'\n                    }, defaultSlot ? defaultSlot({}) : []),\n                    h('span', {\n                        class: 'vxe-select-label'\n                    }, selectLabel)\n                ]);\n            }\n            return h('div', {\n                ref: refElem,\n                class: ['vxe-select', className ? (XEUtils.isFunction(className) ? className({ $select: $xeSelect }) : className) : '', {\n                        [`size--${vSize}`]: vSize,\n                        'is--visible': visiblePanel,\n                        'is--disabled': isDisabled,\n                        'is--filter': filterable,\n                        'is--loading': loading,\n                        'is--active': isActivated\n                    }]\n            }, [\n                h('div', {\n                    class: 'vxe-select-slots',\n                    ref: 'hideOption'\n                }, defaultSlot ? defaultSlot({}) : []),\n                h(VxeInputComponent, {\n                    ref: refInput,\n                    clearable: props.clearable,\n                    placeholder: inpPlaceholder,\n                    readonly: true,\n                    disabled: isDisabled,\n                    type: 'text',\n                    prefixIcon: props.prefixIcon,\n                    suffixIcon: loading ? getIcon().SELECT_LOADED : (visiblePanel ? getIcon().SELECT_OPEN : getIcon().SELECT_CLOSE),\n                    autoFocus: false,\n                    modelValue: selectLabel,\n                    onClear: clearEvent,\n                    onClick: clickEvent,\n                    onFocus: focusEvent,\n                    onBlur: blurEvent,\n                    onSuffixClick: suffixClickEvent\n                }, prefixSlot\n                    ? {\n                        prefix: () => prefixSlot({})\n                    }\n                    : {}),\n                h(Teleport, {\n                    to: 'body',\n                    disabled: btnTransfer ? !initialized : true\n                }, [\n                    h('div', {\n                        ref: refOptionPanel,\n                        class: ['vxe-table--ignore-clear vxe-select--panel', popupClassName ? (XEUtils.isFunction(popupClassName) ? popupClassName({ $select: $xeSelect }) : popupClassName) : '', {\n                                [`size--${vSize}`]: vSize,\n                                'is--transfer': btnTransfer,\n                                'ani--leave': !loading && isAniVisible,\n                                'ani--enter': !loading && visiblePanel\n                            }],\n                        placement: reactData.panelPlacement,\n                        style: reactData.panelStyle\n                    }, initialized && (visiblePanel || isAniVisible)\n                        ? [\n                            h('div', {\n                                class: 'vxe-select--panel-wrapper'\n                            }, [\n                                filterable\n                                    ? h('div', {\n                                        class: 'vxe-select--panel-search'\n                                    }, [\n                                        h(VxeInputComponent, {\n                                            ref: refInpSearch,\n                                            class: 'vxe-select-search--input',\n                                            modelValue: reactData.searchValue,\n                                            clearable: true,\n                                            disabled: false,\n                                            readonly: false,\n                                            placeholder: getI18n('vxe.select.search'),\n                                            prefixIcon: getIcon().INPUT_SEARCH,\n                                            'onUpdate:modelValue': modelSearchEvent,\n                                            onFocus: focusSearchEvent,\n                                            onChange: triggerSearchEvent,\n                                            onSearch: triggerSearchEvent\n                                        })\n                                    ])\n                                    : renderEmptyElement($xeSelect),\n                                headerSlot\n                                    ? h('div', {\n                                        class: 'vxe-select--panel-header'\n                                    }, headerSlot({}))\n                                    : renderEmptyElement($xeSelect),\n                                h('div', {\n                                    class: 'vxe-select--panel-body'\n                                }, [\n                                    h('div', {\n                                        ref: refVirtualWrapper,\n                                        class: 'vxe-select-option--wrapper',\n                                        onScroll: scrollEvent\n                                    }, [\n                                        h('div', {\n                                            class: 'vxe-select--y-space',\n                                            style: {\n                                                height: bodyHeight ? `${bodyHeight}px` : ''\n                                            }\n                                        }),\n                                        h('div', {\n                                            ref: refVirtualBody,\n                                            class: 'vxe-select--body',\n                                            style: {\n                                                marginTop: topSpaceHeight ? `${topSpaceHeight}px` : ''\n                                            }\n                                        }, renderOpts())\n                                    ])\n                                ]),\n                                footerSlot\n                                    ? h('div', {\n                                        class: 'vxe-select--panel-footer'\n                                    }, footerSlot({}))\n                                    : renderEmptyElement($xeSelect)\n                            ])\n                        ]\n                        : [])\n                ])\n            ]);\n        };\n        watch(() => reactData.staticOptions, (val) => {\n            loadData(val);\n        });\n        watch(() => props.options, (val) => {\n            loadData(val || []);\n        });\n        watch(() => props.optionGroups, (val) => {\n            loadData(val || []);\n        });\n        onMounted(() => {\n            nextTick(() => {\n                const { options, optionGroups } = props;\n                if (optionGroups) {\n                    loadData(optionGroups);\n                }\n                else if (options) {\n                    loadData(options);\n                }\n            });\n            globalEvents.on($xeSelect, 'mousewheel', handleGlobalMousewheelEvent);\n            globalEvents.on($xeSelect, 'mousedown', handleGlobalMousedownEvent);\n            globalEvents.on($xeSelect, 'keydown', handleGlobalKeydownEvent);\n            globalEvents.on($xeSelect, 'blur', handleGlobalBlurEvent);\n        });\n        onUnmounted(() => {\n            globalEvents.off($xeSelect, 'mousewheel');\n            globalEvents.off($xeSelect, 'mousedown');\n            globalEvents.off($xeSelect, 'keydown');\n            globalEvents.off($xeSelect, 'blur');\n        });\n        provide('$xeSelect', $xeSelect);\n        $xeSelect.renderVN = renderVN;\n        return $xeSelect;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,sBAAoB;AAMpB,SAAS,gBAAgB,QAAQ;AAC7B,SAAO,OAAO,YAAY;AAC9B;AACA,SAAS,iBAAiB;AACtB,SAAO,gBAAAA,QAAQ,SAAS,MAAM;AAClC;AACA,IAAO,iBAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,YAAY,CAAC,QAAQ,QAAQ,SAAS,KAAK;AAAA,IAC3C,eAAe;AAAA,IACf,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,UAAU;AAAA,IACV,mBAAmB;AAAA,MACf,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,WAAW,CAAC,QAAQ,QAAQ;AAAA,IAC5B,gBAAgB,CAAC,QAAQ,QAAQ;AAAA,IACjC,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO,QAAQ,UAAU,EAAE;AAAA,IAC1D;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,QAAQ;AAAA;AAAA,IAER,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,gBAAgB;AAAA,IAChB,SAAS;AAAA;AAAA,IAET,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA;AAAA,IAEA,WAAW;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,WAAW,OAAO,YAAY,IAAI;AACxC,UAAM,YAAY,OAAO,aAAa,IAAI;AAC1C,UAAM,WAAW,OAAO,YAAY,IAAI;AACxC,UAAM,UAAU,OAAO,WAAW,IAAI;AACtC,UAAM,eAAe,OAAO,kBAAkB,IAAI;AAClD,UAAM,MAAM,gBAAAA,QAAQ,SAAS;AAC7B,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,eAAe,IAAI;AACzB,UAAM,oBAAoB,IAAI;AAC9B,UAAM,iBAAiB,IAAI;AAC3B,UAAM,iBAAiB,IAAI;AAC3B,UAAM,EAAE,YAAY,IAAI,QAAQ,KAAK;AACrC,UAAM,YAAY,SAAS;AAAA,MACvB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,SAAS,CAAC;AAAA,MACV,kBAAkB,CAAC;AAAA,MACnB,eAAe,CAAC;AAAA,MAChB,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,MACb,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,YAAY,CAAC;AAAA,MACb,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,IACjB,CAAC;AACD,UAAM,eAAe;AAAA,MACjB,WAAW,CAAC;AAAA,MACZ,UAAU,CAAC;AAAA,MACX,YAAY,CAAC;AAAA,MACb,iBAAiB,CAAC;AAAA,MAClB,gBAAgB,CAAC;AAAA,MACjB,eAAe,CAAC;AAAA,MAChB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,cAAc;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,MACf;AAAA,MACA,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACf;AACA,UAAM,UAAU;AAAA,MACZ;AAAA,IACJ;AACA,UAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,IACtB;AACA,UAAM,sBAAsB,SAAS,MAAM;AACvC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,aAAa,MAAM;AACnB,YAAI,SAAS;AACT,iBAAO,QAAQ,MAAM;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,aAAa,MAAM;AACnB,YAAI,SAAS;AACT,iBAAO,QAAQ,MAAM;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,aAAa,MAAM;AACnB,cAAM,iBAAiB,UAAU,EAAE,OAAO;AAC1C,YAAI,gBAAAA,QAAQ,UAAU,cAAc,GAAG;AACnC,iBAAO;AAAA,QACX;AACA,YAAI,YAAY,YAAY,aAAa,SAAS;AAC9C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,wBAAwB,SAAS,MAAM;AACzC,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,aAAa;AACb,eAAO,YAAY,WAAW;AAAA,MAClC;AACA,YAAM,oBAAoB,UAAU,EAAE,OAAO;AAC7C,UAAI,mBAAmB;AACnB,eAAO,YAAY,iBAAiB;AAAA,MACxC;AACA,aAAO,QAAQ,uBAAuB;AAAA,IAC1C,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,aAAO,OAAO,OAAO,CAAC,GAAG,MAAM,aAAa;AAAA,IAChD,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW;AAAA,IAC9C,CAAC;AACD,UAAM,wBAAwB,SAAS,MAAM;AACzC,aAAO,OAAO,OAAO,CAAC,GAAG,MAAM,gBAAgB;AAAA,IACnD,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,YAAY,iBAAiB;AACnC,aAAO,UAAU,SAAS;AAAA,IAC9B,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,YAAY,iBAAiB;AACnC,aAAO,UAAU,SAAS;AAAA,IAC9B,CAAC;AACD,UAAM,yBAAyB,SAAS,MAAM;AAC1C,YAAM,iBAAiB,sBAAsB;AAC7C,aAAO,eAAe,SAAS;AAAA,IACnC,CAAC;AACD,UAAM,2BAA2B,SAAS,MAAM;AAC5C,YAAM,iBAAiB,sBAAsB;AAC7C,aAAO,eAAe,WAAW;AAAA,IACrC,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,EAAE,YAAY,UAAU,IAAI,IAAI;AACtC,UAAI,YAAY,KAAK;AACjB,gBAAQ,gBAAAA,QAAQ,QAAQ,UAAU,IAAI,WAAW,SAAU,gBAAAA,QAAQ,OAAO,UAAU,IAAI,IAAI,MAAO,gBAAAA,QAAQ,SAAS,GAAG;AAAA,MAC3H;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,gBAAgB,SAAS,MAAM;AACjC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,OAAO,kBAAkB,UAAU,EAAE,OAAO,SAAS,MAAM,kBAAkB,MAAM,OAAO;AAAA,IACnI,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,OAAO,cAAc,MAAM,YAAY;AAAA,IAChF,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,OAAO,cAAc,MAAM,YAAY;AAAA,IAChF,CAAC;AACD,UAAM,yBAAyB,SAAS,MAAM;AAC1C,aAAO,gBAAAA,QAAQ,SAAS,MAAM,iBAAiB;AAAA,IACnD,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,YAAM,EAAE,YAAY,QAAQ,SAAS,IAAI;AACzC,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,kBAAkB,uBAAuB;AAC/C,UAAI,gBAAAA,QAAQ,OAAO,UAAU,GAAG;AAC5B,eAAO;AAAA,MACX;AACA,YAAM,OAAO,gBAAAA,QAAQ,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AACnE,UAAI,UAAU,WAAW;AACrB,eAAO,KAAK,IAAI,SAAO,qBAAqB,GAAG,CAAC,EAAE,KAAK,IAAI;AAAA,MAC/D;AACA,aAAO,KAAK,IAAI,CAAC,QAAQ;AACrB,cAAM,QAAQ,eAAe,GAAG;AAChC,YAAI,YAAY,kBAAkB,KAAK,MAAM,SAAS,iBAAiB;AACnE,iBAAO,GAAG,MAAM,UAAU,GAAG,eAAe,CAAC;AAAA,QACjD;AACA,eAAO;AAAA,MACX,CAAC,EAAE,KAAK,IAAI;AAAA,IAChB,CAAC;AACD,UAAM,WAAW,CAAC,UAAU,WAAW;AACnC,UAAI,UAAU;AACV,YAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,qBAAW,MAAM,QAAQ,KAAK;AAAA,QAClC;AACA,YAAI,gBAAAA,QAAQ,WAAW,QAAQ,GAAG;AAC9B,iBAAO,WAAW,SAAS,MAAM,CAAC;AAAA,QACtC;AAAA,MACJ;AACA,aAAO,CAAC;AAAA,IACZ;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,SAAS,UAAU,GAAG,MAAM,CAAC;AAAA,IAChE;AACA,UAAM,YAAY,CAAC,UAAU;AACzB,WAAK,qBAAqB,KAAK;AAAA,IACnC;AACA,UAAM,YAAY,MAAM;AACpB,YAAM,aAAa,kBAAkB;AACrC,aAAO,WAAW,YAAY,MAAM,YAAY;AAAA,IACpD;AACA,UAAM,WAAW,CAAC,WAAW;AACzB,YAAM,QAAQ,OAAO,UAAU,CAAC;AAChC,aAAO,QAAQ,mBAAmB,KAAK,IAAI;AAAA,IAC/C;AACA,UAAM,uBAAuB,CAAC,UAAU;AACpC,YAAM,EAAE,eAAe,eAAe,IAAI;AAC1C,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,cAAc,KAAK,KAAK,eAAe,KAAK;AAC/D,YAAM,OAAO,aAAa,WAAW,OAAO;AAC5C,aAAO,gBAAAA,QAAQ,cAAc,OAAO,KAAK,UAAU,IAAI,KAAK;AAAA,IAChE;AACA,UAAM,iBAAiB,CAAC,UAAU;AAC9B,YAAM,EAAE,eAAe,IAAI;AAC3B,YAAM,aAAa,kBAAkB;AACrC,YAAM,YAAY,UAAU,YAAY,eAAe,KAAK,IAAI;AAChE,aAAO,YAAY,UAAU,KAAK,UAAU,IAAI,gBAAAA,QAAQ,cAAc,KAAK;AAAA,IAC/E;AACA,UAAM,eAAe,CAAC,UAAU;AAC5B,YAAM,oBAAoB,yBAAyB;AACnD,YAAM,aAAa,kBAAkB;AACrC,YAAM,MAAM,UAAU;AACtB,YAAM,eAAe,CAAC;AACtB,YAAM,cAAc,CAAC;AACrB,YAAM,OAAO,CAAC;AACd,YAAM,gBAAgB,CAAC,SAAS;AAC5B,aAAK,KAAK,IAAI;AACd,YAAI,QAAQ,SAAS,IAAI;AACzB,YAAI,CAAC,OAAO;AACR,kBAAQ,eAAe;AACvB,eAAK,GAAG,IAAI;AAAA,QAChB;AACA,oBAAY,KAAK,UAAU,CAAC,IAAI;AAAA,UAC5B,KAAK;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,QACZ;AAAA,MACJ;AACA,YAAM,QAAQ,CAAC,UAAU;AACrB,sBAAc,KAAK;AACnB,YAAI,MAAM,iBAAiB,GAAG;AAC1B,uBAAa,MAAM,GAAG,CAAC,IAAI;AAC3B,gBAAM,iBAAiB,EAAE,QAAQ,aAAa;AAAA,QAClD;AAAA,MACJ,CAAC;AACD,mBAAa,WAAW;AACxB,mBAAa,kBAAkB;AAC/B,mBAAa,iBAAiB;AAC9B,gBAAU;AACV,mBAAa;AAAA,IACjB;AAIA,UAAM,eAAe,MAAM;AACvB,YAAM,EAAE,YAAY,YAAY,aAAa,IAAI;AACjD,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,UAAU,eAAe,IAAI;AACrC,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,kBAAkB;AACrC,YAAM,YAAY,GAAG,eAAe,EAAE,GAAG,YAAY;AACrD,UAAI,SAAS,CAAC;AACd,UAAI,cAAc,cAAc;AAC5B,iBAAS,SAAS,OAAO,YAAU,gBAAgB,MAAM,KAAK,aAAa,EAAE,SAAS,WAAW,OAAO,MAAM,QAAQ,aAAa,OAAO,WAAW,CAAC,CAAC;AAAA,MAC3J,WACS,YAAY;AACjB,iBAAS,SAAS,OAAO,YAAU,gBAAgB,MAAM,MAAM,CAAC,aAAa,GAAG,OAAO,UAAU,CAAC,GAAG,YAAY,EAAE,QAAQ,SAAS,IAAI,GAAG;AAAA,MAC/I,OACK;AACD,iBAAS,SAAS,OAAO,eAAe;AAAA,MAC5C;AACA,aAAO,QAAQ,CAAC,MAAM,UAAU;AAC5B,cAAM,YAAY,eAAe,KAAK,UAAU,CAAC;AACjD,YAAI,WAAW;AACX,oBAAU,SAAS;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,gBAAU,mBAAmB;AAC7B,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,mBAAmB,CAAC,WAAW;AACjC,UAAI,QAAQ;AACR,kBAAU,gBAAgB;AAAA,MAC9B;AAAA,IACJ;AACA,UAAM,eAAe,MAAM;AACvB,UAAI,UAAU,aAAa,cAAc,GAAG;AACxC,kBAAU,aAAa,WAAW;AAAA,MACtC;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC1B,aAAO,SAAS,EAAE,KAAK,MAAM;AACzB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,KAAK,QAAQ;AACnB,cAAM,YAAY,eAAe;AACjC,cAAM,cAAc,mBAAmB;AACvC,YAAI,aAAa,IAAI;AACjB,gBAAM,eAAe,GAAG;AACxB,gBAAM,cAAc,GAAG;AACvB,gBAAM,cAAc,UAAU;AAC9B,gBAAM,aAAa,UAAU;AAC7B,gBAAM,aAAa;AACnB,gBAAM,aAAa;AAAA,YACf,QAAQ;AAAA,UACZ;AACA,gBAAM,EAAE,aAAa,cAAc,eAAe,aAAa,IAAI,eAAe,EAAE;AACpF,cAAI,iBAAiB;AACrB,cAAI,aAAa;AACb,gBAAI,OAAO;AACX,gBAAI,MAAM,cAAc;AACxB,gBAAI,cAAc,OAAO;AACrB,+BAAiB;AACjB,oBAAM,cAAc;AAAA,YACxB,WACS,CAAC,WAAW;AAEjB,kBAAI,MAAM,cAAc,aAAa,eAAe;AAChD,iCAAiB;AACjB,sBAAM,cAAc;AAAA,cACxB;AAEA,kBAAI,MAAM,YAAY;AAClB,iCAAiB;AACjB,sBAAM,cAAc;AAAA,cACxB;AAAA,YACJ;AAEA,gBAAI,OAAO,aAAa,aAAa,cAAc;AAC/C,sBAAQ,OAAO,aAAa,aAAa;AAAA,YAC7C;AAEA,gBAAI,OAAO,YAAY;AACnB,qBAAO;AAAA,YACX;AACA,mBAAO,OAAO,YAAY;AAAA,cACtB,MAAM,GAAG,IAAI;AAAA,cACb,KAAK,GAAG,GAAG;AAAA,cACX,UAAU,GAAG,WAAW;AAAA,YAC5B,CAAC;AAAA,UACL,OACK;AACD,gBAAI,cAAc,OAAO;AACrB,+BAAiB;AACjB,yBAAW,SAAS,GAAG,YAAY;AAAA,YACvC,WACS,CAAC,WAAW;AAEjB,kBAAI,cAAc,eAAe,cAAc,eAAe;AAE1D,oBAAI,cAAc,eAAe,cAAc,YAAY;AACvD,mCAAiB;AACjB,6BAAW,SAAS,GAAG,YAAY;AAAA,gBACvC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,oBAAU,aAAa;AACvB,oBAAU,iBAAiB;AAC3B,iBAAO,SAAS;AAAA,QACpB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,EAAE,SAAS,YAAY,OAAO,IAAI;AACxC,YAAM,EAAE,UAAU,UAAU,IAAI;AAChC,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,WAAW,CAAC,YAAY;AACzB,YAAI,WAAW;AACX,uBAAa,SAAS;AACtB,uBAAa,YAAY;AAAA,QAC7B;AACA,YAAI,CAAC,UAAU,aAAa;AACxB,oBAAU,cAAc;AAAA,QAC5B;AACA,kBAAU,cAAc;AACxB,kBAAU,eAAe;AACzB,YAAI,YAAY;AACZ,cAAI,UAAU,WAAW,WAAW,WAAW,YAAY,CAAC,SAAS,QAAQ;AACzE,+BAAmB;AAAA,UACvB,OACK;AACD,yBAAa;AACb,wBAAY;AAAA,UAChB;AAAA,QACJ;AACA,mBAAW,MAAM;AACb,oBAAU,eAAe;AACzB,4BAAkB;AAClB,sBAAY,EAAE,KAAK,MAAM,cAAc,CAAC;AAAA,QAC5C,GAAG,EAAE;AACL,mBAAW,MAAM;AACb,sBAAY,EAAE,KAAK,MAAM,cAAc,CAAC;AAAA,QAC5C,GAAG,GAAG;AACN,qBAAa;AACb,wBAAgB;AAChB,sBAAc,kBAAkB,EAAE,SAAS,KAAK,GAAG,IAAI;AAAA,MAC3D;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC1B,gBAAU,cAAc;AACxB,gBAAU,gBAAgB;AAC1B,gBAAU,eAAe;AACzB,mBAAa,YAAY,WAAW,MAAM;AACtC,kBAAU,eAAe;AAAA,MAC7B,GAAG,GAAG;AACN,oBAAc,kBAAkB,EAAE,SAAS,MAAM,GAAG,IAAI;AAAA,IAC5D;AACA,UAAM,cAAc,CAAC,MAAM,gBAAgB;AACvC,gBAAU,WAAW;AACrB,UAAI,gBAAgB,MAAM,YAAY;AAClC,sBAAc,UAAU,EAAE,OAAO,YAAY,GAAG,IAAI;AAEpD,YAAI,WAAW,cAAc;AACzB,kBAAQ,iBAAiB,MAAM,aAAa,WAAW,OAAO,WAAW;AAAA,QAC7E;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC,MAAM,gBAAgB;AAC3C,mBAAa,gBAAgB,CAAC;AAC9B,kBAAY,MAAM,WAAW;AAC7B,oBAAc,SAAS,EAAE,OAAO,YAAY,GAAG,IAAI;AAAA,IACvD;AACA,UAAM,aAAa,CAAC,WAAW;AAC3B,YAAM,EAAE,OAAO,IAAI;AACnB,sBAAgB,QAAQ,IAAI;AAC5B,sBAAgB;AAAA,IACpB;AACA,UAAM,oBAAoB,CAAC,MAAM,WAAW;AACxC,YAAM,EAAE,YAAY,SAAS,IAAI;AACjC,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,cAAc,OAAO,UAAU;AACrC,YAAM,aAAa,cAAc,WAAW;AAC5C,UAAI,CAAC,UAAU,cAAc;AACzB;AAAA,MACJ;AACA,UAAI,YAAY;AACZ,mBAAW,OAAO;AAAA,MACtB,OACK;AACD,sBAAc,WAAW,IAAI;AAAA,UACzB,KAAK,SAAS,MAAM;AAAA,UACpB,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ;AAAA,MACJ;AACA,UAAI,UAAU;AACV,YAAI,gBAAgB,CAAC;AACrB,cAAM,aAAa,gBAAAA,QAAQ,OAAO,UAAU,IAAI,CAAC,IAAK,gBAAAA,QAAQ,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC5G,cAAM,QAAQ,gBAAAA,QAAQ,YAAY,YAAY,SAAO,QAAQ,WAAW;AACxE,YAAI,UAAU,IAAI;AACd,0BAAgB,WAAW,OAAO,CAAC,WAAW,CAAC;AAAA,QACnD,OACK;AACD,0BAAgB,WAAW,OAAO,CAAC,QAAQ,QAAQ,WAAW;AAAA,QAClE;AACA,oBAAY,MAAM,aAAa;AAAA,MACnC,OACK;AACD,oBAAY,MAAM,WAAW;AAC7B,wBAAgB;AAAA,MACpB;AACA,gBAAU;AAAA,IACd;AACA,UAAM,8BAA8B,CAAC,SAAS;AAC1C,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,YAAY;AACb,YAAI,cAAc;AACd,gBAAM,YAAY,eAAe;AACjC,cAAI,mBAAmB,MAAM,SAAS,EAAE,MAAM;AAC1C,4BAAgB;AAAA,UACpB,OACK;AACD,4BAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,6BAA6B,CAAC,SAAS;AACzC,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,YAAY;AACb,cAAM,KAAK,QAAQ;AACnB,cAAM,YAAY,eAAe;AACjC,kBAAU,cAAc,mBAAmB,MAAM,EAAE,EAAE,QAAQ,mBAAmB,MAAM,SAAS,EAAE;AACjG,YAAI,gBAAgB,CAAC,UAAU,aAAa;AACxC,0BAAgB;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,oBAAoB,CAAC,WAAW;AAClC,YAAM,aAAa,OAAO;AAC1B,YAAM,QAAQ,SAAS,MAAM;AAC7B,UAAI,CAAC,cAAc,CAAC,gBAAgB,KAAK,GAAG;AACxC,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,UAAM,mBAAmB,CAAC,QAAQ,cAAc;AAC5C,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,kBAAkB,QAAQ,IAAI;AACtC,YAAM,EAAE,gBAAgB,WAAW,IAAI;AACvC,YAAM,aAAa,kBAAkB;AACrC,UAAI,WAAW;AACf,UAAI,iBAAiB;AACrB,UAAI,eAAe,QAAQ,QAAQ;AAC/B,cAAM,YAAY,QAAQ,CAAC;AAC3B,cAAM,QAAQ,SAAS,SAAS;AAChC,YAAI,WAAW,KAAK,GAAG;AACnB,2BAAiB;AACjB,qBAAW,CAAC,WAAW,KAAK,CAAC,EAAE,OAAO,QAAQ;AAAA,QAClD;AAAA,MACJ;AACA,UAAI,CAAC,QAAQ;AACT,YAAI,WAAW;AACX,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,kBAAM,OAAO,SAAS,CAAC;AACvB,gBAAI,kBAAkB,IAAI,GAAG;AACzB,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,OACK;AACD,mBAAS,MAAM,SAAS,SAAS,GAAG,OAAO,GAAG,OAAO;AACjD,kBAAM,OAAO,SAAS,GAAG;AACzB,gBAAI,kBAAkB,IAAI,GAAG;AACzB,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,UAAU;AACd,YAAM,YAAY,SAAS,eAAe,OAAO,UAAU,CAAC,IAAI;AAChE,UAAI,WAAW;AACX,kBAAU,UAAU,SAAS;AAAA,MACjC;AACA,UAAI,UAAU,IAAI;AACd,YAAI,WAAW;AACX,mBAAS,IAAI,UAAU,GAAG,KAAK,SAAS,SAAS,GAAG,KAAK;AACrD,kBAAM,OAAO,SAAS,CAAC;AACvB,gBAAI,kBAAkB,IAAI,GAAG;AACzB,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,OACK;AACD,cAAI,UAAU,GAAG;AACb,qBAAS,MAAM,UAAU,GAAG,OAAO,GAAG,OAAO;AACzC,oBAAM,OAAO,SAAS,GAAG;AACzB,kBAAI,kBAAkB,IAAI,GAAG;AACzB,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,UAAM,2BAA2B,CAAC,SAAS;AACvC,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,cAAc,cAAc,IAAI;AACxC,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,YAAY;AACb,cAAM,QAAQ,aAAa,OAAO,MAAM,kBAAkB,GAAG;AAC7D,cAAM,UAAU,aAAa,OAAO,MAAM,kBAAkB,KAAK;AACjE,cAAM,QAAQ,aAAa,OAAO,MAAM,kBAAkB,MAAM;AAChE,cAAM,YAAY,aAAa,OAAO,MAAM,kBAAkB,QAAQ;AACtE,cAAM,YAAY,aAAa,OAAO,MAAM,kBAAkB,UAAU;AACxE,cAAM,QAAQ,aAAa,OAAO,MAAM,kBAAkB,MAAM;AAChE,cAAM,aAAa,aAAa,OAAO,MAAM,kBAAkB,QAAQ;AACvE,YAAI,OAAO;AACP,oBAAU,cAAc;AAAA,QAC5B;AACA,YAAI,cAAc;AACd,cAAI,SAAS,OAAO;AAChB,4BAAgB;AAAA,UACpB,WACS,SAAS;AACd,gBAAI,eAAe;AACf,mBAAK,eAAe;AACpB,mBAAK,gBAAgB;AACrB,gCAAkB,MAAM,aAAa;AAAA,YACzC;AAAA,UACJ,WACS,aAAa,WAAW;AAC7B,iBAAK,eAAe;AACpB,gBAAI,eAAe,iBAAiB,eAAe,SAAS;AAE5D,gBAAI,CAAC,cAAc;AACf,6BAAe,iBAAiB,MAAM,SAAS;AAAA,YACnD;AACA,gBAAI,cAAc;AACd,+BAAiB,YAAY;AAC7B,mCAAqB,cAAc,SAAS;AAAA,YAChD;AAAA,UACJ,WACS,YAAY;AACjB,iBAAK,eAAe;AAAA,UACxB;AAAA,QACJ,YACU,aAAa,aAAa,WAAW,eAAe,UAAU,aAAa;AACjF,eAAK,eAAe;AACpB,0BAAgB;AAAA,QACpB;AACA,YAAI,UAAU,aAAa;AACvB,cAAI,SAAS,WAAW;AACpB,4BAAgB,MAAM,IAAI;AAAA,UAC9B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,wBAAwB,MAAM;AAChC,sBAAgB;AAAA,IACpB;AACA,UAAM,oBAAoB,MAAM;AAC5B,UAAI,MAAM,YAAY;AAClB,iBAAS,MAAM;AACX,gBAAM,YAAY,aAAa;AAC/B,cAAI,WAAW;AACX,sBAAU,MAAM;AAAA,UACpB;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,aAAa,CAAC,SAAS;AACzB,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,YAAY;AACb,YAAI,CAAC,UAAU,cAAc;AACzB,oBAAU,oBAAoB;AAC9B,0BAAgB;AAChB,qBAAW,MAAM;AACb,sBAAU,oBAAoB;AAAA,UAClC,GAAG,GAAG;AAAA,QACV;AAAA,MACJ;AACA,oBAAc,SAAS,CAAC,GAAG,IAAI;AAAA,IACnC;AACA,UAAM,aAAa,CAAC,SAAS;AACzB,uBAAiB,IAAI;AACrB,oBAAc,SAAS,EAAE,eAAe,OAAO,SAAS,UAAU,aAAa,GAAG,IAAI;AAAA,IAC1F;AACA,UAAM,YAAY,CAAC,SAAS;AACxB,gBAAU,cAAc;AACxB,oBAAc,QAAQ,CAAC,GAAG,IAAI;AAAA,IAClC;AACA,UAAM,mBAAmB,CAAC,SAAS;AAC/B,uBAAiB,IAAI;AACrB,oBAAc,SAAS,EAAE,eAAe,MAAM,SAAS,UAAU,aAAa,GAAG,IAAI;AAAA,IACzF;AACA,UAAM,mBAAmB,CAAC,UAAU;AAChC,gBAAU,cAAc;AAAA,IAC5B;AACA,UAAM,mBAAmB,MAAM;AAC3B,gBAAU,cAAc;AAAA,IAC5B;AACA,UAAM,oBAAoB,MAAM;AAC5B,YAAM,EAAE,YAAY,QAAQ,aAAa,IAAI;AAC7C,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,aAAa,kBAAkB;AACrC,YAAM,cAAc,WAAW,eAAe;AAC9C,UAAI,UAAU,eAAe,WAAW,SAAS;AAC7C,kBAAU,gBAAgB;AAC1B,gBAAQ,QAAQ,YAAY,EAAE,SAAS,WAAW,aAAa,OAAO,WAAW,CAAC,CAAC,EAAE,KAAK,MAAM,SAAS,CAAC,EACrG,MAAM,MAAM,SAAS,CAAC,EACtB,QAAQ,MAAM;AACf,oBAAU,gBAAgB;AAC1B,uBAAa;AACb,sBAAY;AAAA,QAChB,CAAC;AAAA,MACL,OACK;AACD,qBAAa;AACb,oBAAY;AAAA,MAChB;AAAA,IACJ;AACA,UAAM,qBAAqB,gBAAAA,QAAQ,SAAS,mBAAmB,KAAK,EAAE,UAAU,KAAK,CAAC;AACtF,UAAM,mBAAmB,CAAC,WAAW;AACjC,YAAM,EAAE,OAAO,IAAI;AACnB,aAAO,eAAe;AACtB,UAAI,UAAU,mBAAmB;AAC7B,kBAAU,oBAAoB;AAAA,MAClC,OACK;AACD,YAAI,UAAU,cAAc;AACxB,0BAAgB;AAAA,QACpB,OACK;AACD,0BAAgB;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,sBAAsB,CAAC,YAAY,QAAQ,UAAU;AACvD,UAAI,OAAO,UAAU;AACjB,eAAO;AAAA,MACX;AACA,UAAI,SAAS,MAAM,UAAU;AACzB,eAAO;AAAA,MACX;AACA,YAAM,aAAa,kBAAkB;AACrC,UAAI,cAAc,CAAC,YAAY;AAC3B,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,UAAM,eAAe,MAAM;AACvB,YAAM,EAAE,aAAa,iBAAiB,IAAI;AAC1C,YAAM,EAAE,aAAa,IAAI;AACzB,gBAAU,aAAa,cAAc,iBAAiB,SAAS,aAAa,YAAY;AACxF,gBAAU,iBAAiB,cAAc,KAAK,IAAI,aAAa,aAAa,aAAa,WAAW,CAAC,IAAI;AAAA,IAC7G;AACA,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,YAAY,YAAY,IAAI;AACpC,YAAM,EAAE,aAAa,kBAAkB,YAAY,IAAI;AACvD,YAAM,EAAE,YAAY,aAAa,IAAI;AACrC,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,kBAAkB;AACrC,YAAM,WAAW,cAAc,iBAAiB,MAAM,aAAa,YAAY,aAAa,QAAQ,IAAI,iBAAiB,MAAM,CAAC;AAChI,UAAI,cAAc,eAAe,aAAa;AAC1C,YAAI,CAAC,SAAS,KAAK,YAAU,OAAO,UAAU,MAAM,WAAW,GAAG;AAC9D,gBAAM,UAAU,WAAW,WAAW,KAAK,SAAS;AAAA,YAChD,CAAC,UAAU,CAAC,GAAG;AAAA,YACf,CAAC,UAAU,GAAG;AAAA,YACd,CAAC,UAAU,GAAG;AAAA,UAClB,CAAC;AACD,qBAAW,WAAW,IAAI;AAC1B,mBAAS,QAAQ,OAAO;AAAA,QAC5B;AAAA,MACJ;AACA,gBAAU,UAAU;AACpB,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,cAAc,MAAM;AACtB,iBAAW;AACX,mBAAa;AAAA,IACjB;AACA,UAAM,oBAAoB,MAAM;AAC5B,aAAO,SAAS,EAAE,KAAK,MAAM;AACzB,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,kBAAkB,eAAe;AACvC,cAAM,SAAS,cAAc;AAC7B,YAAI,YAAY;AAChB,YAAI;AACJ,YAAI,iBAAiB;AACjB,cAAI,OAAO,OAAO;AACd,4BAAgB,gBAAgB,cAAc,OAAO,KAAK;AAAA,UAC9D;AACA,cAAI,CAAC,eAAe;AAChB,4BAAgB,gBAAgB,SAAS,CAAC;AAAA,UAC9C;AAAA,QACJ;AACA,YAAI,eAAe;AACf,sBAAY,cAAc;AAAA,QAC9B;AACA,oBAAY,KAAK,IAAI,IAAI,SAAS;AAClC,qBAAa,YAAY;AAEzB,YAAI,aAAa;AACb,gBAAM,iBAAiB,kBAAkB;AACzC,gBAAM,eAAe,KAAK,IAAI,GAAG,iBAAiB,KAAK,KAAK,eAAe,eAAe,SAAS,IAAI,CAAC;AACxG,gBAAM,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,gBAAAA,QAAQ,SAAS,OAAO,KAAK,CAAC,CAAC;AAC3E,uBAAa,aAAa;AAC1B,uBAAa,cAAc;AAC3B,uBAAa,WAAW,KAAK,IAAI,aAAa,YAAY,eAAe,aAAa,aAAa,QAAQ;AAC3G,sBAAY;AAAA,QAChB,OACK;AACD,uBAAa;AAAA,QACjB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,uBAAuB,CAAC,QAAQ,cAAc;AAChD,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,gBAAgB,aAAa,IAAI;AACzC,YAAM,aAAa,kBAAkB;AACrC,YAAM,YAAY,eAAe,OAAO,UAAU,CAAC;AACnD,UAAI,WAAW;AACX,cAAM,QAAQ,UAAU;AACxB,cAAM,UAAU,UAAU;AAC1B,YAAI,UAAU,IAAI;AACd,gBAAM,iBAAiB,kBAAkB;AACzC,gBAAM,YAAY,eAAe;AACjC,gBAAM,UAAU,UAAU,cAAc,WAAW,KAAK,IAAI;AAC5D,cAAI,gBAAgB;AAChB,gBAAI,SAAS;AACT,oBAAM,gBAAgB,eAAe;AACrC,oBAAM,gBAAgB;AACtB,kBAAI,WAAW;AACX,oBAAI,QAAQ,YAAY,QAAQ,eAAe,eAAe,YAAY,eAAe;AACrF,iCAAe,YAAY,QAAQ,YAAY,QAAQ,eAAe;AAAA,gBAC1E,WACS,QAAQ,YAAY,gBAAgB,eAAe,aAAa,QAAQ,YAAY,gBAAgB,eAAe,YAAY,eAAe,cAAc;AACjK,iCAAe,YAAY,QAAQ,YAAY;AAAA,gBACnD;AAAA,cACJ,OACK;AACD,oBAAI,QAAQ,YAAY,gBAAgB,eAAe,aAAa,QAAQ,YAAY,gBAAgB,eAAe,YAAY,eAAe,cAAc;AAC5J,iCAAe,YAAY,QAAQ,YAAY;AAAA,gBACnD,WACS,QAAQ,YAAY,QAAQ,eAAe,eAAe,YAAY,eAAe;AAC1F,iCAAe,YAAY,QAAQ,YAAY,QAAQ,eAAe;AAAA,gBAC1E;AAAA,cACJ;AAAA,YACJ,WACS,aAAa;AAClB,kBAAI,WAAW;AACX,+BAAe,YAAY,UAAU,aAAa,YAAY,eAAe,eAAe,aAAa;AAAA,cAC7G,OACK;AACD,+BAAe,YAAY,UAAU,aAAa;AAAA,cACtD;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAMA,UAAM,WAAW,CAAC,YAAY,cAAc;AACxC,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,gBAAgB;AAChB,YAAI,gBAAAA,QAAQ,SAAS,UAAU,GAAG;AAC9B,yBAAe,aAAa;AAAA,QAChC;AACA,YAAI,gBAAAA,QAAQ,SAAS,SAAS,GAAG;AAC7B,yBAAe,YAAY;AAAA,QAC/B;AAAA,MACJ;AACA,UAAI,UAAU,aAAa;AACvB,eAAO,IAAI,QAAQ,aAAW;AAC1B,qBAAW,MAAM;AACb,qBAAS,MAAM;AACX,sBAAQ;AAAA,YACZ,CAAC;AAAA,UACL,GAAG,EAAE;AAAA,QACT,CAAC;AAAA,MACL;AACA,aAAO,SAAS;AAAA,IACpB;AAIA,UAAM,gBAAgB,MAAM;AACxB,YAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,aAAO,YAAY,EAAE,KAAK,MAAM;AAC5B,YAAI,kBAAkB,eAAe;AACjC,uBAAa,iBAAiB;AAC9B,uBAAa,gBAAgB;AAC7B,iBAAO,SAAS,gBAAgB,aAAa;AAAA,QACjD;AAAA,MACJ,CAAC;AAAA,IACL;AAIA,UAAM,cAAc,MAAM;AACtB,YAAM,KAAK,QAAQ;AACnB,UAAI,MAAM,GAAG,eAAe,GAAG,cAAc;AACzC,eAAO,kBAAkB;AAAA,MAC7B;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,UAAM,YAAY,CAAC,SAAS;AACxB,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,EAAE,YAAY,UAAU,aAAa,YAAY,UAAU,IAAI;AACrE,YAAM,iBAAiB,KAAK;AAC5B,YAAM,YAAY,eAAe;AACjC,YAAM,iBAAiB,KAAK,MAAM,YAAY,SAAS;AACvD,YAAM,mBAAmB,KAAK,IAAI,GAAG,iBAAiB,IAAI,UAAU;AACpE,YAAM,iBAAiB,iBAAiB,cAAc;AACtD,UAAI,kBAAkB,cAAc,kBAAkB,WAAW,cAAc,GAAG;AAC9E,YAAI,eAAe,oBAAoB,aAAa,gBAAgB;AAChE,uBAAa,aAAa;AAC1B,uBAAa,WAAW;AACxB,sBAAY;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AAEA,UAAM,oBAAoB,MAAM;AAC5B,YAAM,aAAa;AACnB,YAAM,EAAE,eAAe,IAAI;AAC3B,aAAO,CAAC,EAAE,kBAAkB,KAAK,IAAI,IAAI,iBAAiB;AAAA,IAC9D;AACA,UAAM,cAAc,CAAC,SAAS;AAC1B,YAAM,iBAAiB,KAAK;AAC5B,YAAM,YAAY,eAAe;AACjC,YAAM,aAAa,eAAe;AAClC,YAAM,MAAM,eAAe,aAAa;AACxC,YAAM,MAAM,cAAc,aAAa;AACvC,mBAAa,gBAAgB;AAC7B,mBAAa,iBAAiB;AAC9B,UAAI,UAAU,aAAa;AACvB,kBAAU,IAAI;AAAA,MAClB;AACA,mBAAa,iBAAiB,KAAK,IAAI;AACvC,oBAAc,UAAU,EAAE,YAAY,WAAW,KAAK,IAAI,GAAG,IAAI;AAAA,IACrE;AAKA,UAAM,WAAW,CAAC,UAAU;AACxB,mBAAa,SAAS,CAAC,CAAC;AACxB,YAAM,EAAE,UAAU,UAAU,aAAa,IAAI;AAC7C,YAAM,cAAc,mBAAmB;AACvC,YAAM,SAAS,cAAc;AAC7B,YAAM,aAAa,kBAAkB;AACrC,aAAO,OAAO,cAAc;AAAA,QACxB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa;AAAA,MACjB,CAAC;AACD,mBAAa,YAAY,SAAS,CAAC;AAEnC,gBAAU,cAAc,CAAC,CAAC,OAAO,WAAW,OAAO,KAAK,OAAO,OAAO,OAAO,KAAK,OAAO,MAAM,SAAS;AACxG,iBAAW;AACX,UAAI,CAAC,UAAU;AACX,cAAM,EAAE,WAAW,IAAI;AACvB,YAAI,MAAM,SAAS,KAAK,gBAAAA,QAAQ,OAAO,MAAM,UAAU,GAAG;AACtD,cAAI,eAAe,WAAW,eAAe,QAAQ;AACjD,kBAAM,aAAa,gBAAAA,QAAQ,UAAU,EAAE,KAAK;AAC5C,gBAAI,YAAY;AACZ,uBAAS,MAAM;AACX,oBAAI,gBAAAA,QAAQ,OAAO,MAAM,UAAU,GAAG;AAClC,4BAAU,WAAW,UAAU,CAAC;AAAA,gBACpC;AAAA,cACJ,CAAC;AAAA,YACL;AAAA,UACJ;AACA,uBAAa,WAAW;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,kBAAkB,EAAE,KAAK,MAAM;AAClC,sBAAc;AAAA,MAClB,CAAC;AAAA,IACL;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,gBAAgB;AAChB,uBAAe,YAAY;AAC3B,uBAAe,aAAa;AAAA,MAChC;AACA,mBAAa,gBAAgB;AAC7B,mBAAa,iBAAiB;AAC9B,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,kBAAkB,CAAC,UAAU;AAC/B,YAAM,EAAE,gBAAgB,IAAI;AAC5B,aAAO,CAAC,CAAC,gBAAgB,KAAK;AAAA,IAClC;AACA,UAAM,gBAAgB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,WAAW,OAAO;AACd,qBAAa,WAAW;AACxB,oBAAY;AACZ,eAAO,SAAS,KAAK;AAAA,MACzB;AAAA,MACA,iBAAiB;AACb,eAAO,UAAU;AAAA,MACrB;AAAA,MACA,cAAc;AACV,YAAI,UAAU,cAAc;AACxB,0BAAgB;AAAA,QACpB,OACK;AACD,0BAAgB;AAAA,QACpB;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,YAAY;AACR,YAAI,UAAU,cAAc;AACxB,0BAAgB;AAAA,QACpB;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,YAAY;AACR,YAAI,CAAC,UAAU,cAAc;AACzB,0BAAgB;AAAA,QACpB;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,gBAAgB;AACZ,qBAAa;AACb,oBAAY;AACZ,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,QAAQ;AACJ,cAAM,SAAS,SAAS;AACxB,kBAAU,cAAc;AACxB,eAAO,KAAK;AACZ,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,OAAO;AACH,cAAM,SAAS,SAAS;AACxB,eAAO,KAAK;AACZ,kBAAU,cAAc;AACxB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,WAAO,OAAO,WAAW,aAAa;AACtC,UAAM,eAAe,CAAC,MAAM,UAAU;AAClC,YAAM,EAAE,aAAa,WAAW,WAAW,IAAI;AAC/C,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,kBAAkB;AACrC,YAAM,kBAAkB,uBAAuB;AAC/C,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,aAAa,MAAM;AACzB,aAAO,KAAK,IAAI,CAAC,QAAQ,WAAW;AAChC,cAAM,EAAE,OAAAC,QAAO,UAAU,IAAI;AAC7B,cAAM,QAAQ,SAAS,MAAM;AAC7B,cAAM,cAAc,OAAO,UAAU;AACrC,cAAM,aAAa,gBAAgB,KAAK;AACxC,cAAM,QAAQ,CAAC,EAAE,eAAe,WAAW,KAAK;AAChD,cAAM,aAAa,CAAC,UAAU,gBAAAD,QAAQ,QAAQ,UAAU,IAAI,WAAW,QAAQ,WAAW,IAAI,KAAK,eAAe;AAClH,cAAM,YAAY,UAAU,CAAC,cAAc,gBAAgB,MAAM;AACjE,cAAM,aAAa,CAAC,SAAS,oBAAoB,YAAY,QAAQ,KAAK;AAC1E,cAAM,cAAcC,SAAQA,OAAM,UAAU;AAC5C,cAAM,YAAY,EAAE,QAAQ,OAAO,MAAM,SAAS,UAAU;AAC5D,cAAM,SAAS,aAAa,SAAS,YAAY,SAAS,IAAK,cAAc,SAAS,aAAa,SAAS,IAAI,YAAY,OAAQ,aAAa,kBAAkB,UAAW,CAAC;AAC/K,eAAO,YACD,EAAE,OAAO;AAAA,UACP,KAAK,UAAU,YAAY,QAAQ;AAAA,UACnC,OAAO,CAAC,qBAAqB,YAAa,gBAAAD,QAAQ,WAAW,SAAS,IAAI,UAAU,SAAS,IAAI,YAAa,IAAI;AAAA,YAC1G,uBAAuB;AAAA,YACvB,gBAAgB;AAAA,YAChB,gBAAgB;AAAA,YAChB,WAAW;AAAA,YACX,aAAa,iBAAiB,SAAS,aAAa,MAAM;AAAA,UAC9D,CAAC;AAAA;AAAA,UAEL;AAAA;AAAA,UAEA,aAAa,CAAC,SAAS;AACnB,kBAAM,YAAY,KAAK,WAAW;AAClC,gBAAI,WAAW;AACX,mBAAK,gBAAgB;AAAA,YACzB;AAAA,UACJ;AAAA,UACA,SAAS,CAAC,SAAS;AACf,gBAAI,CAAC,cAAc,CAAC,YAAY;AAC5B,gCAAkB,MAAM,MAAM;AAAA,YAClC;AAAA,UACJ;AAAA,UACA,cAAc,MAAM;AAChB,gBAAI,CAAC,cAAc,CAAC,cAAc,CAAC,kBAAkB,GAAG;AACpD,+BAAiB,MAAM;AAAA,YAC3B;AAAA,UACJ;AAAA,QACJ,GAAG,cACG;AAAA,UACE,EAAE,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,OAAO;AAAA,UACX,GAAG,MAAM;AAAA,UACT,QACM,EAAE,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,KAAK;AAAA,cACH,OAAO,QAAQ,EAAE;AAAA,YACrB,CAAC;AAAA,UACL,CAAC,IACC,mBAAmB,SAAS;AAAA,QACtC,IACE,MAAM,IACV,mBAAmB,SAAS;AAAA,MACtC,CAAC;AAAA,IACL;AACA,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,SAAS,cAAc,IAAI;AACnC,UAAI,eAAe;AACf,eAAO;AAAA,UACH,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,KAAK;AAAA,cACH,OAAO,CAAC,2BAA2B,QAAQ,EAAE,aAAa;AAAA,YAC9D,CAAC;AAAA,YACD,EAAE,QAAQ;AAAA,cACN,OAAO;AAAA,YACX,GAAG,QAAQ,wBAAwB,CAAC;AAAA,UACxC,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,QAAQ,QAAQ;AAChB,eAAO,aAAa,OAAO;AAAA,MAC/B;AACA,aAAO;AAAA,QACH,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,MAAM,aAAa,QAAQ,sBAAsB,CAAC;AAAA,MACzD;AAAA,IACJ;AACA,UAAM,WAAW,MAAM;AACnB,YAAM,EAAE,WAAW,gBAAgB,SAAS,WAAW,IAAI;AAC3D,YAAM,EAAE,aAAa,aAAa,cAAc,cAAc,YAAY,eAAe,IAAI;AAC7F,YAAM,QAAQ,YAAY;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,cAAc,mBAAmB;AACvC,YAAM,cAAc,mBAAmB;AACvC,YAAM,eAAe,oBAAoB;AACzC,YAAM,iBAAiB,sBAAsB;AAC7C,YAAM,cAAc,MAAM;AAC1B,YAAM,aAAa,MAAM;AACzB,YAAM,aAAa,MAAM;AACzB,YAAM,aAAa,MAAM;AACzB,UAAI,cAAc;AACd,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,OAAO,CAAC,wBAAwB,SAAS;AAAA,QAC7C,GAAG;AAAA,UACC,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,YACP,KAAK;AAAA,UACT,GAAG,cAAc,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA,UACrC,EAAE,QAAQ;AAAA,YACN,OAAO;AAAA,UACX,GAAG,WAAW;AAAA,QAClB,CAAC;AAAA,MACL;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO,CAAC,cAAc,YAAa,gBAAAA,QAAQ,WAAW,SAAS,IAAI,UAAU,EAAE,SAAS,UAAU,CAAC,IAAI,YAAa,IAAI;AAAA,UAChH,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,eAAe;AAAA,UACf,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,eAAe;AAAA,UACf,cAAc;AAAA,QAClB,CAAC;AAAA,MACT,GAAG;AAAA,QACC,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,UACP,KAAK;AAAA,QACT,GAAG,cAAc,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA,QACrC,EAAE,eAAmB;AAAA,UACjB,KAAK;AAAA,UACL,WAAW,MAAM;AAAA,UACjB,aAAa;AAAA,UACb,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,YAAY,MAAM;AAAA,UAClB,YAAY,UAAU,QAAQ,EAAE,gBAAiB,eAAe,QAAQ,EAAE,cAAc,QAAQ,EAAE;AAAA,UAClG,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,eAAe;AAAA,QACnB,GAAG,aACG;AAAA,UACE,QAAQ,MAAM,WAAW,CAAC,CAAC;AAAA,QAC/B,IACE,CAAC,CAAC;AAAA,QACR,EAAE,UAAU;AAAA,UACR,IAAI;AAAA,UACJ,UAAU,cAAc,CAAC,cAAc;AAAA,QAC3C,GAAG;AAAA,UACC,EAAE,OAAO;AAAA,YACL,KAAK;AAAA,YACL,OAAO,CAAC,6CAA6C,iBAAkB,gBAAAA,QAAQ,WAAW,cAAc,IAAI,eAAe,EAAE,SAAS,UAAU,CAAC,IAAI,iBAAkB,IAAI;AAAA,cACnK,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,cACpB,gBAAgB;AAAA,cAChB,cAAc,CAAC,WAAW;AAAA,cAC1B,cAAc,CAAC,WAAW;AAAA,YAC9B,CAAC;AAAA,YACL,WAAW,UAAU;AAAA,YACrB,OAAO,UAAU;AAAA,UACrB,GAAG,gBAAgB,gBAAgB,gBAC7B;AAAA,YACE,EAAE,OAAO;AAAA,cACL,OAAO;AAAA,YACX,GAAG;AAAA,cACC,aACM,EAAE,OAAO;AAAA,gBACP,OAAO;AAAA,cACX,GAAG;AAAA,gBACC,EAAE,eAAmB;AAAA,kBACjB,KAAK;AAAA,kBACL,OAAO;AAAA,kBACP,YAAY,UAAU;AAAA,kBACtB,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,aAAa,QAAQ,mBAAmB;AAAA,kBACxC,YAAY,QAAQ,EAAE;AAAA,kBACtB,uBAAuB;AAAA,kBACvB,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,UAAU;AAAA,gBACd,CAAC;AAAA,cACL,CAAC,IACC,mBAAmB,SAAS;AAAA,cAClC,aACM,EAAE,OAAO;AAAA,gBACP,OAAO;AAAA,cACX,GAAG,WAAW,CAAC,CAAC,CAAC,IACf,mBAAmB,SAAS;AAAA,cAClC,EAAE,OAAO;AAAA,gBACL,OAAO;AAAA,cACX,GAAG;AAAA,gBACC,EAAE,OAAO;AAAA,kBACL,KAAK;AAAA,kBACL,OAAO;AAAA,kBACP,UAAU;AAAA,gBACd,GAAG;AAAA,kBACC,EAAE,OAAO;AAAA,oBACL,OAAO;AAAA,oBACP,OAAO;AAAA,sBACH,QAAQ,aAAa,GAAG,UAAU,OAAO;AAAA,oBAC7C;AAAA,kBACJ,CAAC;AAAA,kBACD,EAAE,OAAO;AAAA,oBACL,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,OAAO;AAAA,sBACH,WAAW,iBAAiB,GAAG,cAAc,OAAO;AAAA,oBACxD;AAAA,kBACJ,GAAG,WAAW,CAAC;AAAA,gBACnB,CAAC;AAAA,cACL,CAAC;AAAA,cACD,aACM,EAAE,OAAO;AAAA,gBACP,OAAO;AAAA,cACX,GAAG,WAAW,CAAC,CAAC,CAAC,IACf,mBAAmB,SAAS;AAAA,YACtC,CAAC;AAAA,UACL,IACE,CAAC,CAAC;AAAA,QACZ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,UAAM,MAAM,UAAU,eAAe,CAAC,QAAQ;AAC1C,eAAS,GAAG;AAAA,IAChB,CAAC;AACD,UAAM,MAAM,MAAM,SAAS,CAAC,QAAQ;AAChC,eAAS,OAAO,CAAC,CAAC;AAAA,IACtB,CAAC;AACD,UAAM,MAAM,MAAM,cAAc,CAAC,QAAQ;AACrC,eAAS,OAAO,CAAC,CAAC;AAAA,IACtB,CAAC;AACD,cAAU,MAAM;AACZ,eAAS,MAAM;AACX,cAAM,EAAE,SAAS,aAAa,IAAI;AAClC,YAAI,cAAc;AACd,mBAAS,YAAY;AAAA,QACzB,WACS,SAAS;AACd,mBAAS,OAAO;AAAA,QACpB;AAAA,MACJ,CAAC;AACD,mBAAa,GAAG,WAAW,cAAc,2BAA2B;AACpE,mBAAa,GAAG,WAAW,aAAa,0BAA0B;AAClE,mBAAa,GAAG,WAAW,WAAW,wBAAwB;AAC9D,mBAAa,GAAG,WAAW,QAAQ,qBAAqB;AAAA,IAC5D,CAAC;AACD,gBAAY,MAAM;AACd,mBAAa,IAAI,WAAW,YAAY;AACxC,mBAAa,IAAI,WAAW,WAAW;AACvC,mBAAa,IAAI,WAAW,SAAS;AACrC,mBAAa,IAAI,WAAW,MAAM;AAAA,IACtC,CAAC;AACD,YAAQ,aAAa,SAAS;AAC9B,cAAU,WAAW;AACrB,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;", "names": ["XEUtils", "slots"]}