{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/node_modules/is-plain-object/index.es.js", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/utils.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/native.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/custom.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/oneof.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/oneoftype.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/arrayof.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/instanceof.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/objectof.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/validators/shape.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/index.ts", "../../../../../node_modules/.pnpm/vue-types@3.0.2_vue@3.5.13_typescript@5.8.3_/node_modules/vue-types/src/sensibles.ts", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/vue-types/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/createContext.js"], "sourcesContent": ["/*!\n * isobject <https://github.com/jonschlinkert/isobject>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObject(val) {\n  return val != null && typeof val === 'object' && Array.isArray(val) === false;\n}\n\n/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObjectObject(o) {\n  return isObject(o) === true\n    && Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObjectObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (typeof ctor !== 'function') return false;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObjectObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n}\n\nexport default isPlainObject;\n", "import _isPlainObject from 'is-plain-object'\nimport {\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueProp,\n  InferType,\n  PropOptions,\n} from './types'\n\nconst ObjProto = Object.prototype\nconst toString = ObjProto.toString\nexport const hasOwn = ObjProto.hasOwnProperty\n\nconst FN_MATCH_REGEXP = /^\\s*function (\\w+)/\n\n// https://github.com/vuejs/vue/blob/dev/src/core/util/props.js#L177\nexport function getType(\n  fn: VueProp<any> | (() => any) | (new (...args: any[]) => any),\n): string {\n  const type = (fn as VueProp<any>)?.type ?? fn\n  if (type) {\n    const match = type.toString().match(FN_MATCH_REGEXP)\n    return match ? match[1] : ''\n  }\n  return ''\n}\n\nexport function getNativeType(value: any): string {\n  if (value === null || value === undefined) return ''\n  const match = value.constructor.toString().match(FN_MATCH_REGEXP)\n  return match ? match[1] : ''\n}\n\ntype PlainObject = { [key: string]: any }\nexport const isPlainObject = _isPlainObject as (obj: any) => obj is PlainObject\n\n/**\n * No-op function\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\n/**\n * A function that returns its first argument\n *\n * @param arg - Any argument\n */\nexport const identity = (arg: any) => arg\n\nlet warn: (msg: string) => string | void = identity\n\nif (process.env.NODE_ENV !== 'production') {\n  const hasConsole = typeof console !== 'undefined'\n  warn = hasConsole\n    ? function warn(msg) {\n        // eslint-disable-next-line no-console\n        console.warn(`[VueTypes warn]: ${msg}`)\n      }\n    : identity\n}\n\nexport { warn }\n\n/**\n * Checks for a own property in an object\n *\n * @param {object} obj - Object\n * @param {string} prop - Property to check\n */\nexport const has = <T extends any, U extends keyof T>(obj: T, prop: U) =>\n  hasOwn.call(obj, prop)\n\n/**\n * Determines whether the passed value is an integer. Uses `Number.isInteger` if available\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n * @param {*} value - The value to be tested for being an integer.\n * @returns {boolean}\n */\nexport const isInteger =\n  Number.isInteger ||\n  function isInteger(value: unknown): value is number {\n    return (\n      typeof value === 'number' &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    )\n  }\n\n/**\n * Determines whether the passed value is an Array.\n *\n * @param {*} value - The value to be tested for being an array.\n * @returns {boolean}\n */\nexport const isArray =\n  Array.isArray ||\n  function isArray(value): value is any[] {\n    return toString.call(value) === '[object Array]'\n  }\n\n/**\n * Checks if a value is a function\n *\n * @param {any} value - Value to check\n * @returns {boolean}\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isFunction = (value: unknown): value is Function =>\n  toString.call(value) === '[object Function]'\n\n/**\n * Checks if the passed-in value is a VueTypes type\n * @param value - The value to check\n */\nexport const isVueTypeDef = <T>(\n  value: any,\n): value is VueTypeDef<T> | VueTypeValidableDef<T> =>\n  isPlainObject(value) && has(value, '_vueTypes_name')\n\n/**\n * Checks if the passed-in value is a Vue prop definition object or a VueTypes type\n * @param value - The value to check\n */\nexport const isComplexType = <T>(value: any): value is VueProp<T> =>\n  isPlainObject(value) &&\n  (has(value, 'type') ||\n    ['_vueTypes_name', 'validator', 'default', 'required'].some((k) =>\n      has(value, k),\n    ))\n\nexport interface WrappedFn {\n  (...args: any[]): any\n  __original: (...args: any[]) => any\n}\n\n/**\n * Binds a function to a context and saves a reference to the original.\n *\n * @param fn - Target function\n * @param ctx - New function context\n */\nexport function bindTo(fn: (...args: any[]) => any, ctx: any): WrappedFn {\n  return Object.defineProperty(fn.bind(ctx), '__original', {\n    value: fn,\n  })\n}\n\n/**\n * Returns the original function bounded with `bindTo`. If the passed-in function\n * has not be bound, the function itself will be returned instead.\n *\n * @param fn - Function to unwrap\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function unwrap<T extends WrappedFn | Function>(fn: T) {\n  return (fn as WrappedFn).__original ?? fn\n}\n\n/**\n * Validates a given value against a prop type object.\n *\n * If `silent` is `false` (default) will return a boolean. If it is set to `true`\n * it will return `true` on success or a string error message on failure\n *\n * @param {Object|*} type - Type to use for validation. Either a type object or a constructor\n * @param {*} value - Value to check\n * @param {boolean} silent - Silence warnings\n */\nexport function validateType<T, U>(\n  type: T,\n  value: U,\n  silent = false,\n): string | boolean {\n  let typeToCheck: { [key: string]: any }\n  let valid = true\n  let expectedType = ''\n  if (!isPlainObject(type)) {\n    typeToCheck = { type }\n  } else {\n    typeToCheck = type\n  }\n  const namePrefix = isVueTypeDef(typeToCheck)\n    ? typeToCheck._vueTypes_name + ' - '\n    : ''\n\n  if (isComplexType(typeToCheck) && typeToCheck.type !== null) {\n    if (typeToCheck.type === undefined || typeToCheck.type === true) {\n      return valid\n    }\n    if (!typeToCheck.required && value === undefined) {\n      return valid\n    }\n    if (isArray(typeToCheck.type)) {\n      valid = typeToCheck.type.some(\n        (type: any) => validateType(type, value, true) === true,\n      )\n      expectedType = typeToCheck.type\n        .map((type: any) => getType(type))\n        .join(' or ')\n    } else {\n      expectedType = getType(typeToCheck)\n\n      if (expectedType === 'Array') {\n        valid = isArray(value)\n      } else if (expectedType === 'Object') {\n        valid = isPlainObject(value)\n      } else if (\n        expectedType === 'String' ||\n        expectedType === 'Number' ||\n        expectedType === 'Boolean' ||\n        expectedType === 'Function'\n      ) {\n        valid = getNativeType(value) === expectedType\n      } else {\n        valid = value instanceof typeToCheck.type\n      }\n    }\n  }\n\n  if (!valid) {\n    const msg = `${namePrefix}value \"${value}\" should be of type \"${expectedType}\"`\n    if (silent === false) {\n      warn(msg)\n      return false\n    }\n    return msg\n  }\n\n  if (has(typeToCheck, 'validator') && isFunction(typeToCheck.validator)) {\n    const oldWarn = warn\n    const warnLog = []\n    warn = (msg) => {\n      warnLog.push(msg)\n    }\n\n    valid = typeToCheck.validator(value)\n    warn = oldWarn\n\n    if (!valid) {\n      const msg = (warnLog.length > 1 ? '* ' : '') + warnLog.join('\\n* ')\n      warnLog.length = 0\n      if (silent === false) {\n        warn(msg)\n        return valid\n      }\n      return msg\n    }\n  }\n  return valid\n}\n\n/**\n * Adds `isRequired` and `def` modifiers to an object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toType<T = any>(name: string, obj: PropOptions<T>) {\n  const type: VueTypeDef<T> = Object.defineProperties(obj, {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n    def: {\n      value(def?: any) {\n        if (def === undefined && !this.default) {\n          return this\n        }\n        if (!isFunction(def) && validateType(this, def, true) !== true) {\n          warn(`${this._vueTypes_name} - invalid default value: \"${def}\"`)\n          return this\n        }\n        if (isArray(def)) {\n          this.default = () => [...def]\n        } else if (isPlainObject(def)) {\n          this.default = () => Object.assign({}, def)\n        } else {\n          this.default = def\n        }\n        return this\n      },\n    },\n  })\n\n  const { validator } = type\n  if (isFunction(validator)) {\n    type.validator = bindTo(validator, type)\n  }\n\n  return type\n}\n\n/**\n * Like `toType` but also adds the `validate()` method to the type object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toValidableType<T = any>(name: string, obj: PropOptions<T>) {\n  const type = toType<T>(name, obj)\n  return Object.defineProperty(type, 'validate', {\n    value(fn: (value: T) => boolean) {\n      if (isFunction(this.validator)) {\n        warn(\n          `${\n            this._vueTypes_name\n          } - calling .validate() will overwrite the current custom validator function. Validator info:\\n${JSON.stringify(\n            this,\n          )}`,\n        )\n      }\n      this.validator = bindTo(fn, this)\n      return this\n    },\n  }) as VueTypeValidableDef<T>\n}\n\n/**\n *  Clones an object preserving all of it's own keys.\n *\n * @param obj - Object to clone\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function clone<T extends object>(obj: T): T {\n  const descriptors = {} as { [P in keyof T]: any }\n  Object.getOwnPropertyNames(obj).forEach((key) => {\n    descriptors[key as keyof T] = Object.getOwnPropertyDescriptor(obj, key)\n  })\n  return Object.defineProperties({}, descriptors)\n}\n\n/**\n * Return a new VueTypes type using another type as base.\n *\n * Properties in the `props` object will overwrite those defined in the source one\n * expect for the `validator` function. In that case both functions will be executed in series.\n *\n * @param name - Name of the new type\n * @param source - Source type\n * @param props - Custom type properties\n */\nexport function fromType<T extends VueTypeDef<any>>(name: string, source: T): T\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>\n>(name: string, source: T, props: V): Omit<T, keyof V> & V\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>\n>(name: string, source: T, props?: V) {\n  // 1. create an exact copy of the source type\n  const copy = clone(source)\n\n  // 2. give it a new name\n  copy._vueTypes_name = name\n\n  if (!isPlainObject(props)) {\n    return copy\n  }\n  const { validator, ...rest } = props\n\n  // 3. compose the validator function\n  // with the one on the source (if present)\n  // and ensure it is bound to the copy\n  if (isFunction(validator)) {\n    let { validator: prevValidator } = copy\n\n    if (prevValidator) {\n      prevValidator = unwrap(prevValidator)\n    }\n\n    copy.validator = bindTo(\n      prevValidator\n        ? function (this: T, value: any) {\n            return (\n              prevValidator.call(this, value) && validator.call(this, value)\n            )\n          }\n        : validator,\n      copy,\n    )\n  }\n  // 4. overwrite the rest, if present\n  return Object.assign(copy, rest as V)\n}\n\nexport function indent(string: string) {\n  return string.replace(/^(?!\\s*$)/gm, '  ')\n}\n", "import { toType, toValidableType, isInteger } from '../utils'\nimport { PropType } from '../types'\n\nexport const any = () => toValidableType('any', {})\n\nexport const func = <T extends (...args: any[]) => any>() =>\n  toValidableType<T>('function', {\n    type: Function as PropType<T>,\n  })\n\nexport const bool = () =>\n  toValidableType('boolean', {\n    type: Boolean,\n  })\n\nexport const string = () =>\n  toValidableType('string', {\n    type: String,\n  })\n\nexport const number = () =>\n  toValidableType('number', {\n    type: Number,\n  })\n\nexport const array = <T>() =>\n  toValidableType<T[]>('array', {\n    type: Array,\n  })\n\nexport const object = <T extends { [key: string]: any }>() =>\n  toValidableType<T>('object', {\n    type: Object,\n  })\n\nexport const integer = () =>\n  toType('integer', {\n    type: Number,\n    validator(value) {\n      return isInteger(value)\n    },\n  })\n\nexport const symbol = () =>\n  toType<symbol>('symbol', {\n    validator(value) {\n      return typeof value === 'symbol'\n    },\n  })\n", "import { toType, warn } from '../utils'\nimport { ValidatorFunction, VueTypeDef } from '../types'\n\nexport default function custom<T>(\n  validatorFn: ValidatorFunction<T>,\n  warnMsg = 'custom validation failed',\n) {\n  if (typeof validatorFn !== 'function') {\n    throw new TypeError(\n      '[VueTypes error]: You must provide a function as argument',\n    )\n  }\n\n  return toType<T>(validatorFn.name || '<<anonymous function>>', {\n    validator(this: VueTypeDef<T>, value: T) {\n      const valid = validatorFn(value)\n      if (!valid) warn(`${this._vueTypes_name} - ${warnMsg}`)\n      return valid\n    },\n  })\n}\n", "import { Prop } from '../types'\nimport { toType, warn, isArray } from '../utils'\n\nexport default function oneOf<T extends readonly any[]>(arr: T) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument.',\n    )\n  }\n  const msg = `oneOf - value should be one of \"${arr.join('\", \"')}\".`\n  const allowedTypes = arr.reduce((ret, v) => {\n    if (v !== null && v !== undefined) {\n      const constr = (v as any).constructor\n      ret.indexOf(constr) === -1 && ret.push(constr)\n    }\n    return ret\n  }, [] as Prop<T[number]>[])\n\n  return toType<T[number]>('oneOf', {\n    type: allowedTypes.length > 0 ? allowedTypes : undefined,\n    validator(value) {\n      const valid = arr.indexOf(value) !== -1\n      if (!valid) warn(msg)\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType, PropType } from '../types'\nimport {\n  isArray,\n  isComplexType,\n  isVueTypeDef,\n  isFunction,\n  toType,\n  validateType,\n  warn,\n  indent,\n} from '../utils'\n\nexport default function oneOfType<\n  U extends VueProp<any> | Prop<any>,\n  V = InferType<U>\n>(arr: U[]) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument',\n    )\n  }\n\n  let hasCustomValidators = false\n\n  let nativeChecks: Prop<V>[] = []\n\n  for (let i = 0; i < arr.length; i += 1) {\n    const type = arr[i]\n    if (isComplexType<V>(type)) {\n      if (isVueTypeDef<V>(type) && type._vueTypes_name === 'oneOf') {\n        nativeChecks = nativeChecks.concat(type.type as PropType<V>)\n        continue\n      }\n      if (isFunction(type.validator)) {\n        hasCustomValidators = true\n      }\n      if (type.type !== true && type.type) {\n        nativeChecks = nativeChecks.concat(type.type)\n        continue\n      }\n    }\n    nativeChecks.push(type as Prop<V>)\n  }\n\n  // filter duplicates\n  nativeChecks = nativeChecks.filter((t, i) => nativeChecks.indexOf(t) === i)\n\n  if (!hasCustomValidators) {\n    // we got just native objects (ie: Array, Object)\n    // delegate to Vue native prop check\n    return toType<V>('oneOfType', {\n      type: nativeChecks,\n    })\n  }\n\n  return toType<V>('oneOfType', {\n    type: nativeChecks,\n    validator(value) {\n      const err: string[] = []\n      const valid = arr.some((type) => {\n        const t =\n          isVueTypeDef(type) && type._vueTypes_name === 'oneOf'\n            ? type.type || null\n            : type\n        const res = validateType(t, value, true)\n        if (typeof res === 'string') {\n          err.push(res)\n        }\n        return res === true\n      })\n      if (!valid) {\n        warn(\n          `oneOfType - provided value does not match any of the ${\n            err.length\n          } passed-in validators:\\n${indent(err.join('\\n'))}`,\n        )\n      }\n\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function arrayOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<InferType<T>[]>('arrayOf', {\n    type: Array,\n    validator(values: any[]) {\n      let vResult: string | boolean\n      const valid = values.every((value) => {\n        vResult = validateType(type, value, true)\n        return vResult === true\n      })\n      if (!valid) {\n        warn(`arrayOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { toType } from '../utils'\nimport { Constructor } from '../types'\n\nexport default function instanceOf<C extends Constructor>(\n  instanceConstructor: C,\n) {\n  return toType<InstanceType<C>>('instanceOf', {\n    type: instanceConstructor,\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function objectOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<{ [key: string]: InferType<T> }>('objectOf', {\n    type: Object,\n    validator(obj) {\n      let vResult: string | boolean\n      const valid = Object.keys(obj).every((key) => {\n        vResult = validateType(type, obj[key], true)\n        return vResult === true\n      })\n\n      if (!valid) {\n        warn(`objectOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { <PERSON>p, VueProp, VueType<PERSON>hape, VueTypeLooseShape } from '../types'\nimport { toType, validateType, warn, isPlainObject, indent } from '../utils'\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport default function shape<T extends object>(\n  obj: { [K in keyof T]: Prop<T[K]> | VueProp<T[K]> },\n): VueTypeShape<T> {\n  const keys = Object.keys(obj)\n  const requiredKeys = keys.filter((key) => !!(obj as any)[key]?.required)\n\n  const type = toType('shape', {\n    type: Object,\n    validator(this: VueTypeShape<T> | VueTypeLooseShape<T>, value) {\n      if (!isPlainObject(value)) {\n        return false\n      }\n      const valueKeys = Object.keys(value)\n\n      // check for required keys (if any)\n      if (\n        requiredKeys.length > 0 &&\n        requiredKeys.some((req) => valueKeys.indexOf(req) === -1)\n      ) {\n        const missing = requiredKeys.filter(\n          (req) => valueKeys.indexOf(req) === -1,\n        )\n        if (missing.length === 1) {\n          warn(`shape - required property \"${missing[0]}\" is not defined.`)\n        } else {\n          warn(\n            `shape - required properties \"${missing.join(\n              '\", \"',\n            )}\" are not defined.`,\n          )\n        }\n\n        return false\n      }\n\n      return valueKeys.every((key) => {\n        if (keys.indexOf(key) === -1) {\n          if ((this as VueTypeLooseShape<T>)._vueTypes_isLoose === true)\n            return true\n          warn(\n            `shape - shape definition does not include a \"${key}\" property. Allowed keys: \"${keys.join(\n              '\", \"',\n            )}\".`,\n          )\n          return false\n        }\n        const type = (obj as any)[key]\n        const valid = validateType(type, value[key], true)\n        if (typeof valid === 'string') {\n          warn(`shape - \"${key}\" property validation error:\\n ${indent(valid)}`)\n        }\n        return valid === true\n      })\n    },\n  }) as VueTypeShape<T>\n\n  Object.defineProperty(type, '_vueTypes_isLoose', {\n    writable: true,\n    value: false,\n  })\n\n  Object.defineProperty(type, 'loose', {\n    get() {\n      this._vueTypes_isLoose = true\n      return this\n    },\n  })\n\n  return type\n}\n", "import {\n  toType,\n  toValidableType,\n  validateType,\n  isArray,\n  isVueTypeDef,\n  has,\n  fromType,\n} from './utils'\n\nimport {\n  VueTypesDefaults,\n  ExtendProps,\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueTypeShape,\n  VueTypeLooseShape,\n} from './types'\nimport { typeDefaults } from './sensibles'\nimport { PropOptions } from './types'\n\nimport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n} from './validators/native'\nimport custom from './validators/custom'\nimport oneOf from './validators/oneof'\nimport oneOfType from './validators/oneoftype'\nimport arrayOf from './validators/arrayof'\nimport instanceOf from './validators/instanceof'\nimport objectOf from './validators/objectof'\nimport shape from './validators/shape'\n\nclass BaseVueTypes {\n  static defaults: Partial<VueTypesDefaults> = {}\n\n  static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n  static get any() {\n    return any()\n  }\n  static get func() {\n    return func().def(this.defaults.func)\n  }\n  static get bool() {\n    return bool().def(this.defaults.bool)\n  }\n  static get string() {\n    return string().def(this.defaults.string)\n  }\n  static get number() {\n    return number().def(this.defaults.number)\n  }\n  static get array() {\n    return array().def(this.defaults.array)\n  }\n  static get object() {\n    return object().def(this.defaults.object)\n  }\n  static get integer() {\n    return integer().def(this.defaults.integer)\n  }\n  static get symbol() {\n    return symbol()\n  }\n\n  static readonly custom = custom\n  static readonly oneOf = oneOf\n  static readonly instanceOf = instanceOf\n  static readonly oneOfType = oneOfType\n  static readonly arrayOf = arrayOf\n  static readonly objectOf = objectOf\n  static readonly shape = shape\n\n  static extend<T>(props: ExtendProps | ExtendProps[]): T {\n    if (isArray(props)) {\n      props.forEach((p) => this.extend(p))\n      return this as any\n    }\n\n    const { name, validate = false, getter = false, ...opts } = props\n\n    if (has(this, name as any)) {\n      throw new TypeError(`[VueTypes error]: Type \"${name}\" already defined`)\n    }\n\n    const { type } = opts\n    if (isVueTypeDef(type)) {\n      // we are using as base type a vue-type object\n\n      // detach the original type\n      // we are going to inherit the parent data.\n      delete opts.type\n\n      if (getter) {\n        return Object.defineProperty(this, name, {\n          get: () => fromType(name, type, opts as Omit<ExtendProps, 'type'>),\n        })\n      }\n      return Object.defineProperty(this, name, {\n        value(...args: unknown[]) {\n          const t = fromType(name, type, opts as Omit<ExtendProps, 'type'>)\n          if (t.validator) {\n            t.validator = t.validator.bind(t, ...args)\n          }\n          return t\n        },\n      })\n    }\n\n    let descriptor: PropertyDescriptor\n    if (getter) {\n      descriptor = {\n        get() {\n          const typeOptions = Object.assign({}, opts as PropOptions<T>)\n          if (validate) {\n            return toValidableType<T>(name, typeOptions)\n          }\n          return toType<T>(name, typeOptions)\n        },\n        enumerable: true,\n      }\n    } else {\n      descriptor = {\n        value(...args: T[]) {\n          const typeOptions = Object.assign({}, opts as PropOptions<T>)\n          let ret: VueTypeDef<T>\n          if (validate) {\n            ret = toValidableType<T>(name, typeOptions)\n          } else {\n            ret = toType<T>(name, typeOptions)\n          }\n\n          if (typeOptions.validator) {\n            ret.validator = typeOptions.validator.bind(ret, ...args)\n          }\n          return ret\n        },\n        enumerable: true,\n      }\n    }\n\n    return Object.defineProperty(this, name, descriptor)\n  }\n\n  static utils = {\n    validate<T, U>(value: T, type: U) {\n      return validateType<U, T>(type, value, true) === true\n    },\n    toType<T = unknown>(\n      name: string,\n      obj: PropOptions<T>,\n      validable = false,\n    ): VueTypeDef<T> | VueTypeValidableDef<T> {\n      return validable ? toValidableType<T>(name, obj) : toType<T>(name, obj)\n    },\n  }\n}\n\nfunction createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\nexport default class VueTypes extends createTypes() {}\n\nexport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  custom,\n  oneOf,\n  oneOfType,\n  arrayOf,\n  instanceOf,\n  objectOf,\n  shape,\n  createTypes,\n  toType,\n  toValidableType,\n  validateType,\n  fromType,\n}\n\nexport type VueTypesInterface = ReturnType<typeof createTypes>\nexport { VueTypeDef, VueTypeValidableDef, VueTypeShape, VueTypeLooseShape }\n", "import { VueTypesDefaults } from './types'\n\nexport const typeDefaults = (): VueTypesDefaults => ({\n  func: () => undefined,\n  bool: true,\n  string: '',\n  number: 0,\n  array: () => [],\n  object: () => ({}),\n  integer: 0,\n})\n", "import { createTypes } from 'vue-types';\nconst PropTypes = createTypes({\n  func: undefined,\n  bool: undefined,\n  string: undefined,\n  number: undefined,\n  array: undefined,\n  object: undefined,\n  integer: undefined\n});\nPropTypes.extend([{\n  name: 'looseBool',\n  getter: true,\n  type: Boolean,\n  default: undefined\n}, {\n  name: 'style',\n  getter: true,\n  type: [String, Object],\n  default: undefined\n}, {\n  name: 'VueNode',\n  getter: true,\n  type: null\n}]);\nexport function withUndefined(type) {\n  type.default = undefined;\n  return type;\n}\nexport default PropTypes;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { inject, provide, reactive, watchEffect } from 'vue';\nfunction createContext(defaultValue) {\n  const contextKey = Symbol('contextKey');\n  const useProvide = (props, newProps) => {\n    const mergedProps = reactive({});\n    provide(contextKey, mergedProps);\n    watchEffect(() => {\n      _extends(mergedProps, props, newProps || {});\n    });\n    return mergedProps;\n  };\n  const useInject = () => {\n    return inject(contextKey, defaultValue) || {};\n  };\n  return {\n    useProvide,\n    useInject\n  };\n}\nexport default createContext;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,SAASA,EAAeC,IAAAA;AACtB,SAAuB,MAXT,SADEC,KAYAD,OAXqB,YAAA,OAARC,MAAAA,UAAoBC,MAAMC,QAAQF,EAAAA,MAYpB,sBAAtCG,OAAOC,UAAUC,SAASC,KAAKP,EAAAA;AAbtC,MAAkBC;AAAAA;ACElB,IAAMO,IAAWJ,OAAOC;AAAxB,IACMC,IAAWE,EAASF;AAD1B,IAEaG,IAASD,EAASE;AAF/B,IAIMC,IAAkB;AAGxB,SAAgBC,EACdC,IAAAA;AAAAA,MAAAA,IAEMC,KAAAA,UAAAA,KAAQD,QAAAA,KAAAA,SAAAA,GAAqBC,SAAAA,WAAAA,KAAAA,KAAQD;AAC3C,MAAIC,IAAM;AACR,QAAMC,KAAQD,GAAKR,SAAAA,EAAWS,MAAMJ,CAAAA;AACpC,WAAOI,KAAQA,GAAM,CAAA,IAAK;EAAA;AAE5B,SAAO;AAAA;AAAA,IAUIC,IDXb,SAAuBhB,IAAAA;AACrB,MAAIiB,IAAKC;AAET,SAAA,UAAInB,EAAeC,EAAAA,KAIC,cAAA,QADpBiB,KAAOjB,GAAEmB,gBAAAA,UAKLpB,EADJmB,KAAOD,GAAKZ,SAAAA,KAAAA,UAIRa,GAAKR,eAAe,eAAA;AAAA;ACbjB,IAuBIU,IAAW,SAACC,IAAAA;AAAAA,SAAaA;AAAAA;AAvB7B,IAyBLC,IAAuCF;AAE3C,IAA6B,MAAc;AACnCG,MAAgC,eAAA,OAAZC;AAC1BF,MAAOC,IACH,SAAcE,IAAAA;AAEZD,YAAQF,KAAAA,sBAAyBG,EAAAA;EAAAA,IAEnCL;AAAAA;AANEG;AAMFH,IAWOM,IAAM,SAAmCC,IAAQC,IAAAA;AAAAA,SAC5DnB,EAAOF,KAAKoB,IAAKC,EAAAA;AAAAA;AAZbR,IAqBOS,IACXC,OAAOD,aACP,SAAmBE,IAAAA;AACjB,SACmB,YAAA,OAAVA,MACPC,SAASD,EAAAA,KACTE,KAAKC,MAAMH,EAAAA,MAAWA;AAAAA;AA3BtBX,IAqCOjB,IACXD,MAAMC,WACN,SAAiB4B,IAAAA;AACf,SAAgC,qBAAzBzB,EAASC,KAAKwB,EAAAA;AAAAA;AAxCnBX,IAkDOe,IAAa,SAACJ,IAAAA;AAAAA,SACA,wBAAzBzB,EAASC,KAAKwB,EAAAA;AAAAA;AAnDVX,IAyDOgB,IAAe,SAC1BL,IAAAA;AAAAA,SAEAf,EAAce,EAAAA,KAAUL,EAAIK,IAAO,gBAAA;AAAA;AA5D/BX,IAkEOiB,IAAgB,SAAIN,IAAAA;AAAAA,SAC/Bf,EAAce,EAAAA,MACbL,EAAIK,IAAO,MAAA,KACV,CAAC,kBAAkB,aAAa,WAAW,UAAA,EAAYO,KAAK,SAACC,IAAAA;AAAAA,WAC3Db,EAAIK,IAAOQ,EAAAA;EAAAA,CAAAA;AAAAA;AAcjB,SAAgBC,EAAO3B,IAA6B4B,IAAAA;AAClD,SAAOrC,OAAOsC,eAAe7B,GAAG8B,KAAKF,EAAAA,GAAM,cAAc,EACvDV,OAAOlB,GAAAA,CAAAA;AAAAA;AAyBX,SAAgB+B,EACd9B,IACAiB,IACAc,IAAAA;AAEA,MAAIC;AAAAA,aAFJD,OAAAA,KAAAA;AAGA,MAAIE,KAAAA,MACAC,KAAe;AAIjBF,EAAAA,KAHG9B,EAAcF,EAAAA,IAGHA,KAFA,EAAEA,MAAAA,GAAAA;AAIlB,MAAMmC,KAAab,EAAaU,EAAAA,IAC5BA,GAAYI,iBAAiB,QAC7B;AAEJ,MAAIb,EAAcS,EAAAA,KAAqC,SAArBA,GAAYhC,MAAe;AAC3D,QAAA,WAAIgC,GAAYhC,QAAAA,SAAsBgC,GAAYhC,KAChD,QAAOiC;AAET,QAAA,CAAKD,GAAYK,YAAAA,WAAYpB,GAC3B,QAAOgB;AAEL5C,MAAQ2C,GAAYhC,IAAAA,KACtBiC,KAAQD,GAAYhC,KAAKwB,KACvB,SAACxB,IAAAA;AAAAA,aAAAA,SAAc8B,EAAa9B,IAAMiB,IAAAA,IAAO;IAAA,CAAA,GAE3CiB,KAAeF,GAAYhC,KACxBsC,IAAI,SAACtC,IAAAA;AAAAA,aAAcF,EAAQE,EAAAA;IAAAA,CAAAA,EAC3BuC,KAAK,MAAA,KAKNN,KADmB,aAFrBC,KAAepC,EAAQkC,EAAAA,KAGb3C,EAAQ4B,EAAAA,IACU,aAAjBiB,KACDhC,EAAce,EAAAA,IAEL,aAAjBiB,MACiB,aAAjBA,MACiB,cAAjBA,MACiB,eAAjBA,KAAAA,SAxLsBjB,IAAAA;AAC5B,UAAIA,QAAAA,GAAuC,QAAO;AAClD,UAAMhB,KAAQgB,GAAMZ,YAAYb,SAAAA,EAAWS,MAAMJ,CAAAA;AACjD,aAAOI,KAAQA,GAAM,CAAA,IAAK;IAAA,EAuLEgB,EAAAA,MAAWiB,KAEzBjB,cAAiBe,GAAYhC;EAAAA;AAK3C,MAAA,CAAKiC,IAAO;AACV,QAAMtB,KAASwB,KAAAA,YAAoBlB,KAAAA,0BAA6BiB,KAAAA;AAChE,WAAA,UAAIH,MACFvB,EAAKG,EAAAA,GAAAA,SAGAA;EAAAA;AAGT,MAAIC,EAAIoB,IAAa,WAAA,KAAgBX,EAAWW,GAAYQ,SAAAA,GAAY;AACtE,QAAMC,KAAUjC,GACVkC,KAAU,CAAA;AAQhB,QAPAlC,IAAO,SAACG,IAAAA;AACN+B,MAAAA,GAAQC,KAAKhC,EAAAA;IAAAA,GAGfsB,KAAQD,GAAYQ,UAAUvB,EAAAA,GAC9BT,IAAOiC,IAAAA,CAEFR,IAAO;AACV,UAAMtB,KAAO+B,GAAQE,SAAS,IAAI,OAAO,MAAMF,GAAQH,KAAK,MAAA;AAE5D,aADAG,GAAQE,SAAS,GAAA,UACbb,MACFvB,EAAKG,CAAAA,GACEsB,MAEFtB;IAAAA;EAAAA;AAGX,SAAOsB;AAAAA;AAAAA,SASOY,EAAgBC,IAAcjC,IAAAA;AAC5C,MAAMb,KAAsBV,OAAOyD,iBAAiBlC,IAAK,EACvDuB,gBAAgB,EACdnB,OAAO6B,IACPE,UAAAA,KAAU,GAEZC,YAAY,EACVC,KAAAA,WAAAA;AAEE,WADAC,KAAKd,WAAAA,MAAW;EAAA,EAAA,GAIpBe,KAAK,EACHnC,OAAAA,SAAMmC,IAAAA;AACJ,WAAA,WAAIA,MAAsBD,KAAKE,UAG1BhC,EAAW+B,EAAAA,KAAAA,SAAQtB,EAAaqB,MAAMC,IAAAA,IAAK,KAK9CD,KAAKE,UADHhE,EAAQ+D,EAAAA,IACK,WAAA;AAAA,aAAA,CAAA,EAAA,OAAUA,EAAAA;IAAAA,IAChBlD,EAAckD,EAAAA,IACR,WAAA;AAAA,aAAM9D,OAAOgE,OAAO,CAAA,GAAIF,EAAAA;IAAAA,IAExBA,IAAAA,SARf5C,EAAQ2C,KAAKf,iBAAAA,gCAA4CgB,KAAAA,GAAAA,GAAAA,QAAAA;EAAAA,EAAAA,EAAAA,CAAAA,GAezDZ,KAAcxC,GAAdwC;AAKR,SAJInB,EAAWmB,EAAAA,MACbxC,GAAKwC,YAAYd,EAAOc,IAAWxC,EAAAA,IAG9BA;AAAAA;AAAAA,SASOuD,EAAyBT,IAAcjC,IAAAA;AACrD,MAAMb,KAAO6C,EAAUC,IAAMjC,EAAAA;AAC7B,SAAOvB,OAAOsC,eAAe5B,IAAM,YAAY,EAC7CiB,OAAAA,SAAMlB,IAAAA;AAWJ,WAVIsB,EAAW8B,KAAKX,SAAAA,KAClBhC,EAEI2C,KAAKf,iBAAAA,mGAC0FoB,KAAKC,UACpGN,IAAAA,CAAAA,GAINA,KAAKX,YAAYd,EAAO3B,IAAIoD,IAAAA,GAAAA;EAAAA,EAAAA,CAAAA;AAAAA;AAmClC,SAAgBO,EAGdZ,IAAca,IAAWC,IAAAA;AAEzB,MA5BsC/C,IAChCgD,IA2BAC,MA5BgCjD,KA4BnB8C,IA3BbE,KAAc,CAAA,GACpBvE,OAAOyE,oBAAoBlD,EAAAA,EAAKmD,QAAQ,SAACC,IAAAA;AACvCJ,IAAAA,GAAYI,EAAAA,IAAkB3E,OAAO4E,yBAAyBrD,IAAKoD,EAAAA;EAAAA,CAAAA,GAE9D3E,OAAOyD,iBAAiB,CAAA,GAAIc,EAAAA;AA4BnC,MAFAC,GAAK1B,iBAAiBU,IAAAA,CAEjB5C,EAAc0D,EAAAA,EACjB,QAAOE;AAAAA,MAjN4C/D,IAAAA,IAmN7CyC,KAAuBoB,GAAvBpB,WAAc2B,KAAAA,EAASP,IAAAA,CAAAA,WAAAA,CAAAA;AAK/B,MAAIvC,EAAWmB,EAAAA,GAAY;AAAA,QACR4B,KAAkBN,GAA7BtB;AAEF4B,IAAAA,OACFA,KAAAA,UAAAA,MA5NiDrE,KA4N1BqE,IA3NFC,eAAAA,WAAAA,KAAAA,KAActE,KA8NrC+D,GAAKtB,YAAYd,EACf0C,KACI,SAAmBnD,IAAAA;AACjB,aACEmD,GAAc3E,KAAK0D,MAAMlC,EAAAA,KAAUuB,GAAU/C,KAAK0D,MAAMlC,EAAAA;IAAAA,IAG5DuB,IACJsB,EAAAA;EAAAA;AAIJ,SAAOxE,OAAOgE,OAAOQ,IAAMK,EAAAA;AAAAA;AAAAA,SAGbG,EAAOC,IAAAA;AACrB,SAAOA,GAAOC,QAAQ,eAAe,IAAA;AAAA;AAAA,ICvY1BC,IAAM,WAAA;AAAA,SAAMlB,EAAgB,OAAO,CAAA,CAAA;AAAA;ADuYT,ICrY1BmB,IAAO,WAAA;AAAA,SAClBnB,EAAmB,YAAY,EAC7BvD,MAAM2E,SAAAA,CAAAA;AAAAA;ADmY6B,IChY1BC,IAAO,WAAA;AAAA,SAClBrB,EAAgB,WAAW,EACzBvD,MAAM6E,QAAAA,CAAAA;AAAAA;AD8X6B,IC3X1BN,IAAS,WAAA;AAAA,SACpBhB,EAAgB,UAAU,EACxBvD,MAAM8E,OAAAA,CAAAA;AAAAA;ADyX6B,ICtX1BC,IAAS,WAAA;AAAA,SACpBxB,EAAgB,UAAU,EACxBvD,MAAMgB,OAAAA,CAAAA;AAAAA;ADoX6B,ICjX1BgE,IAAQ,WAAA;AAAA,SACnBzB,EAAqB,SAAS,EAC5BvD,MAAMZ,MAAAA,CAAAA;AAAAA;AD+W6B,IC5W1B6F,IAAS,WAAA;AAAA,SACpB1B,EAAmB,UAAU,EAC3BvD,MAAMV,OAAAA,CAAAA;AAAAA;AD0W6B,ICvW1B4F,IAAU,WAAA;AAAA,SACrBrC,EAAO,WAAW,EAChB7C,MAAMgB,QACNwB,WAAAA,SAAUvB,IAAAA;AACR,WAAOF,EAAUE,EAAAA;EAAAA,EAAAA,CAAAA;AAAAA;ADmWgB,IC/V1BkE,IAAS,WAAA;AAAA,SACpBtC,EAAe,UAAU,EACvBL,WAAAA,SAAUvB,IAAAA;AACR,WAAwB,YAAA,OAAVA;EAAAA,EAAAA,CAAAA;AAAAA;AAAAA,SC3CImE,EACtBC,IACAC,IAAAA;AAEA,MAAA,WAFAA,OAAAA,KAAU,6BAEiB,cAAA,OAAhBD,GACT,OAAA,IAAUE,UACR,2DAAA;AAIJ,SAAO1C,EAAUwC,GAAYvC,QAAQ,0BAA0B,EAC7DN,WAAAA,SAA+BvB,IAAAA;AAC7B,QAAMgB,KAAQoD,GAAYpE,EAAAA;AAE1B,WADKgB,MAAOzB,EAAQ2C,KAAKf,iBAAAA,QAAoBkD,EAAAA,GACtCrD;EAAAA,EAAAA,CAAAA;AAAAA;AAAAA,SCdWuD,EAAgCC,IAAAA;AACtD,MAAA,CAAKpG,EAAQoG,EAAAA,EACX,OAAA,IAAUF,UACR,0DAAA;AAGJ,MAAM5E,KAAAA,qCAAyC8E,GAAIlD,KAAK,MAAA,IAAA,MAClDmD,KAAeD,GAAIE,OAAO,SAACC,IAAKC,IAAAA;AACpC,QAAIA,QAAAA,IAA+B;AACjC,UAAMC,KAAUD,GAAUxF;AAAAA,aAC1BuF,GAAIG,QAAQD,EAAAA,KAAkBF,GAAIjD,KAAKmD,EAAAA;IAAAA;AAEzC,WAAOF;EAAAA,GACN,CAAA,CAAA;AAEH,SAAO/C,EAAkB,SAAS,EAChC7C,MAAM0F,GAAa9C,SAAS,IAAI8C,KAAAA,QAChClD,WAAAA,SAAUvB,IAAAA;AACR,QAAMgB,KAAAA,OAAQwD,GAAIM,QAAQ9E,EAAAA;AAE1B,WADKgB,MAAOzB,EAAKG,EAAAA,GACVsB;EAAAA,EAAAA,CAAAA;AAAAA;AAAAA,SCXW+D,EAGtBP,IAAAA;AACA,MAAA,CAAKpG,EAAQoG,EAAAA,EACX,OAAA,IAAUF,UACR,yDAAA;AAQJ,WAJIU,KAAAA,OAEAC,KAA0B,CAAA,GAErBC,KAAI,GAAGA,KAAIV,GAAI7C,QAAQuD,MAAK,GAAG;AACtC,QAAMnG,KAAOyF,GAAIU,EAAAA;AACjB,QAAI5E,EAAiBvB,EAAAA,GAAO;AAC1B,UAAIsB,EAAgBtB,EAAAA,KAAiC,YAAxBA,GAAKoC,gBAA4B;AAC5D8D,QAAAA,KAAeA,GAAaE,OAAOpG,GAAKA,IAAAA;AACxC;MAAA;AAKF,UAHIqB,EAAWrB,GAAKwC,SAAAA,MAClByD,KAAAA,OAAsB,SAEpBjG,GAAKA,QAAiBA,GAAKA,MAAM;AACnCkG,QAAAA,KAAeA,GAAaE,OAAOpG,GAAKA,IAAAA;AACxC;MAAA;IAAA;AAGJkG,IAAAA,GAAavD,KAAK3C,EAAAA;EAAAA;AAMpB,SAFAkG,KAAeA,GAAaG,OAAO,SAACC,IAAGH,IAAAA;AAAAA,WAAMD,GAAaH,QAAQO,EAAAA,MAAOH;EAAAA,CAAAA,GAUlEtD,EAAU,aARZoD,KAQyB,EAC5BjG,MAAMkG,IACN1D,WAAAA,SAAUvB,IAAAA;AACR,QAAMsF,KAAgB,CAAA,GAChBtE,KAAQwD,GAAIjE,KAAK,SAACxB,IAAAA;AACtB,UAIMwG,KAAM1E,EAHVR,EAAatB,EAAAA,KAAiC,YAAxBA,GAAKoC,iBACvBpC,GAAKA,QAAQ,OACbA,IACsBiB,IAAAA,IAAO;AAInC,aAHmB,YAAA,OAARuF,MACTD,GAAI5D,KAAK6D,EAAAA,GAAAA,SAEJA;IAAAA,CAAAA;AAUT,WARKvE,MACHzB,EAAAA,0DAEI+F,GAAI3D,SAAAA,6BACqB0B,EAAOiC,GAAIhE,KAAK,IAAA,CAAA,CAAA,GAIxCN;EAAAA,EAAAA,IA5BqB,EAC5BjC,MAAMkG,GAAAA,CAAAA;AAAAA;AAAAA,SChDYO,EAA4CzG,IAAAA;AAClE,SAAO6C,EAAuB,WAAW,EACvC7C,MAAMZ,OACNoD,WAAAA,SAAUkE,IAAAA;AACR,QAAIC,IACE1E,KAAQyE,GAAOE,MAAM,SAAC3F,IAAAA;AAE1B,aAAA,UADA0F,KAAU7E,EAAa9B,IAAMiB,IAAAA,IAAO;IAAA,CAAA;AAMtC,WAHKgB,MACHzB,EAAAA,wCAA2C8D,EAAOqC,EAAAA,CAAAA,GAE7C1E;EAAAA,EAAAA,CAAAA;AAAAA;AAAAA,SCZW4E,EACtBC,IAAAA;AAEA,SAAOjE,EAAwB,cAAc,EAC3C7C,MAAM8G,GAAAA,CAAAA;AAAAA;AAAAA,SCJcC,EAA6C/G,IAAAA;AACnE,SAAO6C,EAAwC,YAAY,EACzD7C,MAAMV,QACNkD,WAAAA,SAAU3B,IAAAA;AACR,QAAI8F,IACE1E,KAAQ3C,OAAO0H,KAAKnG,EAAAA,EAAK+F,MAAM,SAAC3C,IAAAA;AAEpC,aAAA,UADA0C,KAAU7E,EAAa9B,IAAMa,GAAIoD,EAAAA,GAAAA,IAAM;IAAA,CAAA;AAOzC,WAHKhC,MACHzB,EAAAA,yCAA4C8D,EAAOqC,EAAAA,CAAAA,GAE9C1E;EAAAA,EAAAA,CAAAA;AAAAA;AAAAA,SCZWgF,EACtBpG,IAAAA;AAEA,MAAMmG,KAAO1H,OAAO0H,KAAKnG,EAAAA,GACnBqG,KAAeF,GAAKX,OAAO,SAACpC,IAAAA;AAAAA,QAAAA;AAAAA,WAAAA,CAAAA,EAAAA,UAAAA,KAAWpD,GAAYoD,EAAAA,MAAAA,WAAAA,KAAAA,SAAZkD,GAAkB9E;EAAAA,CAAAA,GAEzDrC,KAAO6C,EAAO,SAAS,EAC3B7C,MAAMV,QACNkD,WAAAA,SAAwDvB,IAAAA;AAAAA,QAAAA,KAAAA;AACtD,QAAA,CAAKf,EAAce,EAAAA,EACjB,QAAA;AAEF,QAAMmG,KAAY9H,OAAO0H,KAAK/F,EAAAA;AAG9B,QACEiG,GAAatE,SAAS,KACtBsE,GAAa1F,KAAK,SAAC6F,IAAAA;AAAAA,aAAAA,OAAQD,GAAUrB,QAAQsB,EAAAA;IAAAA,CAAAA,GAC7C;AACA,UAAMC,KAAUJ,GAAab,OAC3B,SAACgB,IAAAA;AAAAA,eAAAA,OAAQD,GAAUrB,QAAQsB,EAAAA;MAAAA,CAAAA;AAY7B,aATE7G,EADqB,MAAnB8G,GAAQ1E,SAAAA,gCACyB0E,GAAQ,CAAA,IAAA,sBAAA,kCAGTA,GAAQ/E,KACtC,MAAA,IAAA,oBAAA,GAAA;IAAA;AAQR,WAAO6E,GAAUR,MAAM,SAAC3C,IAAAA;AACtB,UAAA,OAAI+C,GAAKjB,QAAQ9B,EAAAA,EACf,QAAA,SAAKsD,GAA8BC,sBAEnChH,EAAAA,kDACkDyD,KAAAA,gCAAiC+C,GAAKzE,KACpF,MAAA,IAAA,IAAA,GAAA;AAKN,UACMN,KAAQH,EADAjB,GAAYoD,EAAAA,GACOhD,GAAMgD,EAAAA,GAAAA,IAAM;AAI7C,aAHqB,YAAA,OAAVhC,MACTzB,EAAAA,cAAiByD,KAAAA,oCAAqCK,EAAOrC,EAAAA,CAAAA,GAAAA,SAExDA;IAAAA,CAAAA;EAAAA,EAAAA,CAAAA;AAiBb,SAZA3C,OAAOsC,eAAe5B,IAAM,qBAAqB,EAC/CgD,UAAAA,MACA/B,OAAAA,MAAO,CAAA,GAGT3B,OAAOsC,eAAe5B,IAAM,SAAS,EACnCkD,KAAAA,WAAAA;AAEE,WADAC,KAAKqE,oBAAAA,MAAoB;EAAA,EAAA,CAAA,GAKtBxH;AAAAA;AAAAA,IChCHyH,IAAAA,WAAAA;AAAAA,WAAAA,KAAAA;EAAAA;AAAAA,SAAAA,GAyCGC,SAAP,SAAiB9D,IAAAA;AAAAA,QAAAA,KAAAA;AACf,QAAIvE,EAAQuE,EAAAA,EAEV,QADAA,GAAMI,QAAQ,SAAC2D,IAAAA;AAAAA,aAAMJ,GAAKG,OAAOC,EAAAA;IAAAA,CAAAA,GAAAA;AAAAA,QAI3B7E,KAAoDc,GAApDd,MAAAA,KAAoDc,GAA9CgE,UAAAA,KAAAA,WAAAA,MAAAA,IAAAA,KAA8ChE,GAA5BiE,QAAAA,KAAAA,WAAAA,MAAAA,IAAmBC,KAAAA,EAASlE,IAAAA,CAAAA,QAAAA,YAAAA,QAAAA,CAAAA;AAE5D,QAAIhD,EAAIuC,MAAML,EAAAA,EACZ,OAAA,IAAUyC,UAAAA,6BAAqCzC,KAAAA,mBAAAA;AAAAA,QA2B7CiF,IAxBI/H,KAAS8H,GAAT9H;AACR,WAAIsB,EAAatB,EAAAA,KAAAA,OAKR8H,GAAK9H,MAGHV,OAAOsC,eAAeuB,MAAML,IADjC+E,KACuC,EACvC3E,KAAK,WAAA;AAAA,aAAMQ,EAASZ,IAAM9C,IAAM8H,EAAAA;IAAAA,EAAAA,IAGK,EACvC7G,OAAAA,WAAAA;AACE,UAAA+G,IAAM1B,KAAI5C,EAASZ,IAAM9C,IAAM8H,EAAAA;AAI/B,aAHIxB,GAAE9D,cACJ8D,GAAE9D,aAAAA,KAAY8D,GAAE9D,WAAUX,KAAAA,MAAAA,IAAAA,CAAKyE,EAAAA,EAAAA,OAAAA,CAAAA,EAAAA,MAAAA,KAAAA,SAAAA,CAAAA,CAAAA,IAE1BA;IAAAA,EAAAA,CAAAA,MAOXyB,KADEF,KACW,EACX3E,KAAAA,WAAAA;AACE,UAAM+E,KAAc3I,OAAOgE,OAAO,CAAA,GAAIwE,EAAAA;AACtC,aAAIF,KACKrE,EAAmBT,IAAMmF,EAAAA,IAE3BpF,EAAUC,IAAMmF,EAAAA;IAAAA,GAEzBC,YAAAA,KAAY,IAGD,EACXjH,OAAAA,WAAAA;AACE,UACI2E,IAAAA,IADEqC,KAAc3I,OAAOgE,OAAO,CAAA,GAAIwE,EAAAA;AAWtC,aARElC,KADEgC,KACIrE,EAAmBT,IAAMmF,EAAAA,IAEzBpF,EAAUC,IAAMmF,EAAAA,GAGpBA,GAAYzF,cACdoD,GAAIpD,aAAAA,KAAYyF,GAAYzF,WAAUX,KAAAA,MAAAA,IAAAA,CAAK+D,EAAAA,EAAAA,OAAAA,CAAAA,EAAAA,MAAAA,KAAAA,SAAAA,CAAAA,CAAAA,IAEtCA;IAAAA,GAETsC,YAAAA,KAAY,GAIT5I,OAAOsC,eAAeuB,MAAML,IAAMiF,EAAAA;EAAAA,GAAAA,EAAAA,IAAAA,MAAAA,CAAAA,EAAAA,KAAAA,OAAAA,KAAAA,WAAAA;AAvGzC,WAAOtD,EAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,QAAAA,KAAAA,WAAAA;AAGP,WAAOC,EAAAA,EAAOtB,IAAID,KAAKgF,SAASzD,IAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,QAAAA,KAAAA,WAAAA;AAGhC,WAAOE,EAAAA,EAAOxB,IAAID,KAAKgF,SAASvD,IAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,UAAAA,KAAAA,WAAAA;AAGhC,WAAOL,EAAAA,EAASnB,IAAID,KAAKgF,SAAS5D,MAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,UAAAA,KAAAA,WAAAA;AAGlC,WAAOQ,EAAAA,EAAS3B,IAAID,KAAKgF,SAASpD,MAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,SAAAA,KAAAA,WAAAA;AAGlC,WAAOC,EAAAA,EAAQ5B,IAAID,KAAKgF,SAASnD,KAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,UAAAA,KAAAA,WAAAA;AAGjC,WAAOC,EAAAA,EAAS7B,IAAID,KAAKgF,SAASlD,MAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,WAAAA,KAAAA,WAAAA;AAGlC,WAAOC,EAAAA,EAAU9B,IAAID,KAAKgF,SAASjD,OAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,UAAAA,KAAAA,WAAAA;AAGnC,WAAOC,EAAAA;EAAAA,EAAAA,CAAAA,CAAAA,GAAAA;AAAAA,EAAAA;AAgGX,SAASiD,EAAYC,IAAAA;AAAAA,MAAAA;AACnB,SAAA,WADmBA,OAAAA,KCpKgC,EACnD3D,MAAM,WAAA;EAAA,GACNE,MAAAA,MACAL,QAAQ,IACRQ,QAAQ,GACRC,OAAO,WAAA;AAAA,WAAM,CAAA;EAAA,GACbC,QAAQ,WAAA;AAAA,WAAO,CAAA;EAAA,GACfC,SAAS,EAAA,KAAAiB,KAAA,SAAAA,IAAA;AAAA,aAAAjH,KAAA;AAAA,aAAAiH,GAAA,MAAA,MAAA,SAAA,KAAA;IAAA;AAAA,WAAA,EAAAjH,IAAAiH,EAAA,GAAA,EAAAjH,IAAA,MAAA,CAAA,EAAA,KAAA,oBAAA,KAAA,WAAA;ADkKL,aAAA,EAAA,CAAA,GAAYiE,KAAKgF,QAAAA;IAAAA,GAAAA,KAAAA,SAGStC,IAAAA;AAS1B1C,WAAKgF,WAAAA,UARDtC,KAAAA,EAAAA,CAAAA,GAAAA,SAIAA,KAIiBA,KAHEwC,EAAAA,IAJL,CAAA;IAAA,EAAA,CAAA,CAAA,GAAAnJ;EAAA,EATDuI,CAAAA,GAAAA,WAAAA,EAAAA,CAAAA,GAC+BY,EAAAA,GAAAA;AAAAA;AA/H7CZ,EAAAA,WAAsC,CAAA,GAgC7BA,EAAAA,SAASrC,GACTqC,EAAAA,QAAQjC,GACRiC,EAAAA,aAAaZ,GACbY,EAAAA,YAAYzB,GACZyB,EAAAA,UAAUhB,GACVgB,EAAAA,WAAWV,GACXU,EAAAA,QAAQR,GAyEjBQ,EAAAA,QAAQ,EACbG,UAAAA,SAAe3G,IAAUjB,IAAAA;AACvB,SAAA,SAAO8B,EAAmB9B,IAAMiB,IAAAA,IAAO;AAAA,GAEzC4B,QAAAA,SACEC,IACAjC,IACAyH,IAAAA;AAEA,SAAA,WAFAA,OAAAA,KAAAA,QAEOA,KAAY/E,EAAmBT,IAAMjC,EAAAA,IAAOgC,EAAUC,IAAMjC,EAAAA;AAAAA,EAAAA;AAAAA,IA2BpD0H,IAAAA,SAAAA,IAAAA;AAAAA,WAAAA,KAAAA;AAAAA,WAAAA,GAAAA,MAAAA,MAAAA,SAAAA,KAAAA;EAAAA;AAAAA,SAAAA,EAAAA,IAAAA,EAAAA,GAAAA;AAAAA,EAAiBH,EAAAA,CAAAA;;;AE3LtC,IAAM,YAAY,EAAY;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACX,CAAC;AACD,UAAU,OAAO,CAAC;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM,CAAC,QAAQ,MAAM;AAAA,EACrB,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AACR,CAAC,CAAC;AACK,SAAS,cAAc,MAAM;AAClC,OAAK,UAAU;AACf,SAAO;AACT;AACA,IAAO,oBAAQ;;;AC3Bf,SAAS,cAAc,cAAc;AACnC,QAAM,aAAa,OAAO,YAAY;AACtC,QAAM,aAAa,CAAC,OAAO,aAAa;AACtC,UAAM,cAAc,SAAS,CAAC,CAAC;AAC/B,YAAQ,YAAY,WAAW;AAC/B,gBAAY,MAAM;AAChB,eAAS,aAAa,OAAO,YAAY,CAAC,CAAC;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,YAAY,MAAM;AACtB,WAAO,OAAO,YAAY,YAAY,KAAK,CAAC;AAAA,EAC9C;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,wBAAQ;", "names": ["isObjectObject", "o", "val", "Array", "isArray", "Object", "prototype", "toString", "call", "Obj<PERSON><PERSON><PERSON>", "hasOwn", "hasOwnProperty", "FN_MATCH_REGEXP", "getType", "fn", "type", "match", "isPlainObject", "ctor", "prot", "constructor", "identity", "arg", "warn", "hasConsole", "console", "msg", "has", "obj", "prop", "isInteger", "Number", "value", "isFinite", "Math", "floor", "isFunction", "isVueTypeDef", "isComplexType", "some", "k", "bindTo", "ctx", "defineProperty", "bind", "validateType", "silent", "typeToCheck", "valid", "expectedType", "namePrefix", "_vueTypes_name", "required", "map", "join", "validator", "old<PERSON>arn", "warnLog", "push", "length", "toType", "name", "defineProperties", "writable", "isRequired", "get", "this", "def", "default", "assign", "toValidableType", "JSON", "stringify", "fromType", "source", "props", "descriptors", "copy", "getOwnPropertyNames", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "rest", "prevValidator", "__original", "indent", "string", "replace", "any", "func", "Function", "bool", "Boolean", "String", "number", "array", "object", "integer", "symbol", "custom", "validatorFn", "warnMsg", "TypeError", "oneOf", "arr", "allowedTypes", "reduce", "ret", "v", "constr", "indexOf", "oneOfType", "hasCustomValidators", "nativeChecks", "i", "concat", "filter", "t", "err", "res", "arrayOf", "values", "vResult", "every", "instanceOf", "instanceConstructor", "objectOf", "keys", "shape", "requiredKeys", "_obj$key", "valueKeys", "req", "missing", "_this", "_vueTypes_isLoose", "BaseVueTypes", "extend", "p", "validate", "getter", "opts", "descriptor", "e", "typeOptions", "enumerable", "defaults", "createTypes", "defs", "validable", "VueTypes"]}