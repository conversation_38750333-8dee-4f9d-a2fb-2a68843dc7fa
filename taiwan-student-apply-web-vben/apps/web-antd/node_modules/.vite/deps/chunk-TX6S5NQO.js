import {
  wave_default
} from "./chunk-ORCF5DNG.js";
import {
  LoadingOutlined_default
} from "./chunk-VKA2T2JB.js";
import {
  KeyCode_default
} from "./chunk-KFBVPRL2.js";
import {
  omit_default
} from "./chunk-ITH5DYXO.js";
import {
  useInjectFormItemContext
} from "./chunk-HUL2W67K.js";
import {
  vue_types_default
} from "./chunk-NO4XDY4U.js";
import {
  _objectSpread2,
  genComponentStyleHook,
  genFocusStyle,
  getPropsSlot,
  merge,
  resetComponent,
  tuple,
  useConfigInject_default,
  useInjectDisabled,
  warning_default2 as warning_default,
  withInstall
} from "./chunk-7LCWIOMH.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  TinyColor
} from "./chunk-DOLVIAJN.js";
import {
  computed,
  createVNode,
  defineComponent,
  nextTick,
  onBeforeMount,
  onMounted,
  ref,
  watch
} from "./chunk-ZLVVKZUX.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/switch/style/index.js
var genSwitchSmallStyle = (token) => {
  const {
    componentCls
  } = token;
  const switchInnerCls = `${componentCls}-inner`;
  return {
    [componentCls]: {
      [`&${componentCls}-small`]: {
        minWidth: token.switchMinWidthSM,
        height: token.switchHeightSM,
        lineHeight: `${token.switchHeightSM}px`,
        [`${componentCls}-inner`]: {
          paddingInlineStart: token.switchInnerMarginMaxSM,
          paddingInlineEnd: token.switchInnerMarginMinSM,
          [`${switchInnerCls}-checked`]: {
            marginInlineStart: `calc(-100% + ${token.switchPinSizeSM + token.switchPadding * 2}px - ${token.switchInnerMarginMaxSM * 2}px)`,
            marginInlineEnd: `calc(100% - ${token.switchPinSizeSM + token.switchPadding * 2}px + ${token.switchInnerMarginMaxSM * 2}px)`
          },
          [`${switchInnerCls}-unchecked`]: {
            marginTop: -token.switchHeightSM,
            marginInlineStart: 0,
            marginInlineEnd: 0
          }
        },
        [`${componentCls}-handle`]: {
          width: token.switchPinSizeSM,
          height: token.switchPinSizeSM
        },
        [`${componentCls}-loading-icon`]: {
          top: (token.switchPinSizeSM - token.switchLoadingIconSize) / 2,
          fontSize: token.switchLoadingIconSize
        },
        [`&${componentCls}-checked`]: {
          [`${componentCls}-inner`]: {
            paddingInlineStart: token.switchInnerMarginMinSM,
            paddingInlineEnd: token.switchInnerMarginMaxSM,
            [`${switchInnerCls}-checked`]: {
              marginInlineStart: 0,
              marginInlineEnd: 0
            },
            [`${switchInnerCls}-unchecked`]: {
              marginInlineStart: `calc(100% - ${token.switchPinSizeSM + token.switchPadding * 2}px + ${token.switchInnerMarginMaxSM * 2}px)`,
              marginInlineEnd: `calc(-100% + ${token.switchPinSizeSM + token.switchPadding * 2}px - ${token.switchInnerMarginMaxSM * 2}px)`
            }
          },
          [`${componentCls}-handle`]: {
            insetInlineStart: `calc(100% - ${token.switchPinSizeSM + token.switchPadding}px)`
          }
        },
        [`&:not(${componentCls}-disabled):active`]: {
          [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {
            [`${switchInnerCls}-unchecked`]: {
              marginInlineStart: token.marginXXS / 2,
              marginInlineEnd: -token.marginXXS / 2
            }
          },
          [`&${componentCls}-checked ${switchInnerCls}`]: {
            [`${switchInnerCls}-checked`]: {
              marginInlineStart: -token.marginXXS / 2,
              marginInlineEnd: token.marginXXS / 2
            }
          }
        }
      }
    }
  };
};
var genSwitchLoadingStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    [componentCls]: {
      [`${componentCls}-loading-icon${token.iconCls}`]: {
        position: "relative",
        top: (token.switchPinSize - token.fontSize) / 2,
        color: token.switchLoadingIconColor,
        verticalAlign: "top"
      },
      [`&${componentCls}-checked ${componentCls}-loading-icon`]: {
        color: token.switchColor
      }
    }
  };
};
var genSwitchHandleStyle = (token) => {
  const {
    componentCls
  } = token;
  const switchHandleCls = `${componentCls}-handle`;
  return {
    [componentCls]: {
      [switchHandleCls]: {
        position: "absolute",
        top: token.switchPadding,
        insetInlineStart: token.switchPadding,
        width: token.switchPinSize,
        height: token.switchPinSize,
        transition: `all ${token.switchDuration} ease-in-out`,
        "&::before": {
          position: "absolute",
          top: 0,
          insetInlineEnd: 0,
          bottom: 0,
          insetInlineStart: 0,
          backgroundColor: token.colorWhite,
          borderRadius: token.switchPinSize / 2,
          boxShadow: token.switchHandleShadow,
          transition: `all ${token.switchDuration} ease-in-out`,
          content: '""'
        }
      },
      [`&${componentCls}-checked ${switchHandleCls}`]: {
        insetInlineStart: `calc(100% - ${token.switchPinSize + token.switchPadding}px)`
      },
      [`&:not(${componentCls}-disabled):active`]: {
        [`${switchHandleCls}::before`]: {
          insetInlineEnd: token.switchHandleActiveInset,
          insetInlineStart: 0
        },
        [`&${componentCls}-checked ${switchHandleCls}::before`]: {
          insetInlineEnd: 0,
          insetInlineStart: token.switchHandleActiveInset
        }
      }
    }
  };
};
var genSwitchInnerStyle = (token) => {
  const {
    componentCls
  } = token;
  const switchInnerCls = `${componentCls}-inner`;
  return {
    [componentCls]: {
      [switchInnerCls]: {
        display: "block",
        overflow: "hidden",
        borderRadius: 100,
        height: "100%",
        paddingInlineStart: token.switchInnerMarginMax,
        paddingInlineEnd: token.switchInnerMarginMin,
        transition: `padding-inline-start ${token.switchDuration} ease-in-out, padding-inline-end ${token.switchDuration} ease-in-out`,
        [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {
          display: "block",
          color: token.colorTextLightSolid,
          fontSize: token.fontSizeSM,
          transition: `margin-inline-start ${token.switchDuration} ease-in-out, margin-inline-end ${token.switchDuration} ease-in-out`,
          pointerEvents: "none"
        },
        [`${switchInnerCls}-checked`]: {
          marginInlineStart: `calc(-100% + ${token.switchPinSize + token.switchPadding * 2}px - ${token.switchInnerMarginMax * 2}px)`,
          marginInlineEnd: `calc(100% - ${token.switchPinSize + token.switchPadding * 2}px + ${token.switchInnerMarginMax * 2}px)`
        },
        [`${switchInnerCls}-unchecked`]: {
          marginTop: -token.switchHeight,
          marginInlineStart: 0,
          marginInlineEnd: 0
        }
      },
      [`&${componentCls}-checked ${switchInnerCls}`]: {
        paddingInlineStart: token.switchInnerMarginMin,
        paddingInlineEnd: token.switchInnerMarginMax,
        [`${switchInnerCls}-checked`]: {
          marginInlineStart: 0,
          marginInlineEnd: 0
        },
        [`${switchInnerCls}-unchecked`]: {
          marginInlineStart: `calc(100% - ${token.switchPinSize + token.switchPadding * 2}px + ${token.switchInnerMarginMax * 2}px)`,
          marginInlineEnd: `calc(-100% + ${token.switchPinSize + token.switchPadding * 2}px - ${token.switchInnerMarginMax * 2}px)`
        }
      },
      [`&:not(${componentCls}-disabled):active`]: {
        [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {
          [`${switchInnerCls}-unchecked`]: {
            marginInlineStart: token.switchPadding * 2,
            marginInlineEnd: -token.switchPadding * 2
          }
        },
        [`&${componentCls}-checked ${switchInnerCls}`]: {
          [`${switchInnerCls}-checked`]: {
            marginInlineStart: -token.switchPadding * 2,
            marginInlineEnd: token.switchPadding * 2
          }
        }
      }
    }
  };
};
var genSwitchStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    [componentCls]: _extends(_extends(_extends(_extends({}, resetComponent(token)), {
      position: "relative",
      display: "inline-block",
      boxSizing: "border-box",
      minWidth: token.switchMinWidth,
      height: token.switchHeight,
      lineHeight: `${token.switchHeight}px`,
      verticalAlign: "middle",
      background: token.colorTextQuaternary,
      border: "0",
      borderRadius: 100,
      cursor: "pointer",
      transition: `all ${token.motionDurationMid}`,
      userSelect: "none",
      [`&:hover:not(${componentCls}-disabled)`]: {
        background: token.colorTextTertiary
      }
    }), genFocusStyle(token)), {
      [`&${componentCls}-checked`]: {
        background: token.switchColor,
        [`&:hover:not(${componentCls}-disabled)`]: {
          background: token.colorPrimaryHover
        }
      },
      [`&${componentCls}-loading, &${componentCls}-disabled`]: {
        cursor: "not-allowed",
        opacity: token.switchDisabledOpacity,
        "*": {
          boxShadow: "none",
          cursor: "not-allowed"
        }
      },
      // rtl style
      [`&${componentCls}-rtl`]: {
        direction: "rtl"
      }
    })
  };
};
var style_default = genComponentStyleHook("Switch", (token) => {
  const switchHeight = token.fontSize * token.lineHeight;
  const switchHeightSM = token.controlHeight / 2;
  const switchPadding = 2;
  const switchPinSize = switchHeight - switchPadding * 2;
  const switchPinSizeSM = switchHeightSM - switchPadding * 2;
  const switchToken = merge(token, {
    switchMinWidth: switchPinSize * 2 + switchPadding * 4,
    switchHeight,
    switchDuration: token.motionDurationMid,
    switchColor: token.colorPrimary,
    switchDisabledOpacity: token.opacityLoading,
    switchInnerMarginMin: switchPinSize / 2,
    switchInnerMarginMax: switchPinSize + switchPadding + switchPadding * 2,
    switchPadding,
    switchPinSize,
    switchBg: token.colorBgContainer,
    switchMinWidthSM: switchPinSizeSM * 2 + switchPadding * 2,
    switchHeightSM,
    switchInnerMarginMinSM: switchPinSizeSM / 2,
    switchInnerMarginMaxSM: switchPinSizeSM + switchPadding + switchPadding * 2,
    switchPinSizeSM,
    switchHandleShadow: `0 2px 4px 0 ${new TinyColor("#00230b").setAlpha(0.2).toRgbString()}`,
    switchLoadingIconSize: token.fontSizeIcon * 0.75,
    switchLoadingIconColor: `rgba(0, 0, 0, ${token.opacityLoading})`,
    switchHandleActiveInset: "-30%"
  });
  return [
    genSwitchStyle(switchToken),
    // inner style
    genSwitchInnerStyle(switchToken),
    // handle style
    genSwitchHandleStyle(switchToken),
    // loading style
    genSwitchLoadingStyle(switchToken),
    // small style
    genSwitchSmallStyle(switchToken)
  ];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/switch/index.js
var SwitchSizes = tuple("small", "default");
var switchProps = () => ({
  id: String,
  prefixCls: String,
  size: vue_types_default.oneOf(SwitchSizes),
  disabled: {
    type: Boolean,
    default: void 0
  },
  checkedChildren: vue_types_default.any,
  unCheckedChildren: vue_types_default.any,
  tabindex: vue_types_default.oneOfType([vue_types_default.string, vue_types_default.number]),
  autofocus: {
    type: Boolean,
    default: void 0
  },
  loading: {
    type: Boolean,
    default: void 0
  },
  checked: vue_types_default.oneOfType([vue_types_default.string, vue_types_default.number, vue_types_default.looseBool]),
  checkedValue: vue_types_default.oneOfType([vue_types_default.string, vue_types_default.number, vue_types_default.looseBool]).def(true),
  unCheckedValue: vue_types_default.oneOfType([vue_types_default.string, vue_types_default.number, vue_types_default.looseBool]).def(false),
  onChange: {
    type: Function
  },
  onClick: {
    type: Function
  },
  onKeydown: {
    type: Function
  },
  onMouseup: {
    type: Function
  },
  "onUpdate:checked": {
    type: Function
  },
  onBlur: Function,
  onFocus: Function
});
var Switch = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ASwitch",
  __ANT_SWITCH: true,
  inheritAttrs: false,
  props: switchProps(),
  slots: Object,
  // emits: ['update:checked', 'mouseup', 'change', 'click', 'keydown', 'blur'],
  setup(props, _ref) {
    let {
      attrs,
      slots,
      expose,
      emit
    } = _ref;
    const formItemContext = useInjectFormItemContext();
    const disabledContext = useInjectDisabled();
    const mergedDisabled = computed(() => {
      var _a;
      return (_a = props.disabled) !== null && _a !== void 0 ? _a : disabledContext.value;
    });
    onBeforeMount(() => {
      warning_default(!("defaultChecked" in attrs), "Switch", `'defaultChecked' is deprecated, please use 'v-model:checked'`);
      warning_default(!("value" in attrs), "Switch", "`value` is not validate prop, do you mean `checked`?");
    });
    const checked = ref(props.checked !== void 0 ? props.checked : attrs.defaultChecked);
    const checkedStatus = computed(() => checked.value === props.checkedValue);
    watch(() => props.checked, () => {
      checked.value = props.checked;
    });
    const {
      prefixCls,
      direction,
      size
    } = useConfigInject_default("switch", props);
    const [wrapSSR, hashId] = style_default(prefixCls);
    const refSwitchNode = ref();
    const focus = () => {
      var _a;
      (_a = refSwitchNode.value) === null || _a === void 0 ? void 0 : _a.focus();
    };
    const blur = () => {
      var _a;
      (_a = refSwitchNode.value) === null || _a === void 0 ? void 0 : _a.blur();
    };
    expose({
      focus,
      blur
    });
    onMounted(() => {
      nextTick(() => {
        if (props.autofocus && !mergedDisabled.value) {
          refSwitchNode.value.focus();
        }
      });
    });
    const setChecked = (check, e) => {
      if (mergedDisabled.value) {
        return;
      }
      emit("update:checked", check);
      emit("change", check, e);
      formItemContext.onFieldChange();
    };
    const handleBlur = (e) => {
      emit("blur", e);
    };
    const handleClick = (e) => {
      focus();
      const newChecked = checkedStatus.value ? props.unCheckedValue : props.checkedValue;
      setChecked(newChecked, e);
      emit("click", newChecked, e);
    };
    const handleKeyDown = (e) => {
      if (e.keyCode === KeyCode_default.LEFT) {
        setChecked(props.unCheckedValue, e);
      } else if (e.keyCode === KeyCode_default.RIGHT) {
        setChecked(props.checkedValue, e);
      }
      emit("keydown", e);
    };
    const handleMouseUp = (e) => {
      var _a;
      (_a = refSwitchNode.value) === null || _a === void 0 ? void 0 : _a.blur();
      emit("mouseup", e);
    };
    const classNames = computed(() => ({
      [`${prefixCls.value}-small`]: size.value === "small",
      [`${prefixCls.value}-loading`]: props.loading,
      [`${prefixCls.value}-checked`]: checkedStatus.value,
      [`${prefixCls.value}-disabled`]: mergedDisabled.value,
      [prefixCls.value]: true,
      [`${prefixCls.value}-rtl`]: direction.value === "rtl",
      [hashId.value]: true
    }));
    return () => {
      var _a;
      return wrapSSR(createVNode(wave_default, null, {
        default: () => [createVNode("button", _objectSpread2(_objectSpread2(_objectSpread2({}, omit_default(props, ["prefixCls", "checkedChildren", "unCheckedChildren", "checked", "autofocus", "checkedValue", "unCheckedValue", "id", "onChange", "onUpdate:checked"])), attrs), {}, {
          "id": (_a = props.id) !== null && _a !== void 0 ? _a : formItemContext.id.value,
          "onKeydown": handleKeyDown,
          "onClick": handleClick,
          "onBlur": handleBlur,
          "onMouseup": handleMouseUp,
          "type": "button",
          "role": "switch",
          "aria-checked": checked.value,
          "disabled": mergedDisabled.value || props.loading,
          "class": [attrs.class, classNames.value],
          "ref": refSwitchNode
        }), [createVNode("div", {
          "class": `${prefixCls.value}-handle`
        }, [props.loading ? createVNode(LoadingOutlined_default, {
          "class": `${prefixCls.value}-loading-icon`
        }, null) : null]), createVNode("span", {
          "class": `${prefixCls.value}-inner`
        }, [createVNode("span", {
          "class": `${prefixCls.value}-inner-checked`
        }, [getPropsSlot(slots, props, "checkedChildren")]), createVNode("span", {
          "class": `${prefixCls.value}-inner-unchecked`
        }, [getPropsSlot(slots, props, "unCheckedChildren")])])])]
      }));
    };
  }
});
var switch_default = withInstall(Switch);

export {
  SwitchSizes,
  switchProps,
  switch_default
};
//# sourceMappingURL=chunk-TX6S5NQO.js.map
