{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/dom.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nlet tpImgEl;\nexport function initTpImg() {\n    if (!tpImgEl) {\n        tpImgEl = new Image();\n        tpImgEl.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';\n    }\n    return tpImgEl;\n}\nexport function getTpImg() {\n    if (!tpImgEl) {\n        return initTpImg();\n    }\n    return tpImgEl;\n}\nconst reClsMap = {};\nfunction getClsRE(cls) {\n    if (!reClsMap[cls]) {\n        reClsMap[cls] = new RegExp(`(?:^|\\\\s)${cls}(?!\\\\S)`, 'g');\n    }\n    return reClsMap[cls];\n}\nfunction getNodeOffset(elem, container, rest) {\n    if (elem) {\n        const parentElem = elem.parentNode;\n        rest.top += elem.offsetTop;\n        rest.left += elem.offsetLeft;\n        if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {\n            rest.top -= parentElem.scrollTop;\n            rest.left -= parentElem.scrollLeft;\n        }\n        if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {\n            return getNodeOffset(elem.offsetParent, container, rest);\n        }\n    }\n    return rest;\n}\nexport function isPx(val) {\n    return val && /^\\d+(px)?$/.test(val);\n}\nexport function isScale(val) {\n    return val && /^\\d+%$/.test(val);\n}\nexport function hasClass(elem, cls) {\n    return !!(elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls)));\n}\nexport function removeClass(elem, cls) {\n    if (elem && hasClass(elem, cls)) {\n        elem.className = elem.className.replace(getClsRE(cls), '');\n    }\n}\nexport function addClass(elem, cls) {\n    if (elem && !hasClass(elem, cls)) {\n        removeClass(elem, cls);\n        elem.className = `${elem.className} ${cls}`;\n    }\n}\nexport function hasControlKey(evnt) {\n    return evnt.ctrlKey || evnt.metaKey;\n}\nexport function toCssUnit(val, unit = 'px') {\n    if (XEUtils.isNumber(val) || /^\\d+$/.test(`${val}`)) {\n        return `${val}${unit}`;\n    }\n    return `${val || ''}`;\n}\nexport function getDomNode() {\n    const documentElement = document.documentElement;\n    const bodyElem = document.body;\n    return {\n        scrollTop: documentElement.scrollTop || bodyElem.scrollTop,\n        scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,\n        visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,\n        visibleWidth: documentElement.clientWidth || bodyElem.clientWidth\n    };\n}\n/**\n * 检查触发源是否属于目标节点\n */\nexport function getEventTargetNode(evnt, container, queryCls, queryMethod) {\n    let targetElem;\n    let target = (evnt.target.shadowRoot && evnt.composed) ? (evnt.composedPath()[0] || evnt.target) : evnt.target;\n    while (target && target.nodeType && target !== document) {\n        if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {\n            targetElem = target;\n        }\n        else if (target === container) {\n            return { flag: queryCls ? !!targetElem : true, container, targetElem: targetElem };\n        }\n        target = target.parentNode;\n    }\n    return { flag: false };\n}\n/**\n * 获取元素相对于 document 的位置\n */\nexport function getOffsetPos(elem, container) {\n    return getNodeOffset(elem, container, { left: 0, top: 0 });\n}\nexport function getAbsolutePos(elem) {\n    const bounding = elem.getBoundingClientRect();\n    const boundingTop = bounding.top;\n    const boundingLeft = bounding.left;\n    const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();\n    return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };\n}\nconst scrollIntoViewIfNeeded = 'scrollIntoViewIfNeeded';\nconst scrollIntoView = 'scrollIntoView';\nexport function scrollToView(elem) {\n    if (elem) {\n        if (elem[scrollIntoViewIfNeeded]) {\n            elem[scrollIntoViewIfNeeded]();\n        }\n        else if (elem[scrollIntoView]) {\n            elem[scrollIntoView]();\n        }\n    }\n}\nexport function triggerEvent(targetElem, type) {\n    if (targetElem) {\n        targetElem.dispatchEvent(new Event(type));\n    }\n}\nexport function isNodeElement(elem) {\n    return elem && elem.nodeType === 1;\n}\n"], "mappings": ";;;;;;;;AAAA,sBAAoB;AACpB,IAAI;AACG,SAAS,YAAY;AACxB,MAAI,CAAC,SAAS;AACV,cAAU,IAAI,MAAM;AACpB,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO;AACX;AACO,SAAS,WAAW;AACvB,MAAI,CAAC,SAAS;AACV,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AACX;AACA,IAAM,WAAW,CAAC;AAClB,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,aAAS,GAAG,IAAI,IAAI,OAAO,YAAY,GAAG,WAAW,GAAG;AAAA,EAC5D;AACA,SAAO,SAAS,GAAG;AACvB;AAsBO,SAAS,SAAS,MAAM,KAAK;AAChC,SAAO,CAAC,EAAE,QAAQ,KAAK,aAAa,KAAK,UAAU,SAAS,KAAK,UAAU,MAAM,SAAS,GAAG,CAAC;AAClG;AAYO,SAAS,cAAc,MAAM;AAChC,SAAO,KAAK,WAAW,KAAK;AAChC;AACO,SAAS,UAAU,KAAK,OAAO,MAAM;AACxC,MAAI,gBAAAA,QAAQ,SAAS,GAAG,KAAK,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG;AACjD,WAAO,GAAG,GAAG,GAAG,IAAI;AAAA,EACxB;AACA,SAAO,GAAG,OAAO,EAAE;AACvB;AACO,SAAS,aAAa;AACzB,QAAM,kBAAkB,SAAS;AACjC,QAAM,WAAW,SAAS;AAC1B,SAAO;AAAA,IACH,WAAW,gBAAgB,aAAa,SAAS;AAAA,IACjD,YAAY,gBAAgB,cAAc,SAAS;AAAA,IACnD,eAAe,gBAAgB,gBAAgB,SAAS;AAAA,IACxD,cAAc,gBAAgB,eAAe,SAAS;AAAA,EAC1D;AACJ;AAIO,SAAS,mBAAmB,MAAM,WAAW,UAAU,aAAa;AACvE,MAAI;AACJ,MAAI,SAAU,KAAK,OAAO,cAAc,KAAK,WAAa,KAAK,aAAa,EAAE,CAAC,KAAK,KAAK,SAAU,KAAK;AACxG,SAAO,UAAU,OAAO,YAAY,WAAW,UAAU;AACrD,QAAI,YAAY,SAAS,QAAQ,QAAQ,MAAM,CAAC,eAAe,YAAY,MAAM,IAAI;AACjF,mBAAa;AAAA,IACjB,WACS,WAAW,WAAW;AAC3B,aAAO,EAAE,MAAM,WAAW,CAAC,CAAC,aAAa,MAAM,WAAW,WAAuB;AAAA,IACrF;AACA,aAAS,OAAO;AAAA,EACpB;AACA,SAAO,EAAE,MAAM,MAAM;AACzB;AAOO,SAAS,eAAe,MAAM;AACjC,QAAM,WAAW,KAAK,sBAAsB;AAC5C,QAAM,cAAc,SAAS;AAC7B,QAAM,eAAe,SAAS;AAC9B,QAAM,EAAE,WAAW,YAAY,eAAe,aAAa,IAAI,WAAW;AAC1E,SAAO,EAAE,aAAa,KAAK,YAAY,aAAa,cAAc,MAAM,aAAa,cAAc,eAAe,aAAa;AACnI;", "names": ["XEUtils"]}