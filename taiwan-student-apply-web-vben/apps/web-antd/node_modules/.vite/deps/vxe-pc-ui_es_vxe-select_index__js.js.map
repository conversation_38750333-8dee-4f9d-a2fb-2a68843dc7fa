{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-select/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeSelectComponent from './src/select';\nimport { dynamicApp } from '../dynamics';\nexport const VxeSelect = Object.assign(VxeSelectComponent, {\n    install: function (app) {\n        app.component(VxeSelectComponent.name, VxeSelectComponent);\n    }\n});\ndynamicApp.use(VxeSelect);\nVxeUI.component(VxeSelectComponent);\nexport const Select = VxeSelect;\nexport default VxeSelect;\n", "import VxeSelect from '../select';\nexport * from '../select';\nexport default VxeSelect;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGO,IAAM,YAAY,OAAO,OAAO,gBAAoB;AAAA,EACvD,SAAS,SAAU,KAAK;AACpB,QAAI,UAAU,eAAmB,MAAM,cAAkB;AAAA,EAC7D;AACJ,CAAC;AACD,WAAW,IAAI,SAAS;AACxB,MAAM,UAAU,cAAkB;AAC3B,IAAM,SAAS;AACtB,IAAOA,kBAAQ;;;ACTf,IAAO,qBAAQC;", "names": ["select_default", "select_default"]}