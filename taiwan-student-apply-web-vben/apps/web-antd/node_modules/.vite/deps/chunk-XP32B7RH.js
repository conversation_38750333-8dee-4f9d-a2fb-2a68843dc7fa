import {
  VxeUI,
  require_xe_utils
} from "./chunk-I6LGDOPB.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/log.js
var { log } = VxeUI;
var version = `table v${"4.13.16"}`;
var warnLog = log.create("warn", version);
var errLog = log.create("error", version);

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/vn.js
var import_xe_utils = __toESM(require_xe_utils());
function getOnName(type) {
  return "on" + type.substring(0, 1).toLocaleUpperCase() + type.substring(1);
}
function getModelEvent(renderOpts) {
  switch (renderOpts.name) {
    case "input":
    case "textarea":
      return "input";
    case "select":
      return "change";
  }
  return "update:modelValue";
}
function getChangeEvent(renderOpts) {
  switch (renderOpts.name) {
    case "input":
    case "textarea":
    case "VxeInput":
    case "VxeNumberInput":
    case "VxeTextarea":
    case "$input":
    case "$textarea":
      return "input";
  }
  return "change";
}
function getSlotVNs(vns) {
  if (vns === null || vns === void 0) {
    return [];
  }
  if (import_xe_utils.default.isArray(vns)) {
    return vns;
  }
  return [vns];
}

export {
  warnLog,
  errLog,
  getOnName,
  getModelEvent,
  getChangeEvent,
  getSlotVNs
};
//# sourceMappingURL=chunk-XP32B7RH.js.map
