{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/divider/style/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/divider/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport { resetComponent } from '../../style';\n// ============================== Shared ==============================\nconst genSharedDividerStyle = token => {\n  const {\n    componentCls,\n    sizePaddingEdgeHorizontal,\n    colorSplit,\n    lineWidth\n  } = token;\n  return {\n    [componentCls]: _extends(_extends({}, resetComponent(token)), {\n      borderBlockStart: `${lineWidth}px solid ${colorSplit}`,\n      // vertical\n      '&-vertical': {\n        position: 'relative',\n        top: '-0.06em',\n        display: 'inline-block',\n        height: '0.9em',\n        margin: `0 ${token.dividerVerticalGutterMargin}px`,\n        verticalAlign: 'middle',\n        borderTop: 0,\n        borderInlineStart: `${lineWidth}px solid ${colorSplit}`\n      },\n      '&-horizontal': {\n        display: 'flex',\n        clear: 'both',\n        width: '100%',\n        minWidth: '100%',\n        margin: `${token.dividerHorizontalGutterMargin}px 0`\n      },\n      [`&-horizontal${componentCls}-with-text`]: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: `${token.dividerHorizontalWithTextGutterMargin}px 0`,\n        color: token.colorTextHeading,\n        fontWeight: 500,\n        fontSize: token.fontSizeLG,\n        whiteSpace: 'nowrap',\n        textAlign: 'center',\n        borderBlockStart: `0 ${colorSplit}`,\n        '&::before, &::after': {\n          position: 'relative',\n          width: '50%',\n          borderBlockStart: `${lineWidth}px solid transparent`,\n          // Chrome not accept `inherit` in `border-top`\n          borderBlockStartColor: 'inherit',\n          borderBlockEnd: 0,\n          transform: 'translateY(50%)',\n          content: \"''\"\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-left`]: {\n        '&::before': {\n          width: '5%'\n        },\n        '&::after': {\n          width: '95%'\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-right`]: {\n        '&::before': {\n          width: '95%'\n        },\n        '&::after': {\n          width: '5%'\n        }\n      },\n      [`${componentCls}-inner-text`]: {\n        display: 'inline-block',\n        padding: '0 1em'\n      },\n      '&-dashed': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dashed',\n        borderWidth: `${lineWidth}px 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dashed`]: {\n        '&::before, &::after': {\n          borderStyle: 'dashed none none'\n        }\n      },\n      [`&-vertical${componentCls}-dashed`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      [`&-plain${componentCls}-with-text`]: {\n        color: token.colorText,\n        fontWeight: 'normal',\n        fontSize: token.fontSize\n      },\n      [`&-horizontal${componentCls}-with-text-left${componentCls}-no-default-orientation-margin-left`]: {\n        '&::before': {\n          width: 0\n        },\n        '&::after': {\n          width: '100%'\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineStart: sizePaddingEdgeHorizontal\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-right${componentCls}-no-default-orientation-margin-right`]: {\n        '&::before': {\n          width: '100%'\n        },\n        '&::after': {\n          width: 0\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineEnd: sizePaddingEdgeHorizontal\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook('Divider', token => {\n  const dividerToken = mergeToken(token, {\n    dividerVerticalGutterMargin: token.marginXS,\n    dividerHorizontalWithTextGutterMargin: token.margin,\n    dividerHorizontalGutterMargin: token.marginLG\n  });\n  return [genSharedDividerStyle(dividerToken)];\n}, {\n  sizePaddingEdgeHorizontal: 0\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode } from \"vue\";\nimport { flattenChildren } from '../_util/props-util';\nimport { computed, defineComponent } from 'vue';\nimport { withInstall } from '../_util/type';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nimport useStyle from './style';\nexport const dividerProps = () => ({\n  prefixCls: String,\n  type: {\n    type: String,\n    default: 'horizontal'\n  },\n  dashed: {\n    type: Boolean,\n    default: false\n  },\n  orientation: {\n    type: String,\n    default: 'center'\n  },\n  plain: {\n    type: Boolean,\n    default: false\n  },\n  orientationMargin: [String, Number]\n});\nconst Divider = defineComponent({\n  name: 'ADivider',\n  inheritAttrs: false,\n  compatConfig: {\n    MODE: 3\n  },\n  props: dividerProps(),\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs\n    } = _ref;\n    const {\n      prefixCls: prefixClsRef,\n      direction\n    } = useConfigInject('divider', props);\n    const [wrapSSR, hashId] = useStyle(prefixClsRef);\n    const hasCustomMarginLeft = computed(() => props.orientation === 'left' && props.orientationMargin != null);\n    const hasCustomMarginRight = computed(() => props.orientation === 'right' && props.orientationMargin != null);\n    const classString = computed(() => {\n      const {\n        type,\n        dashed,\n        plain\n      } = props;\n      const prefixCls = prefixClsRef.value;\n      return {\n        [prefixCls]: true,\n        [hashId.value]: !!hashId.value,\n        [`${prefixCls}-${type}`]: true,\n        [`${prefixCls}-dashed`]: !!dashed,\n        [`${prefixCls}-plain`]: !!plain,\n        [`${prefixCls}-rtl`]: direction.value === 'rtl',\n        [`${prefixCls}-no-default-orientation-margin-left`]: hasCustomMarginLeft.value,\n        [`${prefixCls}-no-default-orientation-margin-right`]: hasCustomMarginRight.value\n      };\n    });\n    const innerStyle = computed(() => {\n      const marginValue = typeof props.orientationMargin === 'number' ? `${props.orientationMargin}px` : props.orientationMargin;\n      return _extends(_extends({}, hasCustomMarginLeft.value && {\n        marginLeft: marginValue\n      }), hasCustomMarginRight.value && {\n        marginRight: marginValue\n      });\n    });\n    const orientationPrefix = computed(() => props.orientation.length > 0 ? '-' + props.orientation : props.orientation);\n    return () => {\n      var _a;\n      const children = flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));\n      return wrapSSR(_createVNode(\"div\", _objectSpread(_objectSpread({}, attrs), {}, {\n        \"class\": [classString.value, children.length ? `${prefixClsRef.value}-with-text ${prefixClsRef.value}-with-text${orientationPrefix.value}` : '', attrs.class],\n        \"role\": \"separator\"\n      }), [children.length ? _createVNode(\"span\", {\n        \"class\": `${prefixClsRef.value}-inner-text`,\n        \"style\": innerStyle.value\n      }, [children]) : null]));\n    };\n  }\n});\nexport default withInstall(Divider);"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA,IAAM,wBAAwB,WAAS;AACrC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,YAAY,GAAG,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,MAC5D,kBAAkB,GAAG,SAAS,YAAY,UAAU;AAAA;AAAA,MAEpD,cAAc;AAAA,QACZ,UAAU;AAAA,QACV,KAAK;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ,KAAK,MAAM,2BAA2B;AAAA,QAC9C,eAAe;AAAA,QACf,WAAW;AAAA,QACX,mBAAmB,GAAG,SAAS,YAAY,UAAU;AAAA,MACvD;AAAA,MACA,gBAAgB;AAAA,QACd,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ,GAAG,MAAM,6BAA6B;AAAA,MAChD;AAAA,MACA,CAAC,eAAe,YAAY,YAAY,GAAG;AAAA,QACzC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ,GAAG,MAAM,qCAAqC;AAAA,QACtD,OAAO,MAAM;AAAA,QACb,YAAY;AAAA,QACZ,UAAU,MAAM;AAAA,QAChB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,kBAAkB,KAAK,UAAU;AAAA,QACjC,uBAAuB;AAAA,UACrB,UAAU;AAAA,UACV,OAAO;AAAA,UACP,kBAAkB,GAAG,SAAS;AAAA;AAAA,UAE9B,uBAAuB;AAAA,UACvB,gBAAgB;AAAA,UAChB,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,CAAC,eAAe,YAAY,iBAAiB,GAAG;AAAA,QAC9C,aAAa;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,eAAe,YAAY,kBAAkB,GAAG;AAAA,QAC/C,aAAa;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,QACV,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,aAAa,GAAG,SAAS;AAAA,MAC3B;AAAA,MACA,CAAC,eAAe,YAAY,aAAa,YAAY,SAAS,GAAG;AAAA,QAC/D,uBAAuB;AAAA,UACrB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,CAAC,aAAa,YAAY,SAAS,GAAG;AAAA,QACpC,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,MAClB;AAAA,MACA,CAAC,UAAU,YAAY,YAAY,GAAG;AAAA,QACpC,OAAO,MAAM;AAAA,QACb,YAAY;AAAA,QACZ,UAAU,MAAM;AAAA,MAClB;AAAA,MACA,CAAC,eAAe,YAAY,kBAAkB,YAAY,qCAAqC,GAAG;AAAA,QAChG,aAAa;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,OAAO;AAAA,QACT;AAAA,QACA,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,UAC9B,oBAAoB;AAAA,QACtB;AAAA,MACF;AAAA,MACA,CAAC,eAAe,YAAY,mBAAmB,YAAY,sCAAsC,GAAG;AAAA,QAClG,aAAa;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,OAAO;AAAA,QACT;AAAA,QACA,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,UAC9B,kBAAkB;AAAA,QACpB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,IAAO,gBAAQ,sBAAsB,WAAW,WAAS;AACvD,QAAM,eAAe,MAAW,OAAO;AAAA,IACrC,6BAA6B,MAAM;AAAA,IACnC,uCAAuC,MAAM;AAAA,IAC7C,+BAA+B,MAAM;AAAA,EACvC,CAAC;AACD,SAAO,CAAC,sBAAsB,YAAY,CAAC;AAC7C,GAAG;AAAA,EACD,2BAA2B;AAC7B,CAAC;;;AC1HM,IAAM,eAAe,OAAO;AAAA,EACjC,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB,CAAC,QAAQ,MAAM;AACpC;AACA,IAAM,UAAU,gBAAgB;AAAA,EAC9B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,OAAO,aAAa;AAAA,EACpB,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ,WAAW;AAAA,MACX;AAAA,IACF,IAAI,wBAAgB,WAAW,KAAK;AACpC,UAAM,CAAC,SAAS,MAAM,IAAI,cAAS,YAAY;AAC/C,UAAM,sBAAsB,SAAS,MAAM,MAAM,gBAAgB,UAAU,MAAM,qBAAqB,IAAI;AAC1G,UAAM,uBAAuB,SAAS,MAAM,MAAM,gBAAgB,WAAW,MAAM,qBAAqB,IAAI;AAC5G,UAAM,cAAc,SAAS,MAAM;AACjC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,YAAY,aAAa;AAC/B,aAAO;AAAA,QACL,CAAC,SAAS,GAAG;AAAA,QACb,CAAC,OAAO,KAAK,GAAG,CAAC,CAAC,OAAO;AAAA,QACzB,CAAC,GAAG,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,QAC1B,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,CAAC;AAAA,QAC3B,CAAC,GAAG,SAAS,QAAQ,GAAG,CAAC,CAAC;AAAA,QAC1B,CAAC,GAAG,SAAS,MAAM,GAAG,UAAU,UAAU;AAAA,QAC1C,CAAC,GAAG,SAAS,qCAAqC,GAAG,oBAAoB;AAAA,QACzE,CAAC,GAAG,SAAS,sCAAsC,GAAG,qBAAqB;AAAA,MAC7E;AAAA,IACF,CAAC;AACD,UAAM,aAAa,SAAS,MAAM;AAChC,YAAM,cAAc,OAAO,MAAM,sBAAsB,WAAW,GAAG,MAAM,iBAAiB,OAAO,MAAM;AACzG,aAAO,SAAS,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,QACxD,YAAY;AAAA,MACd,CAAC,GAAG,qBAAqB,SAAS;AAAA,QAChC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM,MAAM,YAAY,SAAS,IAAI,MAAM,MAAM,cAAc,MAAM,WAAW;AACnH,WAAO,MAAM;AACX,UAAI;AACJ,YAAM,WAAW,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AACzG,aAAO,QAAQ,YAAa,OAAO,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QAC7E,SAAS,CAAC,YAAY,OAAO,SAAS,SAAS,GAAG,aAAa,KAAK,cAAc,aAAa,KAAK,aAAa,kBAAkB,KAAK,KAAK,IAAI,MAAM,KAAK;AAAA,QAC5J,QAAQ;AAAA,MACV,CAAC,GAAG,CAAC,SAAS,SAAS,YAAa,QAAQ;AAAA,QAC1C,SAAS,GAAG,aAAa,KAAK;AAAA,QAC9B,SAAS,WAAW;AAAA,MACtB,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACzB;AAAA,EACF;AACF,CAAC;AACD,IAAO,kBAAQ,YAAY,OAAO;", "names": []}