{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/select/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent, ref } from 'vue';\nimport classNames from '../_util/classNames';\nimport RcSelect, { selectProps as vcSelectProps, Option, OptGroup } from '../vc-select';\nimport getIcons from './utils/iconUtil';\nimport PropTypes from '../_util/vue-types';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nimport { DefaultRenderEmpty } from '../config-provider/renderEmpty';\nimport omit from '../_util/omit';\nimport { FormItemInputContext, useInjectFormItemContext } from '../form/FormItemContext';\nimport { getTransitionDirection, getTransitionName } from '../_util/transition';\nimport { initDefaultProps } from '../_util/props-util';\nimport { getStatusClassNames, getMergedStatus } from '../_util/statusUtils';\nimport { stringType, someType, functionType, booleanType } from '../_util/type';\nimport { useCompactItemContext } from '../space/Compact';\n// CSSINJS\nimport useStyle from './style';\nimport { useInjectDisabled } from '../config-provider/DisabledContext';\nimport devWarning from '../vc-util/devWarning';\nexport const selectProps = () => _extends(_extends({}, omit(vcSelectProps(), ['inputIcon', 'mode', 'getInputElement', 'getRawInputElement', 'backfill'])), {\n  value: someType([Array, Object, String, Number]),\n  defaultValue: someType([Array, Object, String, Number]),\n  notFoundContent: PropTypes.any,\n  suffixIcon: PropTypes.any,\n  itemIcon: PropTypes.any,\n  size: stringType(),\n  mode: stringType(),\n  bordered: booleanType(true),\n  transitionName: String,\n  choiceTransitionName: stringType(''),\n  popupClassName: String,\n  /** @deprecated Please use `popupClassName` instead */\n  dropdownClassName: String,\n  placement: stringType(),\n  status: stringType(),\n  'onUpdate:value': functionType()\n});\nconst SECRET_COMBOBOX_MODE_DO_NOT_USE = 'SECRET_COMBOBOX_MODE_DO_NOT_USE';\nconst Select = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'ASelect',\n  Option,\n  OptGroup,\n  inheritAttrs: false,\n  props: initDefaultProps(selectProps(), {\n    listHeight: 256,\n    listItemHeight: 24\n  }),\n  SECRET_COMBOBOX_MODE_DO_NOT_USE,\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      attrs,\n      emit,\n      slots,\n      expose\n    } = _ref;\n    const selectRef = ref();\n    const formItemContext = useInjectFormItemContext();\n    const formItemInputContext = FormItemInputContext.useInject();\n    const mergedStatus = computed(() => getMergedStatus(formItemInputContext.status, props.status));\n    const focus = () => {\n      var _a;\n      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.blur();\n    };\n    const scrollTo = arg => {\n      var _a;\n      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.scrollTo(arg);\n    };\n    const mode = computed(() => {\n      const {\n        mode\n      } = props;\n      if (mode === 'combobox') {\n        return undefined;\n      }\n      if (mode === SECRET_COMBOBOX_MODE_DO_NOT_USE) {\n        return 'combobox';\n      }\n      return mode;\n    });\n    // ====================== Warning ======================\n    if (process.env.NODE_ENV !== 'production') {\n      devWarning(!props.dropdownClassName, 'Select', '`dropdownClassName` is deprecated. Please use `popupClassName` instead.');\n    }\n    const {\n      prefixCls,\n      direction,\n      configProvider,\n      renderEmpty,\n      size: contextSize,\n      getPrefixCls,\n      getPopupContainer,\n      disabled,\n      select\n    } = useConfigInject('select', props);\n    const {\n      compactSize,\n      compactItemClassnames\n    } = useCompactItemContext(prefixCls, direction);\n    const mergedSize = computed(() => compactSize.value || contextSize.value);\n    const contextDisabled = useInjectDisabled();\n    const mergedDisabled = computed(() => {\n      var _a;\n      return (_a = disabled.value) !== null && _a !== void 0 ? _a : contextDisabled.value;\n    });\n    // style\n    const [wrapSSR, hashId] = useStyle(prefixCls);\n    const rootPrefixCls = computed(() => getPrefixCls());\n    // ===================== Placement =====================\n    const placement = computed(() => {\n      if (props.placement !== undefined) {\n        return props.placement;\n      }\n      return direction.value === 'rtl' ? 'bottomRight' : 'bottomLeft';\n    });\n    const transitionName = computed(() => getTransitionName(rootPrefixCls.value, getTransitionDirection(placement.value), props.transitionName));\n    const mergedClassName = computed(() => classNames({\n      [`${prefixCls.value}-lg`]: mergedSize.value === 'large',\n      [`${prefixCls.value}-sm`]: mergedSize.value === 'small',\n      [`${prefixCls.value}-rtl`]: direction.value === 'rtl',\n      [`${prefixCls.value}-borderless`]: !props.bordered,\n      [`${prefixCls.value}-in-form-item`]: formItemInputContext.isFormItemInput\n    }, getStatusClassNames(prefixCls.value, mergedStatus.value, formItemInputContext.hasFeedback), compactItemClassnames.value, hashId.value));\n    const triggerChange = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      emit('update:value', args[0]);\n      emit('change', ...args);\n      formItemContext.onFieldChange();\n    };\n    const handleBlur = e => {\n      emit('blur', e);\n      formItemContext.onFieldBlur();\n    };\n    expose({\n      blur,\n      focus,\n      scrollTo\n    });\n    const isMultiple = computed(() => mode.value === 'multiple' || mode.value === 'tags');\n    const mergedShowArrow = computed(() => props.showArrow !== undefined ? props.showArrow : props.loading || !(isMultiple.value || mode.value === 'combobox'));\n    return () => {\n      var _a, _b, _c, _d;\n      const {\n        notFoundContent,\n        listHeight = 256,\n        listItemHeight = 24,\n        popupClassName,\n        dropdownClassName,\n        virtual,\n        dropdownMatchSelectWidth,\n        id = formItemContext.id.value,\n        placeholder = (_a = slots.placeholder) === null || _a === void 0 ? void 0 : _a.call(slots),\n        showArrow\n      } = props;\n      const {\n        hasFeedback,\n        feedbackIcon\n      } = formItemInputContext;\n      const {} = configProvider;\n      // ===================== Empty =====================\n      let mergedNotFound;\n      if (notFoundContent !== undefined) {\n        mergedNotFound = notFoundContent;\n      } else if (slots.notFoundContent) {\n        mergedNotFound = slots.notFoundContent();\n      } else if (mode.value === 'combobox') {\n        mergedNotFound = null;\n      } else {\n        mergedNotFound = (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Select')) || _createVNode(DefaultRenderEmpty, {\n          \"componentName\": \"Select\"\n        }, null);\n      }\n      // ===================== Icons =====================\n      const {\n        suffixIcon,\n        itemIcon,\n        removeIcon,\n        clearIcon\n      } = getIcons(_extends(_extends({}, props), {\n        multiple: isMultiple.value,\n        prefixCls: prefixCls.value,\n        hasFeedback,\n        feedbackIcon,\n        showArrow: mergedShowArrow.value\n      }), slots);\n      const selectProps = omit(props, ['prefixCls', 'suffixIcon', 'itemIcon', 'removeIcon', 'clearIcon', 'size', 'bordered', 'status']);\n      const rcSelectRtlDropdownClassName = classNames(popupClassName || dropdownClassName, {\n        [`${prefixCls.value}-dropdown-${direction.value}`]: direction.value === 'rtl'\n      }, hashId.value);\n      return wrapSSR(_createVNode(RcSelect, _objectSpread(_objectSpread(_objectSpread({\n        \"ref\": selectRef,\n        \"virtual\": virtual,\n        \"dropdownMatchSelectWidth\": dropdownMatchSelectWidth\n      }, selectProps), attrs), {}, {\n        \"showSearch\": (_b = props.showSearch) !== null && _b !== void 0 ? _b : (_c = select === null || select === void 0 ? void 0 : select.value) === null || _c === void 0 ? void 0 : _c.showSearch,\n        \"placeholder\": placeholder,\n        \"listHeight\": listHeight,\n        \"listItemHeight\": listItemHeight,\n        \"mode\": mode.value,\n        \"prefixCls\": prefixCls.value,\n        \"direction\": direction.value,\n        \"inputIcon\": suffixIcon,\n        \"menuItemSelectedIcon\": itemIcon,\n        \"removeIcon\": removeIcon,\n        \"clearIcon\": clearIcon,\n        \"notFoundContent\": mergedNotFound,\n        \"class\": [mergedClassName.value, attrs.class],\n        \"getPopupContainer\": getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.value,\n        \"dropdownClassName\": rcSelectRtlDropdownClassName,\n        \"onChange\": triggerChange,\n        \"onBlur\": handleBlur,\n        \"id\": id,\n        \"dropdownRender\": selectProps.dropdownRender || slots.dropdownRender,\n        \"transitionName\": transitionName.value,\n        \"children\": (_d = slots.default) === null || _d === void 0 ? void 0 : _d.call(slots),\n        \"tagRender\": props.tagRender || slots.tagRender,\n        \"optionLabelRender\": slots.optionLabel,\n        \"maxTagPlaceholder\": props.maxTagPlaceholder || slots.maxTagPlaceholder,\n        \"showArrow\": hasFeedback || showArrow,\n        \"disabled\": mergedDisabled.value\n      }), {\n        option: slots.option\n      }));\n    };\n  }\n});\n/* istanbul ignore next */\nSelect.install = function (app) {\n  app.component(Select.name, Select);\n  app.component(Select.Option.displayName, Select.Option);\n  app.component(Select.OptGroup.displayName, Select.OptGroup);\n  return app;\n};\nexport const SelectOption = Select.Option;\nexport const SelectOptGroup = Select.OptGroup;\nexport default Select;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBO,IAAMA,eAAc,MAAM,SAAS,SAAS,CAAC,GAAG,aAAK,YAAc,GAAG,CAAC,aAAa,QAAQ,mBAAmB,sBAAsB,UAAU,CAAC,CAAC,GAAG;AAAA,EACzJ,OAAO,SAAS,CAAC,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC/C,cAAc,SAAS,CAAC,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAAA,EACtD,iBAAiB,kBAAU;AAAA,EAC3B,YAAY,kBAAU;AAAA,EACtB,UAAU,kBAAU;AAAA,EACpB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,UAAU,YAAY,IAAI;AAAA,EAC1B,gBAAgB;AAAA,EAChB,sBAAsB,WAAW,EAAE;AAAA,EACnC,gBAAgB;AAAA;AAAA,EAEhB,mBAAmB;AAAA,EACnB,WAAW,WAAW;AAAA,EACtB,QAAQ,WAAW;AAAA,EACnB,kBAAkB,aAAa;AACjC,CAAC;AACD,IAAM,kCAAkC;AACxC,IAAM,SAAS,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,OAAO,yBAAiBA,aAAY,GAAG;AAAA,IACrC,YAAY;AAAA,IACZ,gBAAgB;AAAA,EAClB,CAAC;AAAA,EACD;AAAA,EACA,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,IAAI;AACtB,UAAM,kBAAkB,yBAAyB;AACjD,UAAM,uBAAuB,qBAAqB,UAAU;AAC5D,UAAM,eAAe,SAAS,MAAM,gBAAgB,qBAAqB,QAAQ,MAAM,MAAM,CAAC;AAC9F,UAAM,QAAQ,MAAM;AAClB,UAAI;AACJ,OAAC,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACvE;AACA,UAAM,OAAO,MAAM;AACjB,UAAI;AACJ,OAAC,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,IACtE;AACA,UAAM,WAAW,SAAO;AACtB,UAAI;AACJ,OAAC,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,GAAG;AAAA,IAC7E;AACA,UAAM,OAAO,SAAS,MAAM;AAC1B,YAAM;AAAA,QACJ,MAAAC;AAAA,MACF,IAAI;AACJ,UAAIA,UAAS,YAAY;AACvB,eAAO;AAAA,MACT;AACA,UAAIA,UAAS,iCAAiC;AAC5C,eAAO;AAAA,MACT;AACA,aAAOA;AAAA,IACT,CAAC;AAED,QAAI,MAAuC;AACzC,yBAAW,CAAC,MAAM,mBAAmB,UAAU,yEAAyE;AAAA,IAC1H;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,wBAAgB,UAAU,KAAK;AACnC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,sBAAsB,WAAW,SAAS;AAC9C,UAAM,aAAa,SAAS,MAAM,YAAY,SAAS,YAAY,KAAK;AACxE,UAAM,kBAAkB,kBAAkB;AAC1C,UAAM,iBAAiB,SAAS,MAAM;AACpC,UAAI;AACJ,cAAQ,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,KAAK,gBAAgB;AAAA,IAChF,CAAC;AAED,UAAM,CAAC,SAAS,MAAM,IAAI,cAAS,SAAS;AAC5C,UAAM,gBAAgB,SAAS,MAAM,aAAa,CAAC;AAEnD,UAAM,YAAY,SAAS,MAAM;AAC/B,UAAI,MAAM,cAAc,QAAW;AACjC,eAAO,MAAM;AAAA,MACf;AACA,aAAO,UAAU,UAAU,QAAQ,gBAAgB;AAAA,IACrD,CAAC;AACD,UAAM,iBAAiB,SAAS,MAAM,kBAAkB,cAAc,OAAO,uBAAuB,UAAU,KAAK,GAAG,MAAM,cAAc,CAAC;AAC3I,UAAM,kBAAkB,SAAS,MAAM,mBAAW;AAAA,MAChD,CAAC,GAAG,UAAU,KAAK,KAAK,GAAG,WAAW,UAAU;AAAA,MAChD,CAAC,GAAG,UAAU,KAAK,KAAK,GAAG,WAAW,UAAU;AAAA,MAChD,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU;AAAA,MAChD,CAAC,GAAG,UAAU,KAAK,aAAa,GAAG,CAAC,MAAM;AAAA,MAC1C,CAAC,GAAG,UAAU,KAAK,eAAe,GAAG,qBAAqB;AAAA,IAC5D,GAAG,oBAAoB,UAAU,OAAO,aAAa,OAAO,qBAAqB,WAAW,GAAG,sBAAsB,OAAO,OAAO,KAAK,CAAC;AACzI,UAAM,gBAAgB,WAAY;AAChC,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AACA,WAAK,gBAAgB,KAAK,CAAC,CAAC;AAC5B,WAAK,UAAU,GAAG,IAAI;AACtB,sBAAgB,cAAc;AAAA,IAChC;AACA,UAAM,aAAa,OAAK;AACtB,WAAK,QAAQ,CAAC;AACd,sBAAgB,YAAY;AAAA,IAC9B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,aAAa,SAAS,MAAM,KAAK,UAAU,cAAc,KAAK,UAAU,MAAM;AACpF,UAAM,kBAAkB,SAAS,MAAM,MAAM,cAAc,SAAY,MAAM,YAAY,MAAM,WAAW,EAAE,WAAW,SAAS,KAAK,UAAU,WAAW;AAC1J,WAAO,MAAM;AACX,UAAI,IAAI,IAAI,IAAI;AAChB,YAAM;AAAA,QACJ;AAAA,QACA,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK,gBAAgB,GAAG;AAAA,QACxB,eAAe,KAAK,MAAM,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,QACzF;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,CAAC,IAAI;AAEX,UAAI;AACJ,UAAI,oBAAoB,QAAW;AACjC,yBAAiB;AAAA,MACnB,WAAW,MAAM,iBAAiB;AAChC,yBAAiB,MAAM,gBAAgB;AAAA,MACzC,WAAW,KAAK,UAAU,YAAY;AACpC,yBAAiB;AAAA,MACnB,OAAO;AACL,0BAAkB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,QAAQ,MAAM,YAAa,oBAAoB;AAAA,UACrI,iBAAiB;AAAA,QACnB,GAAG,IAAI;AAAA,MACT;AAEA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,QACzC,UAAU,WAAW;AAAA,QACrB,WAAW,UAAU;AAAA,QACrB;AAAA,QACA;AAAA,QACA,WAAW,gBAAgB;AAAA,MAC7B,CAAC,GAAG,KAAK;AACT,YAAMD,eAAc,aAAK,OAAO,CAAC,aAAa,cAAc,YAAY,cAAc,aAAa,QAAQ,YAAY,QAAQ,CAAC;AAChI,YAAM,+BAA+B,mBAAW,kBAAkB,mBAAmB;AAAA,QACnF,CAAC,GAAG,UAAU,KAAK,aAAa,UAAU,KAAK,EAAE,GAAG,UAAU,UAAU;AAAA,MAC1E,GAAG,OAAO,KAAK;AACf,aAAO,QAAQ,YAAa,mBAAU,eAAc,eAAc,eAAc;AAAA,QAC9E,OAAO;AAAA,QACP,WAAW;AAAA,QACX,4BAA4B;AAAA,MAC9B,GAAGA,YAAW,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QAC3B,eAAe,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,QACnL,eAAe;AAAA,QACf,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,QAAQ,KAAK;AAAA,QACb,aAAa,UAAU;AAAA,QACvB,aAAa,UAAU;AAAA,QACvB,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,SAAS,CAAC,gBAAgB,OAAO,MAAM,KAAK;AAAA,QAC5C,qBAAqB,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AAAA,QAC7G,qBAAqB;AAAA,QACrB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,MAAM;AAAA,QACN,kBAAkBA,aAAY,kBAAkB,MAAM;AAAA,QACtD,kBAAkB,eAAe;AAAA,QACjC,aAAa,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,QACnF,aAAa,MAAM,aAAa,MAAM;AAAA,QACtC,qBAAqB,MAAM;AAAA,QAC3B,qBAAqB,MAAM,qBAAqB,MAAM;AAAA,QACtD,aAAa,eAAe;AAAA,QAC5B,YAAY,eAAe;AAAA,MAC7B,CAAC,GAAG;AAAA,QACF,QAAQ,MAAM;AAAA,MAChB,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF,CAAC;AAED,OAAO,UAAU,SAAU,KAAK;AAC9B,MAAI,UAAU,OAAO,MAAM,MAAM;AACjC,MAAI,UAAU,OAAO,OAAO,aAAa,OAAO,MAAM;AACtD,MAAI,UAAU,OAAO,SAAS,aAAa,OAAO,QAAQ;AAC1D,SAAO;AACT;AACO,IAAM,eAAe,OAAO;AAC5B,IAAM,iBAAiB,OAAO;AACrC,IAAO,iBAAQ;", "names": ["selectProps", "mode"]}