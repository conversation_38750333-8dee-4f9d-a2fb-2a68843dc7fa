import {
  toolbar_default
} from "./chunk-RNZLJ723.js";
import "./chunk-GW2WQRXE.js";
import "./chunk-RFZDZ2Z5.js";
import {
  VxeUI
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/index.js
var VxeToolbar = Object.assign({}, toolbar_default, {
  install(app) {
    app.component(toolbar_default.name, toolbar_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(toolbar_default.name, toolbar_default);
}
VxeUI.component(toolbar_default);
var Toolbar = VxeToolbar;
var toolbar_default2 = VxeToolbar;

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-toolbar/index.js
var vxe_toolbar_default = toolbar_default2;
export {
  Toolbar,
  VxeToolbar,
  vxe_toolbar_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-toolbar_index__js.js.map
