import {
  toolbar_default
} from "./chunk-EAC7H2J5.js";
import "./chunk-XP32B7RH.js";
import "./chunk-LLNCDZ36.js";
import {
  VxeUI
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/index.js
var VxeToolbar = Object.assign({}, toolbar_default, {
  install(app) {
    app.component(toolbar_default.name, toolbar_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(toolbar_default.name, toolbar_default);
}
VxeUI.component(toolbar_default);
var Toolbar = VxeToolbar;
var toolbar_default2 = VxeToolbar;

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-toolbar/index.js
var vxe_toolbar_default = toolbar_default2;
export {
  Toolbar,
  VxeToolbar,
  vxe_toolbar_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-toolbar_index__js.js.map
