import {
  input_default
} from "./chunk-MNFS5WSK.js";
import "./chunk-L6NCXLUG.js";
import "./chunk-SZAZ35M7.js";
import "./chunk-GUBNAPS4.js";
import "./chunk-7BLNBWQR.js";
import {
  dynamicApp
} from "./chunk-XD6QLL5Q.js";
import {
  VxeUI
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/input/index.js
var VxeInput = Object.assign(input_default, {
  install(app) {
    app.component(input_default.name, input_default);
  }
});
dynamicApp.use(VxeInput);
VxeUI.component(input_default);
var Input = VxeInput;
var input_default2 = VxeInput;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-input/index.js
var vxe_input_default = input_default2;
export {
  Input,
  VxeInput,
  vxe_input_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-input_index__js.js.map
