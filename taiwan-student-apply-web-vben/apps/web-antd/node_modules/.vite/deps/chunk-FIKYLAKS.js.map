{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-util/Dom/isVisible.js"], "sourcesContent": ["export default (element => {\n  if (!element) {\n    return false;\n  }\n  if (element.offsetParent) {\n    return true;\n  }\n  if (element.getBBox) {\n    const box = element.getBBox();\n    if (box.width || box.height) {\n      return true;\n    }\n  }\n  if (element.getBoundingClientRect) {\n    const box = element.getBoundingClientRect();\n    if (box.width || box.height) {\n      return true;\n    }\n  }\n  return false;\n});"], "mappings": ";AAAA,IAAO,oBAAS,aAAW;AACzB,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,cAAc;AACxB,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS;AACnB,UAAM,MAAM,QAAQ,QAAQ;AAC5B,QAAI,IAAI,SAAS,IAAI,QAAQ;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,QAAQ,uBAAuB;AACjC,UAAM,MAAM,QAAQ,sBAAsB;AAC1C,QAAI,IAAI,SAAS,IAAI,QAAQ;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;", "names": []}