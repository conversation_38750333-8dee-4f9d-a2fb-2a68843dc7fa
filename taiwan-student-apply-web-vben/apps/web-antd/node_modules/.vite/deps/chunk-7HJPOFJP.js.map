{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/colors.js"], "sourcesContent": ["import { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => `${color}-inverse`);\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color) {\n  let includeInverse = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (includeInverse) {\n    return [...inverseColors, ...PresetColors].includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}"], "mappings": ";;;;;AACA,IAAM,gBAAgB,aAAa,IAAI,WAAS,GAAG,KAAK,UAAU;AAC3D,IAAM,yBAAyB,CAAC,WAAW,cAAc,SAAS,WAAW,SAAS;AAMtF,SAAS,cAAc,OAAO;AACnC,MAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACzF,MAAI,gBAAgB;AAClB,WAAO,CAAC,GAAG,eAAe,GAAG,YAAY,EAAE,SAAS,KAAK;AAAA,EAC3D;AACA,SAAO,aAAa,SAAS,KAAK;AACpC;AACO,SAAS,oBAAoB,OAAO;AACzC,SAAO,uBAAuB,SAAS,KAAK;AAC9C;", "names": []}