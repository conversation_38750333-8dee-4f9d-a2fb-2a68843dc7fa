import {
  DownOutlined_default,
  isMobile_default
} from "./chunk-5LABJFL2.js";
import {
  KeyCode_default
} from "./chunk-KFBVPRL2.js";
import {
  genActiveStyle,
  genBasicInputStyle,
  genDisabledStyle,
  genHoverStyle,
  genInputGroupStyle,
  genPlaceholderStyle,
  genStatusStyle,
  initInputToken
} from "./chunk-RH6GZBAT.js";
import {
  getMergedStatus,
  getStatusClassNames
} from "./chunk-WSTZTIHT.js";
import {
  cloneElement
} from "./chunk-7D7PH3EE.js";
import {
  omit_default
} from "./chunk-ITH5DYXO.js";
import {
  genCompactItemStyle
} from "./chunk-EKSPOE5X.js";
import {
  NoCompactStyle,
  useCompactItemContext
} from "./chunk-IPNOEU2W.js";
import {
  AntdIcon_default,
  wrapperRaf
} from "./chunk-O6J4ZVW4.js";
import {
  FormItemInputContext,
  NoFormStatus,
  useInjectFormItemContext
} from "./chunk-HUL2W67K.js";
import {
  vue_types_default
} from "./chunk-NO4XDY4U.js";
import {
  _objectSpread2,
  booleanType,
  classNames_default,
  functionType,
  genComponentStyleHook,
  resetComponent,
  resetIcon,
  someType,
  stringType,
  useConfigInject_default,
  useInjectDisabled,
  warning
} from "./chunk-7LCWIOMH.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  computed,
  createVNode,
  defineComponent,
  onBeforeUnmount,
  ref,
  shallowRef,
  watch
} from "./chunk-ZLVVKZUX.js";

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/UpOutlined.js
var UpOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z" } }] }, "name": "up", "theme": "outlined" };
var UpOutlined_default = UpOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/UpOutlined.js
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var UpOutlined2 = function UpOutlined3(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread({}, p, {
    "icon": UpOutlined_default
  }), null);
};
UpOutlined2.displayName = "UpOutlined";
UpOutlined2.inheritAttrs = false;
var UpOutlined_default2 = UpOutlined2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/utils/supportUtil.js
function supportBigInt() {
  return typeof BigInt === "function";
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/utils/numberUtil.js
function trimNumber(numStr) {
  let str = numStr.trim();
  let negative = str.startsWith("-");
  if (negative) {
    str = str.slice(1);
  }
  str = str.replace(/(\.\d*[^0])0*$/, "$1").replace(/\.0*$/, "").replace(/^0+/, "");
  if (str.startsWith(".")) {
    str = `0${str}`;
  }
  const trimStr = str || "0";
  const splitNumber = trimStr.split(".");
  const integerStr = splitNumber[0] || "0";
  const decimalStr = splitNumber[1] || "0";
  if (integerStr === "0" && decimalStr === "0") {
    negative = false;
  }
  const negativeStr = negative ? "-" : "";
  return {
    negative,
    negativeStr,
    trimStr,
    integerStr,
    decimalStr,
    fullStr: `${negativeStr}${trimStr}`
  };
}
function isE(number) {
  const str = String(number);
  return !Number.isNaN(Number(str)) && str.includes("e");
}
function getNumberPrecision(number) {
  const numStr = String(number);
  if (isE(number)) {
    let precision = Number(numStr.slice(numStr.indexOf("e-") + 2));
    const decimalMatch = numStr.match(/\.(\d+)/);
    if (decimalMatch === null || decimalMatch === void 0 ? void 0 : decimalMatch[1]) {
      precision += decimalMatch[1].length;
    }
    return precision;
  }
  return numStr.includes(".") && validateNumber(numStr) ? numStr.length - numStr.indexOf(".") - 1 : 0;
}
function num2str(number) {
  let numStr = String(number);
  if (isE(number)) {
    if (number > Number.MAX_SAFE_INTEGER) {
      return String(supportBigInt() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);
    }
    if (number < Number.MIN_SAFE_INTEGER) {
      return String(supportBigInt() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);
    }
    numStr = number.toFixed(getNumberPrecision(numStr));
  }
  return trimNumber(numStr).fullStr;
}
function validateNumber(num) {
  if (typeof num === "number") {
    return !Number.isNaN(num);
  }
  if (!num) {
    return false;
  }
  return (
    // Normal type: 11.28
    /^\s*-?\d+(\.\d+)?\s*$/.test(num) || // Pre-number: 1.
    /^\s*-?\d+\.\s*$/.test(num) || // Post-number: .1
    /^\s*-?\.\d+\s*$/.test(num)
  );
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/utils/MiniDecimal.js
function isEmpty(value) {
  return !value && value !== 0 && !Number.isNaN(value) || !String(value).trim();
}
var NumberDecimal = class _NumberDecimal {
  constructor(value) {
    this.origin = "";
    if (isEmpty(value)) {
      this.empty = true;
      return;
    }
    this.origin = String(value);
    this.number = Number(value);
  }
  negate() {
    return new _NumberDecimal(-this.toNumber());
  }
  add(value) {
    if (this.isInvalidate()) {
      return new _NumberDecimal(value);
    }
    const target = Number(value);
    if (Number.isNaN(target)) {
      return this;
    }
    const number = this.number + target;
    if (number > Number.MAX_SAFE_INTEGER) {
      return new _NumberDecimal(Number.MAX_SAFE_INTEGER);
    }
    if (number < Number.MIN_SAFE_INTEGER) {
      return new _NumberDecimal(Number.MIN_SAFE_INTEGER);
    }
    const maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));
    return new _NumberDecimal(number.toFixed(maxPrecision));
  }
  isEmpty() {
    return this.empty;
  }
  isNaN() {
    return Number.isNaN(this.number);
  }
  isInvalidate() {
    return this.isEmpty() || this.isNaN();
  }
  equals(target) {
    return this.toNumber() === (target === null || target === void 0 ? void 0 : target.toNumber());
  }
  lessEquals(target) {
    return this.add(target.negate().toString()).toNumber() <= 0;
  }
  toNumber() {
    return this.number;
  }
  toString() {
    let safe = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
    if (!safe) {
      return this.origin;
    }
    if (this.isInvalidate()) {
      return "";
    }
    return num2str(this.number);
  }
};
var BigIntDecimal = class _BigIntDecimal {
  constructor(value) {
    this.origin = "";
    if (isEmpty(value)) {
      this.empty = true;
      return;
    }
    this.origin = String(value);
    if (value === "-" || Number.isNaN(value)) {
      this.nan = true;
      return;
    }
    let mergedValue = value;
    if (isE(mergedValue)) {
      mergedValue = Number(mergedValue);
    }
    mergedValue = typeof mergedValue === "string" ? mergedValue : num2str(mergedValue);
    if (validateNumber(mergedValue)) {
      const trimRet = trimNumber(mergedValue);
      this.negative = trimRet.negative;
      const numbers = trimRet.trimStr.split(".");
      this.integer = BigInt(numbers[0]);
      const decimalStr = numbers[1] || "0";
      this.decimal = BigInt(decimalStr);
      this.decimalLen = decimalStr.length;
    } else {
      this.nan = true;
    }
  }
  getMark() {
    return this.negative ? "-" : "";
  }
  getIntegerStr() {
    return this.integer.toString();
  }
  getDecimalStr() {
    return this.decimal.toString().padStart(this.decimalLen, "0");
  }
  /**
   * Align BigIntDecimal with same decimal length. e.g. 12.3 + 5 = 1230000
   * This is used for add function only.
   */
  alignDecimal(decimalLength) {
    const str = `${this.getMark()}${this.getIntegerStr()}${this.getDecimalStr().padEnd(decimalLength, "0")}`;
    return BigInt(str);
  }
  negate() {
    const clone = new _BigIntDecimal(this.toString());
    clone.negative = !clone.negative;
    return clone;
  }
  add(value) {
    if (this.isInvalidate()) {
      return new _BigIntDecimal(value);
    }
    const offset = new _BigIntDecimal(value);
    if (offset.isInvalidate()) {
      return this;
    }
    const maxDecimalLength = Math.max(this.getDecimalStr().length, offset.getDecimalStr().length);
    const myAlignedDecimal = this.alignDecimal(maxDecimalLength);
    const offsetAlignedDecimal = offset.alignDecimal(maxDecimalLength);
    const valueStr = (myAlignedDecimal + offsetAlignedDecimal).toString();
    const {
      negativeStr,
      trimStr
    } = trimNumber(valueStr);
    const hydrateValueStr = `${negativeStr}${trimStr.padStart(maxDecimalLength + 1, "0")}`;
    return new _BigIntDecimal(`${hydrateValueStr.slice(0, -maxDecimalLength)}.${hydrateValueStr.slice(-maxDecimalLength)}`);
  }
  isEmpty() {
    return this.empty;
  }
  isNaN() {
    return this.nan;
  }
  isInvalidate() {
    return this.isEmpty() || this.isNaN();
  }
  equals(target) {
    return this.toString() === (target === null || target === void 0 ? void 0 : target.toString());
  }
  lessEquals(target) {
    return this.add(target.negate().toString()).toNumber() <= 0;
  }
  toNumber() {
    if (this.isNaN()) {
      return NaN;
    }
    return Number(this.toString());
  }
  toString() {
    let safe = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
    if (!safe) {
      return this.origin;
    }
    if (this.isInvalidate()) {
      return "";
    }
    return trimNumber(`${this.getMark()}${this.getIntegerStr()}.${this.getDecimalStr()}`).fullStr;
  }
};
function getMiniDecimal(value) {
  if (supportBigInt()) {
    return new BigIntDecimal(value);
  }
  return new NumberDecimal(value);
}
function toFixed(numStr, separatorStr, precision) {
  let cutOnly = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
  if (numStr === "") {
    return "";
  }
  const {
    negativeStr,
    integerStr,
    decimalStr
  } = trimNumber(numStr);
  const precisionDecimalStr = `${separatorStr}${decimalStr}`;
  const numberWithoutDecimal = `${negativeStr}${integerStr}`;
  if (precision >= 0) {
    const advancedNum = Number(decimalStr[precision]);
    if (advancedNum >= 5 && !cutOnly) {
      const advancedDecimal = getMiniDecimal(numStr).add(`${negativeStr}0.${"0".repeat(precision)}${10 - advancedNum}`);
      return toFixed(advancedDecimal.toString(), separatorStr, precision, cutOnly);
    }
    if (precision === 0) {
      return numberWithoutDecimal;
    }
    return `${numberWithoutDecimal}${separatorStr}${decimalStr.padEnd(precision, "0").slice(0, precision)}`;
  }
  if (precisionDecimalStr === ".0") {
    return numberWithoutDecimal;
  }
  return `${numberWithoutDecimal}${precisionDecimalStr}`;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/StepHandler.js
var STEP_INTERVAL = 200;
var STEP_DELAY = 600;
var StepHandler_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "StepHandler",
  inheritAttrs: false,
  props: {
    prefixCls: String,
    upDisabled: Boolean,
    downDisabled: Boolean,
    onStep: functionType()
  },
  slots: Object,
  setup(props, _ref) {
    let {
      slots,
      emit
    } = _ref;
    const stepTimeoutRef = ref();
    const onStepMouseDown = (e, up) => {
      e.preventDefault();
      emit("step", up);
      function loopStep() {
        emit("step", up);
        stepTimeoutRef.value = setTimeout(loopStep, STEP_INTERVAL);
      }
      stepTimeoutRef.value = setTimeout(loopStep, STEP_DELAY);
    };
    const onStopStep = () => {
      clearTimeout(stepTimeoutRef.value);
    };
    onBeforeUnmount(() => {
      onStopStep();
    });
    return () => {
      if (isMobile_default()) {
        return null;
      }
      const {
        prefixCls,
        upDisabled,
        downDisabled
      } = props;
      const handlerClassName = `${prefixCls}-handler`;
      const upClassName = classNames_default(handlerClassName, `${handlerClassName}-up`, {
        [`${handlerClassName}-up-disabled`]: upDisabled
      });
      const downClassName = classNames_default(handlerClassName, `${handlerClassName}-down`, {
        [`${handlerClassName}-down-disabled`]: downDisabled
      });
      const sharedHandlerProps = {
        unselectable: "on",
        role: "button",
        onMouseup: onStopStep,
        onMouseleave: onStopStep
      };
      const {
        upNode,
        downNode
      } = slots;
      return createVNode("div", {
        "class": `${handlerClassName}-wrap`
      }, [createVNode("span", _objectSpread2(_objectSpread2({}, sharedHandlerProps), {}, {
        "onMousedown": (e) => {
          onStepMouseDown(e, true);
        },
        "aria-label": "Increase Value",
        "aria-disabled": upDisabled,
        "class": upClassName
      }), [(upNode === null || upNode === void 0 ? void 0 : upNode()) || createVNode("span", {
        "unselectable": "on",
        "class": `${prefixCls}-handler-up-inner`
      }, null)]), createVNode("span", _objectSpread2(_objectSpread2({}, sharedHandlerProps), {}, {
        "onMousedown": (e) => {
          onStepMouseDown(e, false);
        },
        "aria-label": "Decrease Value",
        "aria-disabled": downDisabled,
        "class": downClassName
      }), [(downNode === null || downNode === void 0 ? void 0 : downNode()) || createVNode("span", {
        "unselectable": "on",
        "class": `${prefixCls}-handler-down-inner`
      }, null)])]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/hooks/useCursor.js
function useCursor(inputRef, focused) {
  const selectionRef = ref(null);
  function recordCursor() {
    try {
      const {
        selectionStart: start,
        selectionEnd: end,
        value
      } = inputRef.value;
      const beforeTxt = value.substring(0, start);
      const afterTxt = value.substring(end);
      selectionRef.value = {
        start,
        end,
        value,
        beforeTxt,
        afterTxt
      };
    } catch (e) {
    }
  }
  function restoreCursor() {
    if (inputRef.value && selectionRef.value && focused.value) {
      try {
        const {
          value
        } = inputRef.value;
        const {
          beforeTxt,
          afterTxt,
          start
        } = selectionRef.value;
        let startPos = value.length;
        if (value.endsWith(afterTxt)) {
          startPos = value.length - selectionRef.value.afterTxt.length;
        } else if (value.startsWith(beforeTxt)) {
          startPos = beforeTxt.length;
        } else {
          const beforeLastChar = beforeTxt[start - 1];
          const newIndex = value.indexOf(beforeLastChar, start - 1);
          if (newIndex !== -1) {
            startPos = newIndex + 1;
          }
        }
        inputRef.value.setSelectionRange(startPos, startPos);
      } catch (e) {
        warning(false, `Something warning of cursor restore. Please fire issue about this: ${e.message}`);
      }
    }
  }
  return [recordCursor, restoreCursor];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/hooks/useFrame.js
var useFrame_default = () => {
  const idRef = shallowRef(0);
  const cleanUp = () => {
    wrapperRaf.cancel(idRef.value);
  };
  onBeforeUnmount(() => {
    cleanUp();
  });
  return (callback) => {
    cleanUp();
    idRef.value = wrapperRaf(() => {
      callback();
    });
  };
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/src/InputNumber.js
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var getDecimalValue = (stringMode, decimalValue) => {
  if (stringMode || decimalValue.isEmpty()) {
    return decimalValue.toString();
  }
  return decimalValue.toNumber();
};
var getDecimalIfValidate = (value) => {
  const decimal = getMiniDecimal(value);
  return decimal.isInvalidate() ? null : decimal;
};
var inputNumberProps = () => ({
  /** value will show as string */
  stringMode: booleanType(),
  defaultValue: someType([String, Number]),
  value: someType([String, Number]),
  prefixCls: stringType(),
  min: someType([String, Number]),
  max: someType([String, Number]),
  step: someType([String, Number], 1),
  tabindex: Number,
  controls: booleanType(true),
  readonly: booleanType(),
  disabled: booleanType(),
  autofocus: booleanType(),
  keyboard: booleanType(true),
  /** Parse display value to validate number */
  parser: functionType(),
  /** Transform `value` to display value show in input */
  formatter: functionType(),
  /** Syntactic sugar of `formatter`. Config precision of display. */
  precision: Number,
  /** Syntactic sugar of `formatter`. Config decimal separator of display. */
  decimalSeparator: String,
  onInput: functionType(),
  onChange: functionType(),
  onPressEnter: functionType(),
  onStep: functionType(),
  onBlur: functionType(),
  onFocus: functionType()
});
var InputNumber_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "InnerInputNumber",
  inheritAttrs: false,
  props: _extends(_extends({}, inputNumberProps()), {
    lazy: Boolean
  }),
  slots: Object,
  setup(props, _ref) {
    let {
      attrs,
      slots,
      emit,
      expose
    } = _ref;
    const inputRef = shallowRef();
    const focus = shallowRef(false);
    const userTypingRef = shallowRef(false);
    const compositionRef = shallowRef(false);
    const decimalValue = shallowRef(getMiniDecimal(props.value));
    function setUncontrolledDecimalValue(newDecimal) {
      if (props.value === void 0) {
        decimalValue.value = newDecimal;
      }
    }
    const getPrecision = (numStr, userTyping) => {
      if (userTyping) {
        return void 0;
      }
      if (props.precision >= 0) {
        return props.precision;
      }
      return Math.max(getNumberPrecision(numStr), getNumberPrecision(props.step));
    };
    const mergedParser = (num) => {
      const numStr = String(num);
      if (props.parser) {
        return props.parser(numStr);
      }
      let parsedStr = numStr;
      if (props.decimalSeparator) {
        parsedStr = parsedStr.replace(props.decimalSeparator, ".");
      }
      return parsedStr.replace(/[^\w.-]+/g, "");
    };
    const inputValue = shallowRef("");
    const mergedFormatter = (number, userTyping) => {
      if (props.formatter) {
        return props.formatter(number, {
          userTyping,
          input: String(inputValue.value)
        });
      }
      let str = typeof number === "number" ? num2str(number) : number;
      if (!userTyping) {
        const mergedPrecision = getPrecision(str, userTyping);
        if (validateNumber(str) && (props.decimalSeparator || mergedPrecision >= 0)) {
          const separatorStr = props.decimalSeparator || ".";
          str = toFixed(str, separatorStr, mergedPrecision);
        }
      }
      return str;
    };
    const initValue = (() => {
      const initValue2 = props.value;
      if (decimalValue.value.isInvalidate() && ["string", "number"].includes(typeof initValue2)) {
        return Number.isNaN(initValue2) ? "" : initValue2;
      }
      return mergedFormatter(decimalValue.value.toString(), false);
    })();
    inputValue.value = initValue;
    function setInputValue(newValue, userTyping) {
      inputValue.value = mergedFormatter(
        // Invalidate number is sometime passed by external control, we should let it go
        // Otherwise is controlled by internal interactive logic which check by userTyping
        // You can ref 'show limited value when input is not focused' test for more info.
        newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping),
        userTyping
      );
    }
    const maxDecimal = computed(() => getDecimalIfValidate(props.max));
    const minDecimal = computed(() => getDecimalIfValidate(props.min));
    const upDisabled = computed(() => {
      if (!maxDecimal.value || !decimalValue.value || decimalValue.value.isInvalidate()) {
        return false;
      }
      return maxDecimal.value.lessEquals(decimalValue.value);
    });
    const downDisabled = computed(() => {
      if (!minDecimal.value || !decimalValue.value || decimalValue.value.isInvalidate()) {
        return false;
      }
      return decimalValue.value.lessEquals(minDecimal.value);
    });
    const [recordCursor, restoreCursor] = useCursor(inputRef, focus);
    const getRangeValue = (target) => {
      if (maxDecimal.value && !target.lessEquals(maxDecimal.value)) {
        return maxDecimal.value;
      }
      if (minDecimal.value && !minDecimal.value.lessEquals(target)) {
        return minDecimal.value;
      }
      return null;
    };
    const isInRange = (target) => !getRangeValue(target);
    const triggerValueUpdate = (newValue, userTyping) => {
      var _a;
      let updateValue = newValue;
      let isRangeValidate = isInRange(updateValue) || updateValue.isEmpty();
      if (!updateValue.isEmpty() && !userTyping) {
        updateValue = getRangeValue(updateValue) || updateValue;
        isRangeValidate = true;
      }
      if (!props.readonly && !props.disabled && isRangeValidate) {
        const numStr = updateValue.toString();
        const mergedPrecision = getPrecision(numStr, userTyping);
        if (mergedPrecision >= 0) {
          updateValue = getMiniDecimal(toFixed(numStr, ".", mergedPrecision));
        }
        if (!updateValue.equals(decimalValue.value)) {
          setUncontrolledDecimalValue(updateValue);
          (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, updateValue.isEmpty() ? null : getDecimalValue(props.stringMode, updateValue));
          if (props.value === void 0) {
            setInputValue(updateValue, userTyping);
          }
        }
        return updateValue;
      }
      return decimalValue.value;
    };
    const onNextPromise = useFrame_default();
    const collectInputValue = (inputStr) => {
      var _a;
      recordCursor();
      inputValue.value = inputStr;
      if (!compositionRef.value) {
        const finalValue = mergedParser(inputStr);
        const finalDecimal = getMiniDecimal(finalValue);
        if (!finalDecimal.isNaN()) {
          triggerValueUpdate(finalDecimal, true);
        }
      }
      (_a = props.onInput) === null || _a === void 0 ? void 0 : _a.call(props, inputStr);
      onNextPromise(() => {
        let nextInputStr = inputStr;
        if (!props.parser) {
          nextInputStr = inputStr.replace(/。/g, ".");
        }
        if (nextInputStr !== inputStr) {
          collectInputValue(nextInputStr);
        }
      });
    };
    const onCompositionStart = () => {
      compositionRef.value = true;
    };
    const onCompositionEnd = () => {
      compositionRef.value = false;
      collectInputValue(inputRef.value.value);
    };
    const onInternalInput = (e) => {
      collectInputValue(e.target.value);
    };
    const onInternalStep = (up) => {
      var _a, _b;
      if (up && upDisabled.value || !up && downDisabled.value) {
        return;
      }
      userTypingRef.value = false;
      let stepDecimal = getMiniDecimal(props.step);
      if (!up) {
        stepDecimal = stepDecimal.negate();
      }
      const target = (decimalValue.value || getMiniDecimal(0)).add(stepDecimal.toString());
      const updatedValue = triggerValueUpdate(target, false);
      (_a = props.onStep) === null || _a === void 0 ? void 0 : _a.call(props, getDecimalValue(props.stringMode, updatedValue), {
        offset: props.step,
        type: up ? "up" : "down"
      });
      (_b = inputRef.value) === null || _b === void 0 ? void 0 : _b.focus();
    };
    const flushInputValue = (userTyping) => {
      const parsedValue = getMiniDecimal(mergedParser(inputValue.value));
      let formatValue = parsedValue;
      if (!parsedValue.isNaN()) {
        formatValue = triggerValueUpdate(parsedValue, userTyping);
      } else {
        formatValue = decimalValue.value;
      }
      if (props.value !== void 0) {
        setInputValue(decimalValue.value, false);
      } else if (!formatValue.isNaN()) {
        setInputValue(formatValue, false);
      }
    };
    const onBeforeInput = () => {
      userTypingRef.value = true;
    };
    const onKeyDown = (event) => {
      var _a;
      const {
        which
      } = event;
      userTypingRef.value = true;
      if (which === KeyCode_default.ENTER) {
        if (!compositionRef.value) {
          userTypingRef.value = false;
        }
        flushInputValue(false);
        (_a = props.onPressEnter) === null || _a === void 0 ? void 0 : _a.call(props, event);
      }
      if (props.keyboard === false) {
        return;
      }
      if (!compositionRef.value && [KeyCode_default.UP, KeyCode_default.DOWN].includes(which)) {
        onInternalStep(KeyCode_default.UP === which);
        event.preventDefault();
      }
    };
    const onKeyUp = () => {
      userTypingRef.value = false;
    };
    const onBlur = (e) => {
      flushInputValue(false);
      focus.value = false;
      userTypingRef.value = false;
      emit("blur", e);
    };
    watch(() => props.precision, () => {
      if (!decimalValue.value.isInvalidate()) {
        setInputValue(decimalValue.value, false);
      }
    }, {
      flush: "post"
    });
    watch(() => props.value, () => {
      const newValue = getMiniDecimal(props.value);
      decimalValue.value = newValue;
      const currentParsedValue = getMiniDecimal(mergedParser(inputValue.value));
      if (!newValue.equals(currentParsedValue) || !userTypingRef.value || props.formatter) {
        setInputValue(newValue, userTypingRef.value);
      }
    }, {
      flush: "post"
    });
    watch(inputValue, () => {
      if (props.formatter) {
        restoreCursor();
      }
    }, {
      flush: "post"
    });
    watch(() => props.disabled, (val) => {
      if (val) {
        focus.value = false;
      }
    });
    expose({
      focus: () => {
        var _a;
        (_a = inputRef.value) === null || _a === void 0 ? void 0 : _a.focus();
      },
      blur: () => {
        var _a;
        (_a = inputRef.value) === null || _a === void 0 ? void 0 : _a.blur();
      }
    });
    return () => {
      const _a = _extends(_extends({}, attrs), props), {
        prefixCls = "rc-input-number",
        min,
        max,
        step = 1,
        defaultValue,
        value,
        disabled,
        readonly,
        keyboard,
        controls = true,
        autofocus,
        stringMode,
        parser,
        formatter,
        precision,
        decimalSeparator,
        onChange,
        onInput,
        onPressEnter,
        onStep,
        lazy,
        class: className,
        style
      } = _a, inputProps = __rest(_a, ["prefixCls", "min", "max", "step", "defaultValue", "value", "disabled", "readonly", "keyboard", "controls", "autofocus", "stringMode", "parser", "formatter", "precision", "decimalSeparator", "onChange", "onInput", "onPressEnter", "onStep", "lazy", "class", "style"]);
      const {
        upHandler,
        downHandler
      } = slots;
      const inputClassName = `${prefixCls}-input`;
      const eventProps = {};
      if (lazy) {
        eventProps.onChange = onInternalInput;
      } else {
        eventProps.onInput = onInternalInput;
      }
      return createVNode("div", {
        "class": classNames_default(prefixCls, className, {
          [`${prefixCls}-focused`]: focus.value,
          [`${prefixCls}-disabled`]: disabled,
          [`${prefixCls}-readonly`]: readonly,
          [`${prefixCls}-not-a-number`]: decimalValue.value.isNaN(),
          [`${prefixCls}-out-of-range`]: !decimalValue.value.isInvalidate() && !isInRange(decimalValue.value)
        }),
        "style": style,
        "onKeydown": onKeyDown,
        "onKeyup": onKeyUp
      }, [controls && createVNode(StepHandler_default, {
        "prefixCls": prefixCls,
        "upDisabled": upDisabled.value,
        "downDisabled": downDisabled.value,
        "onStep": onInternalStep
      }, {
        upNode: upHandler,
        downNode: downHandler
      }), createVNode("div", {
        "class": `${inputClassName}-wrap`
      }, [createVNode("input", _objectSpread2(_objectSpread2(_objectSpread2({
        "autofocus": autofocus,
        "autocomplete": "off",
        "role": "spinbutton",
        "aria-valuemin": min,
        "aria-valuemax": max,
        "aria-valuenow": decimalValue.value.isInvalidate() ? null : decimalValue.value.toString(),
        "step": step
      }, inputProps), {}, {
        "ref": inputRef,
        "class": inputClassName,
        "value": inputValue.value,
        "disabled": disabled,
        "readonly": readonly,
        "onFocus": (e) => {
          focus.value = true;
          emit("focus", e);
        }
      }, eventProps), {}, {
        "onBlur": onBlur,
        "onCompositionstart": onCompositionStart,
        "onCompositionend": onCompositionEnd,
        "onBeforeinput": onBeforeInput
      }), null)])]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/isValidValue.js
function isValidValue_default(val) {
  return val !== void 0 && val !== null;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/style/index.js
var genInputNumberStyles = (token) => {
  const {
    componentCls,
    lineWidth,
    lineType,
    colorBorder,
    borderRadius,
    fontSizeLG,
    controlHeightLG,
    controlHeightSM,
    colorError,
    inputPaddingHorizontalSM,
    colorTextDescription,
    motionDurationMid,
    colorPrimary,
    controlHeight,
    inputPaddingHorizontal,
    colorBgContainer,
    colorTextDisabled,
    borderRadiusSM,
    borderRadiusLG,
    controlWidth,
    handleVisible
  } = token;
  return [
    {
      [componentCls]: _extends(_extends(_extends(_extends({}, resetComponent(token)), genBasicInputStyle(token)), genStatusStyle(token, componentCls)), {
        display: "inline-block",
        width: controlWidth,
        margin: 0,
        padding: 0,
        border: `${lineWidth}px ${lineType} ${colorBorder}`,
        borderRadius,
        "&-rtl": {
          direction: "rtl",
          [`${componentCls}-input`]: {
            direction: "rtl"
          }
        },
        "&-lg": {
          padding: 0,
          fontSize: fontSizeLG,
          borderRadius: borderRadiusLG,
          [`input${componentCls}-input`]: {
            height: controlHeightLG - 2 * lineWidth
          }
        },
        "&-sm": {
          padding: 0,
          borderRadius: borderRadiusSM,
          [`input${componentCls}-input`]: {
            height: controlHeightSM - 2 * lineWidth,
            padding: `0 ${inputPaddingHorizontalSM}px`
          }
        },
        "&:hover": _extends({}, genHoverStyle(token)),
        "&-focused": _extends({}, genActiveStyle(token)),
        "&-disabled": _extends(_extends({}, genDisabledStyle(token)), {
          [`${componentCls}-input`]: {
            cursor: "not-allowed"
          }
        }),
        // ===================== Out Of Range =====================
        "&-out-of-range": {
          input: {
            color: colorError
          }
        },
        // Style for input-group: input with label, with button or dropdown...
        "&-group": _extends(_extends(_extends({}, resetComponent(token)), genInputGroupStyle(token)), {
          "&-wrapper": {
            display: "inline-block",
            textAlign: "start",
            verticalAlign: "top",
            [`${componentCls}-affix-wrapper`]: {
              width: "100%"
            },
            // Size
            "&-lg": {
              [`${componentCls}-group-addon`]: {
                borderRadius: borderRadiusLG
              }
            },
            "&-sm": {
              [`${componentCls}-group-addon`]: {
                borderRadius: borderRadiusSM
              }
            }
          }
        }),
        [componentCls]: {
          "&-input": _extends(_extends({
            width: "100%",
            height: controlHeight - 2 * lineWidth,
            padding: `0 ${inputPaddingHorizontal}px`,
            textAlign: "start",
            backgroundColor: "transparent",
            border: 0,
            borderRadius,
            outline: 0,
            transition: `all ${motionDurationMid} linear`,
            appearance: "textfield",
            color: token.colorText,
            fontSize: "inherit",
            verticalAlign: "top"
          }, genPlaceholderStyle(token.colorTextPlaceholder)), {
            '&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button': {
              margin: 0,
              /* stylelint-disable-next-line property-no-vendor-prefix */
              webkitAppearance: "none",
              appearance: "none"
            }
          })
        }
      })
    },
    // Handler
    {
      [componentCls]: {
        [`&:hover ${componentCls}-handler-wrap, &-focused ${componentCls}-handler-wrap`]: {
          opacity: 1
        },
        [`${componentCls}-handler-wrap`]: {
          position: "absolute",
          insetBlockStart: 0,
          insetInlineEnd: 0,
          width: token.handleWidth,
          height: "100%",
          background: colorBgContainer,
          borderStartStartRadius: 0,
          borderStartEndRadius: borderRadius,
          borderEndEndRadius: borderRadius,
          borderEndStartRadius: 0,
          opacity: handleVisible === true ? 1 : 0,
          display: "flex",
          flexDirection: "column",
          alignItems: "stretch",
          transition: `opacity ${motionDurationMid} linear ${motionDurationMid}`,
          // Fix input number inside Menu makes icon too large
          // We arise the selector priority by nest selector here
          // https://github.com/ant-design/ant-design/issues/14367
          [`${componentCls}-handler`]: {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flex: "auto",
            height: "40%",
            [`
              ${componentCls}-handler-up-inner,
              ${componentCls}-handler-down-inner
            `]: {
              marginInlineEnd: 0,
              fontSize: token.handleFontSize
            }
          }
        },
        [`${componentCls}-handler`]: {
          height: "50%",
          overflow: "hidden",
          color: colorTextDescription,
          fontWeight: "bold",
          lineHeight: 0,
          textAlign: "center",
          cursor: "pointer",
          borderInlineStart: `${lineWidth}px ${lineType} ${colorBorder}`,
          transition: `all ${motionDurationMid} linear`,
          "&:active": {
            background: token.colorFillAlter
          },
          // Hover
          "&:hover": {
            height: `60%`,
            [`
              ${componentCls}-handler-up-inner,
              ${componentCls}-handler-down-inner
            `]: {
              color: colorPrimary
            }
          },
          "&-up-inner, &-down-inner": _extends(_extends({}, resetIcon()), {
            color: colorTextDescription,
            transition: `all ${motionDurationMid} linear`,
            userSelect: "none"
          })
        },
        [`${componentCls}-handler-up`]: {
          borderStartEndRadius: borderRadius
        },
        [`${componentCls}-handler-down`]: {
          borderBlockStart: `${lineWidth}px ${lineType} ${colorBorder}`,
          borderEndEndRadius: borderRadius
        },
        // Disabled
        "&-disabled, &-readonly": {
          [`${componentCls}-handler-wrap`]: {
            display: "none"
          },
          [`${componentCls}-input`]: {
            color: "inherit"
          }
        },
        [`
          ${componentCls}-handler-up-disabled,
          ${componentCls}-handler-down-disabled
        `]: {
          cursor: "not-allowed"
        },
        [`
          ${componentCls}-handler-up-disabled:hover &-handler-up-inner,
          ${componentCls}-handler-down-disabled:hover &-handler-down-inner
        `]: {
          color: colorTextDisabled
        }
      }
    },
    // Border-less
    {
      [`${componentCls}-borderless`]: {
        borderColor: "transparent",
        boxShadow: "none",
        [`${componentCls}-handler-down`]: {
          borderBlockStartWidth: 0
        }
      }
    }
  ];
};
var genAffixWrapperStyles = (token) => {
  const {
    componentCls,
    inputPaddingHorizontal,
    inputAffixPadding,
    controlWidth,
    borderRadiusLG,
    borderRadiusSM
  } = token;
  return {
    [`${componentCls}-affix-wrapper`]: _extends(_extends(_extends({}, genBasicInputStyle(token)), genStatusStyle(token, `${componentCls}-affix-wrapper`)), {
      // or number handler will cover form status
      position: "relative",
      display: "inline-flex",
      width: controlWidth,
      padding: 0,
      paddingInlineStart: inputPaddingHorizontal,
      "&-lg": {
        borderRadius: borderRadiusLG
      },
      "&-sm": {
        borderRadius: borderRadiusSM
      },
      [`&:not(${componentCls}-affix-wrapper-disabled):hover`]: _extends(_extends({}, genHoverStyle(token)), {
        zIndex: 1
      }),
      "&-focused, &:focus": {
        zIndex: 1
      },
      "&-disabled": {
        [`${componentCls}[disabled]`]: {
          background: "transparent"
        }
      },
      [`> div${componentCls}`]: {
        width: "100%",
        border: "none",
        outline: "none",
        [`&${componentCls}-focused`]: {
          boxShadow: "none !important"
        }
      },
      [`input${componentCls}-input`]: {
        padding: 0
      },
      "&::before": {
        width: 0,
        visibility: "hidden",
        content: '"\\a0"'
      },
      [`${componentCls}-handler-wrap`]: {
        zIndex: 2
      },
      [componentCls]: {
        "&-prefix, &-suffix": {
          display: "flex",
          flex: "none",
          alignItems: "center",
          pointerEvents: "none"
        },
        "&-prefix": {
          marginInlineEnd: inputAffixPadding
        },
        "&-suffix": {
          position: "absolute",
          insetBlockStart: 0,
          insetInlineEnd: 0,
          zIndex: 1,
          height: "100%",
          marginInlineEnd: inputPaddingHorizontal,
          marginInlineStart: inputAffixPadding
        }
      }
    })
  };
};
var style_default = genComponentStyleHook("InputNumber", (token) => {
  const inputNumberToken = initInputToken(token);
  return [
    genInputNumberStyles(inputNumberToken),
    genAffixWrapperStyles(inputNumberToken),
    // =====================================================
    // ==             Space Compact                       ==
    // =====================================================
    genCompactItemStyle(inputNumberToken)
  ];
}, (token) => ({
  controlWidth: 90,
  handleWidth: token.controlHeightSM - token.lineWidth * 2,
  handleFontSize: token.fontSize / 2,
  handleVisible: "auto"
}));

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/input-number/index.js
var __rest2 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var baseProps = inputNumberProps();
var inputNumberProps2 = () => _extends(_extends({}, baseProps), {
  size: stringType(),
  bordered: booleanType(true),
  placeholder: String,
  name: String,
  id: String,
  type: String,
  addonBefore: vue_types_default.any,
  addonAfter: vue_types_default.any,
  prefix: vue_types_default.any,
  "onUpdate:value": baseProps.onChange,
  valueModifiers: Object,
  status: stringType()
});
var InputNumber = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AInputNumber",
  inheritAttrs: false,
  props: inputNumberProps2(),
  // emits: ['focus', 'blur', 'change', 'input', 'update:value'],
  slots: Object,
  setup(props, _ref) {
    let {
      emit,
      expose,
      attrs,
      slots
    } = _ref;
    var _a;
    const formItemContext = useInjectFormItemContext();
    const formItemInputContext = FormItemInputContext.useInject();
    const mergedStatus = computed(() => getMergedStatus(formItemInputContext.status, props.status));
    const {
      prefixCls,
      size,
      direction,
      disabled
    } = useConfigInject_default("input-number", props);
    const {
      compactSize,
      compactItemClassnames
    } = useCompactItemContext(prefixCls, direction);
    const disabledContext = useInjectDisabled();
    const mergedDisabled = computed(() => {
      var _a2;
      return (_a2 = disabled.value) !== null && _a2 !== void 0 ? _a2 : disabledContext.value;
    });
    const [wrapSSR, hashId] = style_default(prefixCls);
    const mergedSize = computed(() => compactSize.value || size.value);
    const mergedValue = shallowRef((_a = props.value) !== null && _a !== void 0 ? _a : props.defaultValue);
    const focused = shallowRef(false);
    watch(() => props.value, () => {
      mergedValue.value = props.value;
    });
    const inputNumberRef = shallowRef(null);
    const focus = () => {
      var _a2;
      (_a2 = inputNumberRef.value) === null || _a2 === void 0 ? void 0 : _a2.focus();
    };
    const blur = () => {
      var _a2;
      (_a2 = inputNumberRef.value) === null || _a2 === void 0 ? void 0 : _a2.blur();
    };
    expose({
      focus,
      blur
    });
    const handleChange = (val) => {
      if (props.value === void 0) {
        mergedValue.value = val;
      }
      emit("update:value", val);
      emit("change", val);
      formItemContext.onFieldChange();
    };
    const handleBlur = (e) => {
      focused.value = false;
      emit("blur", e);
      formItemContext.onFieldBlur();
    };
    const handleFocus = (e) => {
      focused.value = true;
      emit("focus", e);
    };
    return () => {
      var _a2, _b, _c, _d;
      const {
        hasFeedback,
        isFormItemInput,
        feedbackIcon
      } = formItemInputContext;
      const id = (_a2 = props.id) !== null && _a2 !== void 0 ? _a2 : formItemContext.id.value;
      const _e = _extends(_extends(_extends({}, attrs), props), {
        id,
        disabled: mergedDisabled.value
      }), {
        class: className,
        bordered,
        readonly,
        style,
        addonBefore = (_b = slots.addonBefore) === null || _b === void 0 ? void 0 : _b.call(slots),
        addonAfter = (_c = slots.addonAfter) === null || _c === void 0 ? void 0 : _c.call(slots),
        prefix = (_d = slots.prefix) === null || _d === void 0 ? void 0 : _d.call(slots),
        valueModifiers = {}
      } = _e, others = __rest2(_e, ["class", "bordered", "readonly", "style", "addonBefore", "addonAfter", "prefix", "valueModifiers"]);
      const preCls = prefixCls.value;
      const inputNumberClass = classNames_default({
        [`${preCls}-lg`]: mergedSize.value === "large",
        [`${preCls}-sm`]: mergedSize.value === "small",
        [`${preCls}-rtl`]: direction.value === "rtl",
        [`${preCls}-readonly`]: readonly,
        [`${preCls}-borderless`]: !bordered,
        [`${preCls}-in-form-item`]: isFormItemInput
      }, getStatusClassNames(preCls, mergedStatus.value), className, compactItemClassnames.value, hashId.value);
      let element = createVNode(InputNumber_default, _objectSpread2(_objectSpread2({}, omit_default(others, ["size", "defaultValue"])), {}, {
        "ref": inputNumberRef,
        "lazy": !!valueModifiers.lazy,
        "value": mergedValue.value,
        "class": inputNumberClass,
        "prefixCls": preCls,
        "readonly": readonly,
        "onChange": handleChange,
        "onBlur": handleBlur,
        "onFocus": handleFocus
      }), {
        upHandler: slots.upIcon ? () => createVNode("span", {
          "class": `${preCls}-handler-up-inner`
        }, [slots.upIcon()]) : () => createVNode(UpOutlined_default2, {
          "class": `${preCls}-handler-up-inner`
        }, null),
        downHandler: slots.downIcon ? () => createVNode("span", {
          "class": `${preCls}-handler-down-inner`
        }, [slots.downIcon()]) : () => createVNode(DownOutlined_default, {
          "class": `${preCls}-handler-down-inner`
        }, null)
      });
      const hasAddon = isValidValue_default(addonBefore) || isValidValue_default(addonAfter);
      const hasPrefix = isValidValue_default(prefix);
      if (hasPrefix || hasFeedback) {
        const affixWrapperCls = classNames_default(`${preCls}-affix-wrapper`, getStatusClassNames(`${preCls}-affix-wrapper`, mergedStatus.value, hasFeedback), {
          [`${preCls}-affix-wrapper-focused`]: focused.value,
          [`${preCls}-affix-wrapper-disabled`]: mergedDisabled.value,
          [`${preCls}-affix-wrapper-sm`]: mergedSize.value === "small",
          [`${preCls}-affix-wrapper-lg`]: mergedSize.value === "large",
          [`${preCls}-affix-wrapper-rtl`]: direction.value === "rtl",
          [`${preCls}-affix-wrapper-readonly`]: readonly,
          [`${preCls}-affix-wrapper-borderless`]: !bordered,
          // className will go to addon wrapper
          [`${className}`]: !hasAddon && className
        }, hashId.value);
        element = createVNode("div", {
          "class": affixWrapperCls,
          "style": style,
          "onClick": focus
        }, [hasPrefix && createVNode("span", {
          "class": `${preCls}-prefix`
        }, [prefix]), element, hasFeedback && createVNode("span", {
          "class": `${preCls}-suffix`
        }, [feedbackIcon])]);
      }
      if (hasAddon) {
        const wrapperClassName = `${preCls}-group`;
        const addonClassName = `${wrapperClassName}-addon`;
        const addonBeforeNode = addonBefore ? createVNode("div", {
          "class": addonClassName
        }, [addonBefore]) : null;
        const addonAfterNode = addonAfter ? createVNode("div", {
          "class": addonClassName
        }, [addonAfter]) : null;
        const mergedWrapperClassName = classNames_default(`${preCls}-wrapper`, wrapperClassName, {
          [`${wrapperClassName}-rtl`]: direction.value === "rtl"
        }, hashId.value);
        const mergedGroupClassName = classNames_default(`${preCls}-group-wrapper`, {
          [`${preCls}-group-wrapper-sm`]: mergedSize.value === "small",
          [`${preCls}-group-wrapper-lg`]: mergedSize.value === "large",
          [`${preCls}-group-wrapper-rtl`]: direction.value === "rtl"
        }, getStatusClassNames(`${prefixCls}-group-wrapper`, mergedStatus.value, hasFeedback), className, hashId.value);
        element = createVNode("div", {
          "class": mergedGroupClassName,
          "style": style
        }, [createVNode("div", {
          "class": mergedWrapperClassName
        }, [addonBeforeNode && createVNode(NoCompactStyle, null, {
          default: () => [createVNode(NoFormStatus, null, {
            default: () => [addonBeforeNode]
          })]
        }), element, addonAfterNode && createVNode(NoCompactStyle, null, {
          default: () => [createVNode(NoFormStatus, null, {
            default: () => [addonAfterNode]
          })]
        })])]);
      }
      return wrapSSR(cloneElement(element, {
        style
      }));
    };
  }
});
var input_number_default = _extends(InputNumber, {
  install: (app) => {
    app.component(InputNumber.name, InputNumber);
    return app;
  }
});

export {
  inputNumberProps2 as inputNumberProps,
  input_number_default
};
//# sourceMappingURL=chunk-SSR75XCQ.js.map
