import {
  require_xe_utils
} from "./chunk-TV7URO3H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/vn.js
var import_xe_utils = __toESM(require_xe_utils());
function getSlotVNs(vns) {
  if (import_xe_utils.default.isArray(vns)) {
    return vns;
  }
  return vns ? [vns] : [];
}

export {
  getSlotVNs
};
//# sourceMappingURL=chunk-JL3MBCJ6.js.map
