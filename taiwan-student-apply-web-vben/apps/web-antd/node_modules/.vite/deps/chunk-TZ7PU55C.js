import {
  useFlexGapSupport_default
} from "./chunk-2362C3ZF.js";
import {
  Compact_default,
  style_default
} from "./chunk-IPNOEU2W.js";
import {
  vue_types_default
} from "./chunk-NO4XDY4U.js";
import {
  _objectSpread2,
  booleanType,
  classNames_default,
  filterEmpty,
  tuple,
  useConfigInject_default
} from "./chunk-7LCWIOMH.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  Fragment,
  computed,
  createVNode,
  defineComponent,
  ref,
  watch
} from "./chunk-ZLVVKZUX.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/space/index.js
var spaceSize = {
  small: 8,
  middle: 16,
  large: 24
};
var spaceProps = () => ({
  prefixCls: String,
  size: {
    type: [String, Number, Array]
  },
  direction: vue_types_default.oneOf(tuple("horizontal", "vertical")).def("horizontal"),
  align: vue_types_default.oneOf(tuple("start", "end", "center", "baseline")),
  wrap: booleanType()
});
function getNumberSize(size) {
  return typeof size === "string" ? spaceSize[size] : size || 0;
}
var Space = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ASpace",
  inheritAttrs: false,
  props: spaceProps(),
  slots: Object,
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    const {
      prefixCls,
      space,
      direction: directionConfig
    } = useConfigInject_default("space", props);
    const [wrapSSR, hashId] = style_default(prefixCls);
    const supportFlexGap = useFlexGapSupport_default();
    const size = computed(() => {
      var _a, _b, _c;
      return (_c = (_a = props.size) !== null && _a !== void 0 ? _a : (_b = space === null || space === void 0 ? void 0 : space.value) === null || _b === void 0 ? void 0 : _b.size) !== null && _c !== void 0 ? _c : "small";
    });
    const horizontalSize = ref();
    const verticalSize = ref();
    watch(size, () => {
      [horizontalSize.value, verticalSize.value] = (Array.isArray(size.value) ? size.value : [size.value, size.value]).map((item) => getNumberSize(item));
    }, {
      immediate: true
    });
    const mergedAlign = computed(() => props.align === void 0 && props.direction === "horizontal" ? "center" : props.align);
    const cn = computed(() => {
      return classNames_default(prefixCls.value, hashId.value, `${prefixCls.value}-${props.direction}`, {
        [`${prefixCls.value}-rtl`]: directionConfig.value === "rtl",
        [`${prefixCls.value}-align-${mergedAlign.value}`]: mergedAlign.value
      });
    });
    const marginDirection = computed(() => directionConfig.value === "rtl" ? "marginLeft" : "marginRight");
    const style = computed(() => {
      const gapStyle = {};
      if (supportFlexGap.value) {
        gapStyle.columnGap = `${horizontalSize.value}px`;
        gapStyle.rowGap = `${verticalSize.value}px`;
      }
      return _extends(_extends({}, gapStyle), props.wrap && {
        flexWrap: "wrap",
        marginBottom: `${-verticalSize.value}px`
      });
    });
    return () => {
      var _a, _b;
      const {
        wrap,
        direction = "horizontal"
      } = props;
      const children = (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);
      const items = filterEmpty(children);
      const len = items.length;
      if (len === 0) {
        return null;
      }
      const split = (_b = slots.split) === null || _b === void 0 ? void 0 : _b.call(slots);
      const itemClassName = `${prefixCls.value}-item`;
      const horizontalSizeVal = horizontalSize.value;
      const latestIndex = len - 1;
      return createVNode("div", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": [cn.value, attrs.class],
        "style": [style.value, attrs.style]
      }), [items.map((child, index) => {
        let originIndex = children.indexOf(child);
        if (originIndex === -1) {
          originIndex = `$$space-${index}`;
        }
        let itemStyle = {};
        if (!supportFlexGap.value) {
          if (direction === "vertical") {
            if (index < latestIndex) {
              itemStyle = {
                marginBottom: `${horizontalSizeVal / (split ? 2 : 1)}px`
              };
            }
          } else {
            itemStyle = _extends(_extends({}, index < latestIndex && {
              [marginDirection.value]: `${horizontalSizeVal / (split ? 2 : 1)}px`
            }), wrap && {
              paddingBottom: `${verticalSize.value}px`
            });
          }
        }
        return wrapSSR(createVNode(Fragment, {
          "key": originIndex
        }, [createVNode("div", {
          "class": itemClassName,
          "style": itemStyle
        }, [child]), index < latestIndex && split && createVNode("span", {
          "class": `${itemClassName}-split`,
          "style": itemStyle
        }, [split])]));
      })]);
    };
  }
});
Space.Compact = Compact_default;
Space.install = function(app) {
  app.component(Space.name, Space);
  app.component(Compact_default.name, Compact_default);
  return app;
};
var space_default = Space;

export {
  spaceProps,
  space_default
};
//# sourceMappingURL=chunk-TZ7PU55C.js.map
