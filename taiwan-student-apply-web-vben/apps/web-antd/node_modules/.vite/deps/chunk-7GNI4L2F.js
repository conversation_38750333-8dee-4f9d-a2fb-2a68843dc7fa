import {
  vue_types_default
} from "./chunk-NO4XDY4U.js";
import {
  _objectSpread2,
  classNames_default,
  initDefaultProps_default
} from "./chunk-7LCWIOMH.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  createVNode,
  defineComponent,
  ref,
  watch
} from "./chunk-ZLVVKZUX.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-checkbox/Checkbox.js
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var checkboxProps = {
  prefixCls: String,
  name: String,
  id: String,
  type: String,
  defaultChecked: {
    type: [Boolean, Number],
    default: void 0
  },
  checked: {
    type: [Boolean, Number],
    default: void 0
  },
  disabled: Boolean,
  tabindex: {
    type: [Number, String]
  },
  readonly: Boolean,
  autofocus: Boolean,
  value: vue_types_default.any,
  required: Boolean
};
var Checkbox_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "Checkbox",
  inheritAttrs: false,
  props: initDefaultProps_default(checkboxProps, {
    prefixCls: "rc-checkbox",
    type: "checkbox",
    defaultChecked: false
  }),
  emits: ["click", "change"],
  setup(props, _ref) {
    let {
      attrs,
      emit,
      expose
    } = _ref;
    const checked = ref(props.checked === void 0 ? props.defaultChecked : props.checked);
    const inputRef = ref();
    watch(() => props.checked, () => {
      checked.value = props.checked;
    });
    expose({
      focus() {
        var _a;
        (_a = inputRef.value) === null || _a === void 0 ? void 0 : _a.focus();
      },
      blur() {
        var _a;
        (_a = inputRef.value) === null || _a === void 0 ? void 0 : _a.blur();
      }
    });
    const eventShiftKey = ref();
    const handleChange = (e) => {
      if (props.disabled) {
        return;
      }
      if (props.checked === void 0) {
        checked.value = e.target.checked;
      }
      e.shiftKey = eventShiftKey.value;
      const eventObj = {
        target: _extends(_extends({}, props), {
          checked: e.target.checked
        }),
        stopPropagation() {
          e.stopPropagation();
        },
        preventDefault() {
          e.preventDefault();
        },
        nativeEvent: e
      };
      if (props.checked !== void 0) {
        inputRef.value.checked = !!props.checked;
      }
      emit("change", eventObj);
      eventShiftKey.value = false;
    };
    const onClick = (e) => {
      emit("click", e);
      eventShiftKey.value = e.shiftKey;
    };
    return () => {
      const {
        prefixCls,
        name,
        id,
        type,
        disabled,
        readonly,
        tabindex,
        autofocus,
        value,
        required
      } = props, others = __rest(props, ["prefixCls", "name", "id", "type", "disabled", "readonly", "tabindex", "autofocus", "value", "required"]);
      const {
        class: className,
        onFocus,
        onBlur,
        onKeydown,
        onKeypress,
        onKeyup
      } = attrs;
      const othersAndAttrs = _extends(_extends({}, others), attrs);
      const globalProps = Object.keys(othersAndAttrs).reduce((prev, key) => {
        if (key.startsWith("data-") || key.startsWith("aria-") || key === "role") {
          prev[key] = othersAndAttrs[key];
        }
        return prev;
      }, {});
      const classString = classNames_default(prefixCls, className, {
        [`${prefixCls}-checked`]: checked.value,
        [`${prefixCls}-disabled`]: disabled
      });
      const inputProps = _extends(_extends({
        name,
        id,
        type,
        readonly,
        disabled,
        tabindex,
        class: `${prefixCls}-input`,
        checked: !!checked.value,
        autofocus,
        value
      }, globalProps), {
        onChange: handleChange,
        onClick,
        onFocus,
        onBlur,
        onKeydown,
        onKeypress,
        onKeyup,
        required
      });
      return createVNode("span", {
        "class": classString
      }, [createVNode("input", _objectSpread2({
        "ref": inputRef
      }, inputProps), null), createVNode("span", {
        "class": `${prefixCls}-inner`
      }, null)]);
    };
  }
});

export {
  Checkbox_default
};
//# sourceMappingURL=chunk-7GNI4L2F.js.map
