import {
  columnProps
} from "./chunk-BOJ725D2.js";
import {
  assembleColumn,
  cell_default,
  destroyColumn,
  watchColumn
} from "./chunk-GDZBY3D5.js";
import "./chunk-XP32B7RH.js";
import "./chunk-LLNCDZ36.js";
import {
  VxeUI
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import {
  createCommentVNode,
  defineComponent,
  h,
  inject,
  onMounted,
  onUnmounted,
  provide,
  ref
} from "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/group.js
var group_default = defineComponent({
  name: "VxeColgroup",
  props: columnProps,
  setup(props, { slots }) {
    const refElem = ref();
    const $xeTable = inject("$xeTable", null);
    const $xeParentColgroup = inject("$xeColgroup", null);
    if (!$xeTable) {
      return () => createCommentVNode();
    }
    const columnConfig = cell_default.createColumn($xeTable, props);
    const columnSlots = {};
    if (slots.header) {
      columnSlots.header = slots.header;
    }
    columnConfig.slots = columnSlots;
    columnConfig.children = [];
    watchColumn($xeTable, props, columnConfig);
    onMounted(() => {
      const elem = refElem.value;
      if (elem) {
        assembleColumn($xeTable, elem, columnConfig, $xeParentColgroup);
      }
    });
    onUnmounted(() => {
      destroyColumn($xeTable, columnConfig);
    });
    const renderVN = () => {
      return h("div", {
        ref: refElem
      }, slots.default ? slots.default() : []);
    };
    const $xeColgroup = { columnConfig };
    provide("$xeColgroup", $xeColgroup);
    provide("$xeGrid", null);
    return renderVN;
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/colgroup/index.js
var VxeColgroup = Object.assign({}, group_default, {
  install(app) {
    app.component(group_default.name, group_default);
    app.component("VxeTableColgroup", group_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(group_default.name, group_default);
  VxeUI.dynamicApp.component("VxeTableColgroup", group_default);
}
VxeUI.component(group_default);
var Colgroup = VxeColgroup;
var colgroup_default = VxeColgroup;

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-colgroup/index.js
var vxe_colgroup_default = colgroup_default;
export {
  Colgroup,
  VxeColgroup,
  vxe_colgroup_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-colgroup_index__js.js.map
