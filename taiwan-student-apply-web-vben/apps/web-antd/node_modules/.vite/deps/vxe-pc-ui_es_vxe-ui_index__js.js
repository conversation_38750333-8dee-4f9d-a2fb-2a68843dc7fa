import {
  config,
  setup,
  ui_default,
  version
} from "./chunk-XD6QLL5Q.js";
import {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  coreVersion,
  createEvent,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  use,
  useFns,
  usePermission,
  useSize,
  validators
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  config,
  coreVersion,
  createEvent,
  vxe_ui_default as default,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  use,
  useFns,
  usePermission,
  useSize,
  validators,
  version
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-ui_index__js.js.map
