import {
  select_default
} from "./chunk-L5KHIE4E.js";
import "./chunk-MNFS5WSK.js";
import "./chunk-L6NCXLUG.js";
import "./chunk-SZAZ35M7.js";
import "./chunk-GUBNAPS4.js";
import "./chunk-7BLNBWQR.js";
import {
  dynamicApp
} from "./chunk-XD6QLL5Q.js";
import {
  VxeUI
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js
var VxeSelect = Object.assign(select_default, {
  install: function(app) {
    app.component(select_default.name, select_default);
  }
});
dynamicApp.use(VxeSelect);
VxeUI.component(select_default);
var Select = VxeSelect;
var select_default2 = VxeSelect;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-select/index.js
var vxe_select_default = select_default2;
export {
  Select,
  VxeSelect,
  vxe_select_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-select_index__js.js.map
