{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/dom.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/util.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/columnInfo.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/cell.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nconst reClsMap = {};\nlet tpImgEl;\nexport function initTpImg() {\n    if (!tpImgEl) {\n        tpImgEl = new Image();\n        tpImgEl.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';\n    }\n    return tpImgEl;\n}\nexport function getTpImg() {\n    if (!tpImgEl) {\n        return initTpImg();\n    }\n    return tpImgEl;\n}\nexport function getPropClass(property, params) {\n    return property ? XEUtils.isFunction(property) ? property(params) : property : '';\n}\nfunction getClsRE(cls) {\n    if (!reClsMap[cls]) {\n        reClsMap[cls] = new RegExp(`(?:^|\\\\s)${cls}(?!\\\\S)`, 'g');\n    }\n    return reClsMap[cls];\n}\nfunction getNodeOffset(elem, container, rest) {\n    if (elem) {\n        const parentElem = elem.parentNode;\n        rest.top += elem.offsetTop;\n        rest.left += elem.offsetLeft;\n        if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {\n            rest.top -= parentElem.scrollTop;\n            rest.left -= parentElem.scrollLeft;\n        }\n        if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {\n            return getNodeOffset(elem.offsetParent, container, rest);\n        }\n    }\n    return rest;\n}\nexport function isPx(val) {\n    return val && /^\\d+(px)?$/.test(val);\n}\nexport function isScale(val) {\n    return val && /^\\d+%$/.test(val);\n}\nexport function hasClass(elem, cls) {\n    return !!(elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls)));\n}\nexport function removeClass(elem, cls) {\n    if (elem && hasClass(elem, cls)) {\n        elem.className = elem.className.replace(getClsRE(cls), '');\n    }\n}\nexport function addClass(elem, cls) {\n    if (elem && !hasClass(elem, cls)) {\n        removeClass(elem, cls);\n        elem.className = `${elem.className} ${cls}`;\n    }\n}\nexport function hasControlKey(evnt) {\n    return evnt.ctrlKey || evnt.metaKey;\n}\nexport function toCssUnit(val, unit = 'px') {\n    if (XEUtils.isNumber(val) || /^\\d+$/.test(`${val}`)) {\n        return `${val}${unit}`;\n    }\n    return `${val || ''}`;\n}\nexport function getDomNode() {\n    const documentElement = document.documentElement;\n    const bodyElem = document.body;\n    return {\n        scrollTop: documentElement.scrollTop || bodyElem.scrollTop,\n        scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,\n        visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,\n        visibleWidth: documentElement.clientWidth || bodyElem.clientWidth\n    };\n}\nexport function getOffsetHeight(elem) {\n    return elem ? elem.offsetHeight : 0;\n}\nexport function getPaddingTopBottomSize(elem) {\n    if (elem) {\n        const computedStyle = getComputedStyle(elem);\n        const paddingTop = XEUtils.toNumber(computedStyle.paddingTop);\n        const paddingBottom = XEUtils.toNumber(computedStyle.paddingBottom);\n        return paddingTop + paddingBottom;\n    }\n    return 0;\n}\nexport function setScrollTop(elem, scrollTop) {\n    if (elem) {\n        elem.scrollTop = scrollTop;\n    }\n}\nexport function setScrollLeft(elem, scrollLeft) {\n    if (elem) {\n        elem.scrollLeft = scrollLeft;\n    }\n}\n// export function setScrollLeftAndTop (elem: HTMLElement | null, scrollLeft: number, scrollTop: number) {\n//   if (elem) {\n//     elem.scrollLeft = scrollLeft\n//     elem.scrollTop = scrollTop\n//   }\n// }\nexport function updateCellTitle(overflowElem, column) {\n    const content = column.type === 'html' ? overflowElem.innerText : overflowElem.textContent;\n    if (overflowElem.getAttribute('title') !== content) {\n        overflowElem.setAttribute('title', content);\n    }\n}\n/**\n * 检查触发源是否属于目标节点\n */\nexport function getEventTargetNode(evnt, container, queryCls, queryMethod) {\n    let targetElem;\n    let target = (evnt.target.shadowRoot && evnt.composed) ? (evnt.composedPath()[0] || evnt.target) : evnt.target;\n    while (target && target.nodeType && target !== document) {\n        if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {\n            targetElem = target;\n        }\n        else if (target === container) {\n            return { flag: queryCls ? !!targetElem : true, container, targetElem: targetElem };\n        }\n        target = target.parentNode;\n    }\n    return { flag: false };\n}\n/**\n * 获取元素相对于 document 的位置\n */\nexport function getOffsetPos(elem, container) {\n    return getNodeOffset(elem, container, { left: 0, top: 0 });\n}\nexport function getAbsolutePos(elem) {\n    const bounding = elem.getBoundingClientRect();\n    const boundingTop = bounding.top;\n    const boundingLeft = bounding.left;\n    const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();\n    return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };\n}\nconst scrollIntoViewIfNeeded = 'scrollIntoViewIfNeeded';\nconst scrollIntoView = 'scrollIntoView';\nexport function scrollToView(elem) {\n    if (elem) {\n        if (elem[scrollIntoViewIfNeeded]) {\n            elem[scrollIntoViewIfNeeded]();\n        }\n        else if (elem[scrollIntoView]) {\n            elem[scrollIntoView]();\n        }\n    }\n}\nexport function triggerEvent(targetElem, type) {\n    if (targetElem) {\n        targetElem.dispatchEvent(new Event(type));\n    }\n}\nexport function isNodeElement(elem) {\n    return elem && elem.nodeType === 1;\n}\n", "import { watch, reactive, nextTick } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { ColumnInfo } from './columnInfo';\nimport { isPx, isScale } from '../../ui/src/dom';\nimport { eqEmptyValue } from '../../ui/src/utils';\nconst getAllConvertColumns = (columns, parentColumn) => {\n    const result = [];\n    columns.forEach((column) => {\n        column.parentId = parentColumn ? parentColumn.id : null;\n        if (column.visible) {\n            if (column.children && column.children.length && column.children.some((column) => column.visible)) {\n                result.push(column);\n                result.push(...getAllConvertColumns(column.children, column));\n            }\n            else {\n                result.push(column);\n            }\n        }\n    });\n    return result;\n};\nexport const convertHeaderColumnToRows = (originColumns) => {\n    let maxLevel = 1;\n    const traverse = (column, parent) => {\n        if (parent) {\n            column.level = parent.level + 1;\n            if (maxLevel < column.level) {\n                maxLevel = column.level;\n            }\n        }\n        if (column.children && column.children.length && column.children.some((column) => column.visible)) {\n            let colSpan = 0;\n            column.children.forEach((subColumn) => {\n                if (subColumn.visible) {\n                    traverse(subColumn, column);\n                    colSpan += subColumn.colSpan;\n                }\n            });\n            column.colSpan = colSpan;\n        }\n        else {\n            column.colSpan = 1;\n        }\n    };\n    originColumns.forEach((column) => {\n        column.level = 1;\n        traverse(column);\n    });\n    const rows = [];\n    for (let i = 0; i < maxLevel; i++) {\n        rows.push([]);\n    }\n    const allColumns = getAllConvertColumns(originColumns);\n    allColumns.forEach((column) => {\n        if (column.children && column.children.length && column.children.some((column) => column.visible)) {\n            column.rowSpan = 1;\n        }\n        else {\n            column.rowSpan = maxLevel - column.level + 1;\n        }\n        rows[column.level - 1].push(column);\n    });\n    return rows;\n};\nexport function restoreScrollLocation($xeTable, scrollLeft, scrollTop) {\n    const internalData = $xeTable.internalData;\n    if (scrollLeft || scrollTop) {\n        internalData.intoRunScroll = false;\n        internalData.inVirtualScroll = false;\n        internalData.inWheelScroll = false;\n        internalData.inHeaderScroll = false;\n        internalData.inBodyScroll = false;\n        internalData.inFooterScroll = false;\n        internalData.scrollRenderType = '';\n        // 还原滚动状态\n        return $xeTable.scrollTo(scrollLeft, scrollTop);\n    }\n    return nextTick();\n}\n/**\n * 生成行的唯一主键\n */\nexport function getRowUniqueId() {\n    return XEUtils.uniqueId('row_');\n}\n// 行主键 key\nexport function getRowkey($xeTable) {\n    const { props } = $xeTable;\n    const { computeRowOpts } = $xeTable.getComputeMaps();\n    const rowOpts = computeRowOpts.value;\n    return `${props.rowId || rowOpts.keyField || '_X_ROW_KEY'}`;\n}\n// 行主键 value\nexport function getRowid($xeTable, row) {\n    const rowid = XEUtils.get(row, getRowkey($xeTable));\n    return encodeRowid(rowid);\n}\nexport function createHandleUpdateRowId($xeTable) {\n    const rowKey = getRowkey($xeTable);\n    const isDeepKey = rowKey.indexOf('.') > -1;\n    const updateRId = isDeepKey ? updateDeepRowKey : updateFastRowKey;\n    return {\n        rowKey,\n        handleUpdateRowId(row) {\n            return row ? updateRId(row, rowKey) : null;\n        }\n    };\n}\nexport function createHandleGetRowId($xeTable) {\n    const rowKey = getRowkey($xeTable);\n    const isDeepKey = rowKey.indexOf('.') > -1;\n    const getRId = isDeepKey ? getDeepRowIdByKey : getFastRowIdByKey;\n    return {\n        rowKey,\n        handleGetRowId(row) {\n            return row ? getRId(row, rowKey) : null;\n        }\n    };\n}\n// 编码行主键\nexport function encodeRowid(rowVal) {\n    return XEUtils.eqNull(rowVal) ? '' : encodeURIComponent(rowVal);\n}\nfunction getDeepRowIdByKey(row, rowKey) {\n    return XEUtils.get(row, rowKey);\n}\nexport function updateDeepRowKey(row, rowKey) {\n    let rowid = getDeepRowIdByKey(row, rowKey);\n    if (eqEmptyValue(rowid)) {\n        rowid = getRowUniqueId();\n        XEUtils.set(row, rowKey, rowid);\n    }\n    return rowid;\n}\nfunction getFastRowIdByKey(row, rowKey) {\n    return row[rowKey];\n}\nexport function updateFastRowKey(row, rowKey) {\n    let rowid = getFastRowIdByKey(row, rowKey);\n    if (eqEmptyValue(rowid)) {\n        rowid = getRowUniqueId();\n        row[rowKey] = rowid;\n    }\n    return rowid;\n}\nexport const handleFieldOrColumn = ($xeTable, fieldOrColumn) => {\n    if (fieldOrColumn) {\n        return XEUtils.isString(fieldOrColumn) || XEUtils.isNumber(fieldOrColumn) ? $xeTable.getColumnByField(`${fieldOrColumn}`) : fieldOrColumn;\n    }\n    return null;\n};\nexport const handleRowidOrRow = ($xeTable, rowidOrRow) => {\n    if (rowidOrRow) {\n        const rowid = XEUtils.isString(rowidOrRow) || XEUtils.isNumber(rowidOrRow) ? rowidOrRow : getRowid($xeTable, rowidOrRow);\n        return $xeTable.getRowById(rowid);\n    }\n    return null;\n};\nfunction getPaddingLeftRightSize(elem) {\n    if (elem) {\n        const computedStyle = getComputedStyle(elem);\n        const paddingLeft = XEUtils.toNumber(computedStyle.paddingLeft);\n        const paddingRight = XEUtils.toNumber(computedStyle.paddingRight);\n        return paddingLeft + paddingRight;\n    }\n    return 0;\n}\nfunction getElementMarginWidth(elem) {\n    if (elem) {\n        const computedStyle = getComputedStyle(elem);\n        const marginLeft = XEUtils.toNumber(computedStyle.marginLeft);\n        const marginRight = XEUtils.toNumber(computedStyle.marginRight);\n        return elem.offsetWidth + marginLeft + marginRight;\n    }\n    return 0;\n}\nfunction queryCellElement(cell, selector) {\n    return cell.querySelector('.vxe-cell' + selector);\n}\nexport function toFilters(filters) {\n    if (filters && XEUtils.isArray(filters)) {\n        return filters.map(({ label, value, data, resetValue, checked }) => {\n            return { label, value, data, resetValue, checked: !!checked, _checked: !!checked };\n        });\n    }\n    return filters;\n}\nexport function toTreePathSeq(path) {\n    return path.map((num, i) => i % 2 === 0 ? (Number(num) + 1) : '.').join('');\n}\nexport function getCellValue(row, column) {\n    return XEUtils.get(row, column.field);\n}\nexport function setCellValue(row, column, value) {\n    return XEUtils.set(row, column.field, value);\n}\nexport function getRefElem(refEl) {\n    if (refEl) {\n        const rest = refEl.value;\n        if (rest) {\n            return (rest.$el || rest);\n        }\n    }\n    return null;\n}\nexport function getCellHeight(height) {\n    if (height === 'unset') {\n        return 0;\n    }\n    return height || 0;\n}\n/**\n * 列宽拖动最大宽度\n * @param params\n * @returns\n */\nexport function getColReMaxWidth(params) {\n    const { $table } = params;\n    const { computeResizableOpts } = $table.getComputeMaps();\n    const resizableOpts = computeResizableOpts.value;\n    const { maxWidth: reMaxWidth } = resizableOpts;\n    // 如果自定义调整宽度逻辑\n    if (reMaxWidth) {\n        const customMaxWidth = XEUtils.isFunction(reMaxWidth) ? reMaxWidth(params) : reMaxWidth;\n        if (customMaxWidth !== 'auto') {\n            return Math.max(1, XEUtils.toNumber(customMaxWidth));\n        }\n    }\n    return -1;\n}\n/**\n * 列宽拖动最小宽度\n * @param params\n * @returns\n */\nexport function getColReMinWidth(params) {\n    const { $table, column, cell } = params;\n    const tableProps = $table.props;\n    const internalData = $table.internalData;\n    const { computeResizableOpts } = $table.getComputeMaps();\n    const resizableOpts = computeResizableOpts.value;\n    const { minWidth: reMinWidth } = resizableOpts;\n    // 如果自定义调整宽度逻辑\n    if (reMinWidth) {\n        const customMinWidth = XEUtils.isFunction(reMinWidth) ? reMinWidth(params) : reMinWidth;\n        if (customMinWidth !== 'auto') {\n            return Math.max(1, XEUtils.toNumber(customMinWidth));\n        }\n    }\n    const { elemStore } = internalData;\n    const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;\n    const { showHeaderOverflow, minWidth: colMinWidth } = column;\n    const headOverflow = XEUtils.isUndefined(showHeaderOverflow) || XEUtils.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;\n    const showEllipsis = headOverflow === 'ellipsis';\n    const showTitle = headOverflow === 'title';\n    const showTooltip = headOverflow === true || headOverflow === 'tooltip';\n    const hasEllipsis = showTitle || showTooltip || showEllipsis;\n    const minTitleWidth = XEUtils.floor((XEUtils.toNumber(getComputedStyle(cell).fontSize) || 14) * 1.6);\n    const paddingLeftRight = getPaddingLeftRightSize(cell) + getPaddingLeftRightSize(queryCellElement(cell, ''));\n    let mWidth = minTitleWidth + paddingLeftRight;\n    // 默认最小宽处理\n    if (hasEllipsis) {\n        const dragIconWidth = getPaddingLeftRightSize(queryCellElement(cell, '>.vxe-cell--drag-handle'));\n        const checkboxIconWidth = getPaddingLeftRightSize(queryCellElement(cell, '>.vxe-cell--checkbox'));\n        const requiredIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--required-icon'));\n        const editIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--edit-icon'));\n        const prefixIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell-title-prefix-icon'));\n        const suffixIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell-title-suffix-icon'));\n        const sortIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--sort'));\n        const filterIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--filter'));\n        mWidth += dragIconWidth + checkboxIconWidth + requiredIconWidth + editIconWidth + prefixIconWidth + suffixIconWidth + filterIconWidth + sortIconWidth;\n    }\n    // 如果设置最小宽\n    if (colMinWidth) {\n        const bodyScrollElem = getRefElem(elemStore['main-body-scroll']);\n        if (bodyScrollElem) {\n            if (isScale(colMinWidth)) {\n                const bodyWidth = bodyScrollElem.clientWidth - 1;\n                const meanWidth = bodyWidth / 100;\n                return Math.max(mWidth, Math.floor(XEUtils.toInteger(colMinWidth) * meanWidth));\n            }\n            else if (isPx(colMinWidth)) {\n                return Math.max(mWidth, XEUtils.toInteger(colMinWidth));\n            }\n        }\n    }\n    return mWidth;\n}\nexport function isColumnInfo(column) {\n    return column && (column.constructor === ColumnInfo || column instanceof ColumnInfo);\n}\nexport function createColumn($xeTable, options, renderOptions) {\n    return isColumnInfo(options) ? options : reactive(new ColumnInfo($xeTable, options, renderOptions));\n}\nexport function watchColumn($xeTable, props, column) {\n    Object.keys(props).forEach(name => {\n        watch(() => props[name], (value) => {\n            column.update(name, value);\n            if ($xeTable) {\n                if (name === 'filters') {\n                    $xeTable.setFilter(column, value);\n                    $xeTable.handleUpdateDataQueue();\n                }\n                else if (['visible', 'fixed', 'width', 'minWidth', 'maxWidth'].includes(name)) {\n                    $xeTable.handleRefreshColumnQueue();\n                }\n            }\n        });\n    });\n}\nexport function assembleColumn($xeTable, elem, column, colgroup) {\n    const { reactData } = $xeTable;\n    const { staticColumns } = reactData;\n    const parentElem = elem.parentNode;\n    const parentColumn = colgroup ? colgroup.columnConfig : null;\n    const parentCols = parentColumn ? parentColumn.children : staticColumns;\n    if (parentElem && parentCols) {\n        parentCols.splice(XEUtils.arrayIndexOf(parentElem.children, elem), 0, column);\n        reactData.staticColumns = staticColumns.slice(0);\n    }\n}\nexport function destroyColumn($xeTable, column) {\n    const { reactData } = $xeTable;\n    const { staticColumns } = reactData;\n    const matchObj = XEUtils.findTree(staticColumns, item => item.id === column.id, { children: 'children' });\n    if (matchObj) {\n        matchObj.items.splice(matchObj.index, 1);\n    }\n    reactData.staticColumns = staticColumns.slice(0);\n}\nexport function getRootColumn($xeTable, column) {\n    const { internalData } = $xeTable;\n    const { fullColumnIdData } = internalData;\n    if (!column) {\n        return null;\n    }\n    let parentColId = column.parentId;\n    while (fullColumnIdData[parentColId]) {\n        const column = fullColumnIdData[parentColId].column;\n        parentColId = column.parentId;\n        if (!parentColId) {\n            return column;\n        }\n    }\n    return column;\n}\nconst lineOffsetSizes = {\n    mini: 3,\n    small: 2,\n    medium: 1,\n    large: 0\n};\nconst countTreeExpand = (prevRow, params) => {\n    let count = 1;\n    if (!prevRow) {\n        return count;\n    }\n    const { $table } = params;\n    const { computeTreeOpts } = $table.getComputeMaps();\n    const treeOpts = computeTreeOpts.value;\n    const { transform, mapChildrenField } = treeOpts;\n    const childrenField = treeOpts.children || treeOpts.childrenField;\n    const rowChildren = prevRow[transform ? mapChildrenField : childrenField];\n    if (rowChildren && $table.isTreeExpandByRow(prevRow)) {\n        for (let index = 0; index < rowChildren.length; index++) {\n            count += countTreeExpand(rowChildren[index], params);\n        }\n    }\n    return count;\n};\nexport const getOffsetSize = ($xeTable) => {\n    const { computeSize } = $xeTable.getComputeMaps();\n    const vSize = computeSize.value;\n    if (vSize) {\n        return lineOffsetSizes[vSize] || 0;\n    }\n    return 0;\n};\nexport function calcTreeLine(params, prevRow) {\n    const { $table, row } = params;\n    const tableProps = $table.props;\n    const tableReactData = $table.reactData;\n    const tableInternalData = $table.internalData;\n    const { showOverflow } = tableProps;\n    const { scrollYLoad } = tableReactData;\n    const { fullAllDataRowIdData } = tableInternalData;\n    const { computeRowOpts, computeCellOpts, computeDefaultRowHeight } = $table.getComputeMaps();\n    const rowOpts = computeRowOpts.value;\n    const cellOpts = computeCellOpts.value;\n    const defaultRowHeight = computeDefaultRowHeight.value;\n    const rowid = getRowid($table, row);\n    const rowRest = fullAllDataRowIdData[rowid];\n    const currCellHeight = rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;\n    let expandSize = 1;\n    if (prevRow) {\n        expandSize = countTreeExpand(prevRow, params);\n    }\n    let cellHeight = currCellHeight;\n    const vnHeight = rowRest.height;\n    if (scrollYLoad) {\n        if (!showOverflow) {\n            cellHeight = vnHeight || currCellHeight;\n        }\n    }\n    return cellHeight * expandSize - (prevRow ? 1 : (12 - getOffsetSize($table)));\n}\nexport function clearTableDefaultStatus($xeTable) {\n    const { props, internalData } = $xeTable;\n    internalData.initStatus = false;\n    $xeTable.clearSort();\n    $xeTable.clearCurrentRow();\n    $xeTable.clearCurrentColumn();\n    $xeTable.clearRadioRow();\n    $xeTable.clearRadioReserve();\n    $xeTable.clearCheckboxRow();\n    $xeTable.clearCheckboxReserve();\n    $xeTable.clearRowExpand();\n    $xeTable.clearTreeExpand();\n    $xeTable.clearTreeExpandReserve();\n    $xeTable.clearPendingRow();\n    if ($xeTable.clearFilter) {\n        $xeTable.clearFilter();\n    }\n    if ($xeTable.clearSelected && (props.keyboardConfig || props.mouseConfig)) {\n        $xeTable.clearSelected();\n    }\n    if ($xeTable.clearCellAreas && props.mouseConfig) {\n        $xeTable.clearCellAreas();\n        $xeTable.clearCopyCellArea();\n    }\n    return $xeTable.clearScroll();\n}\nexport function clearTableAllStatus($xeTable) {\n    if ($xeTable.clearFilter) {\n        $xeTable.clearFilter();\n    }\n    return clearTableDefaultStatus($xeTable);\n}\nexport function rowToVisible($xeTable, row) {\n    const tableProps = $xeTable.props;\n    const reactData = $xeTable.reactData;\n    const internalData = $xeTable.internalData;\n    const { computeLeftFixedWidth, computeRightFixedWidth, computeRowOpts, computeCellOpts, computeDefaultRowHeight } = $xeTable.getComputeMaps();\n    const { showOverflow } = tableProps;\n    const { scrollYLoad } = reactData;\n    const { elemStore, afterFullData, fullAllDataRowIdData, isResizeCellHeight } = internalData;\n    const rowOpts = computeRowOpts.value;\n    const cellOpts = computeCellOpts.value;\n    const defaultRowHeight = computeDefaultRowHeight.value;\n    const leftFixedWidth = computeLeftFixedWidth.value;\n    const rightFixedWidth = computeRightFixedWidth.value;\n    const bodyScrollElem = getRefElem(elemStore['main-body-scroll']);\n    const rowid = getRowid($xeTable, row);\n    if (bodyScrollElem) {\n        const bodyHeight = bodyScrollElem.clientHeight;\n        const bodyScrollTop = bodyScrollElem.scrollTop;\n        const trElem = bodyScrollElem.querySelector(`[rowid=\"${rowid}\"]`);\n        if (trElem) {\n            const trOffsetParent = trElem.offsetParent;\n            const trOffsetTop = trElem.offsetTop + (trOffsetParent ? trOffsetParent.offsetTop : 0);\n            const trHeight = trElem.clientHeight;\n            // 检测行是否在可视区中\n            if (trOffsetTop < bodyScrollTop || trOffsetTop > bodyScrollTop + bodyHeight) {\n                return $xeTable.scrollTo(null, trOffsetTop);\n            }\n            else if (trOffsetTop + trHeight >= bodyHeight + bodyScrollTop) {\n                return $xeTable.scrollTo(null, bodyScrollTop + trHeight);\n            }\n        }\n        else {\n            // 如果是虚拟渲染滚动\n            if (scrollYLoad) {\n                const isCustomCellHeight = isResizeCellHeight || cellOpts.height || rowOpts.height;\n                if (!isCustomCellHeight && showOverflow) {\n                    return $xeTable.scrollTo(null, ($xeTable.findRowIndexOf(afterFullData, row) - 1) * defaultRowHeight);\n                }\n                let scrollTop = 0;\n                const rowRest = fullAllDataRowIdData[rowid] || {};\n                const rHeight = rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;\n                for (let i = 0; i < afterFullData.length; i++) {\n                    const currRow = afterFullData[i];\n                    const currRowid = getRowid($xeTable, currRow);\n                    if (currRow === row || currRowid === rowid) {\n                        break;\n                    }\n                    const currRowRest = fullAllDataRowIdData[currRowid] || {};\n                    scrollTop += currRowRest.resizeHeight || cellOpts.height || rowOpts.height || currRowRest.height || defaultRowHeight;\n                }\n                if (scrollTop < bodyScrollTop) {\n                    return $xeTable.scrollTo(null, scrollTop - leftFixedWidth - 1);\n                }\n                return $xeTable.scrollTo(null, (scrollTop + rHeight) - (bodyHeight - rightFixedWidth - 1));\n            }\n        }\n    }\n    return Promise.resolve();\n}\nexport function colToVisible($xeTable, column, row) {\n    const reactData = $xeTable.reactData;\n    const internalData = $xeTable.internalData;\n    const { computeLeftFixedWidth, computeRightFixedWidth } = $xeTable.getComputeMaps();\n    const { scrollXLoad } = reactData;\n    const { elemStore, visibleColumn } = internalData;\n    const leftFixedWidth = computeLeftFixedWidth.value;\n    const rightFixedWidth = computeRightFixedWidth.value;\n    const bodyScrollElem = getRefElem(elemStore['main-body-scroll']);\n    if (column.fixed) {\n        return Promise.resolve();\n    }\n    if (bodyScrollElem) {\n        const bodyWidth = bodyScrollElem.clientWidth;\n        const bodyScrollLeft = bodyScrollElem.scrollLeft;\n        let tdElem = null;\n        if (row) {\n            const rowid = getRowid($xeTable, row);\n            tdElem = bodyScrollElem.querySelector(`[rowid=\"${rowid}\"] .${column.id}`);\n        }\n        if (!tdElem) {\n            tdElem = bodyScrollElem.querySelector(`.${column.id}`);\n        }\n        if (tdElem) {\n            const tdOffsetParent = tdElem.offsetParent;\n            const tdOffsetLeft = tdElem.offsetLeft + (tdOffsetParent ? tdOffsetParent.offsetLeft : 0);\n            const cellWidth = tdElem.clientWidth;\n            // 检测是否在可视区中\n            if (tdOffsetLeft < (bodyScrollLeft + leftFixedWidth)) {\n                return $xeTable.scrollTo(tdOffsetLeft - leftFixedWidth - 1);\n            }\n            else if ((tdOffsetLeft + cellWidth - bodyScrollLeft) > (bodyWidth - rightFixedWidth)) {\n                return $xeTable.scrollTo((tdOffsetLeft + cellWidth) - (bodyWidth - rightFixedWidth - 1));\n            }\n        }\n        else {\n            // 检测是否在虚拟渲染可视区中\n            if (scrollXLoad) {\n                let scrollLeft = 0;\n                const cellWidth = column.renderWidth;\n                for (let i = 0; i < visibleColumn.length; i++) {\n                    const currCol = visibleColumn[i];\n                    if (currCol === column || currCol.id === column.id) {\n                        break;\n                    }\n                    scrollLeft += currCol.renderWidth;\n                }\n                if (scrollLeft < bodyScrollLeft) {\n                    return $xeTable.scrollTo(scrollLeft - leftFixedWidth - 1);\n                }\n                return $xeTable.scrollTo((scrollLeft + cellWidth) - (bodyWidth - rightFixedWidth - 1));\n            }\n        }\n    }\n    return Promise.resolve();\n}\n", "import XEUtils from 'xe-utils';\nimport { VxeUI } from '../../ui';\nimport { toFilters } from './util';\nimport { getFuncText } from '../../ui/src/utils';\nimport { warnLog, errLog } from '../../ui/src/log';\nconst { getI18n, formats } = VxeUI;\nexport class ColumnInfo {\n    /* eslint-disable @typescript-eslint/no-use-before-define */\n    constructor($xeTable, _vm, { renderHeader, renderCell, renderFooter, renderData } = {}) {\n        const tableProps = $xeTable.props;\n        const $xeGrid = $xeTable.xeGrid;\n        const formatter = _vm.formatter;\n        const visible = XEUtils.isBoolean(_vm.visible) ? _vm.visible : true;\n        const types = ['seq', 'checkbox', 'radio', 'expand', 'html'];\n        if (_vm.type && types.indexOf(_vm.type) === -1) {\n            warnLog('vxe.error.errProp', [`type=${_vm.type}`, types.join(', ')]);\n        }\n        if (XEUtils.isBoolean(_vm.cellRender) || (_vm.cellRender && !XEUtils.isObject(_vm.cellRender))) {\n            warnLog('vxe.error.errProp', [`column.cell-render=${_vm.cellRender}`, 'column.cell-render={}']);\n        }\n        if (XEUtils.isBoolean(_vm.editRender) || (_vm.editRender && !XEUtils.isObject(_vm.editRender))) {\n            warnLog('vxe.error.errProp', [`column.edit-render=${_vm.editRender}`, 'column.edit-render={}']);\n        }\n        if (_vm.type === 'expand') {\n            const { treeConfig } = tableProps;\n            const { computeTreeOpts } = $xeTable.getComputeMaps();\n            const treeOpts = computeTreeOpts.value;\n            if (treeConfig && (treeOpts.showLine || treeOpts.line)) {\n                errLog('vxe.error.errConflicts', ['tree-config.showLine', 'column.type=expand']);\n            }\n        }\n        if (formatter) {\n            if (XEUtils.isString(formatter)) {\n                const gFormatOpts = formats.get(formatter) || XEUtils[formatter];\n                if (!gFormatOpts || !XEUtils.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {\n                    errLog('vxe.error.notFormats', [formatter]);\n                }\n            }\n            else if (XEUtils.isArray(formatter)) {\n                const gFormatOpts = formats.get(formatter[0]) || XEUtils[formatter[0]];\n                if (!gFormatOpts || !XEUtils.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {\n                    errLog('vxe.error.notFormats', [formatter[0]]);\n                }\n            }\n        }\n        Object.assign(this, {\n            // 基本属性\n            type: _vm.type,\n            property: _vm.field,\n            field: _vm.field,\n            title: _vm.title,\n            width: _vm.width,\n            minWidth: _vm.minWidth,\n            maxWidth: _vm.maxWidth,\n            resizable: _vm.resizable,\n            fixed: _vm.fixed,\n            align: _vm.align,\n            headerAlign: _vm.headerAlign,\n            footerAlign: _vm.footerAlign,\n            showOverflow: _vm.showOverflow,\n            showHeaderOverflow: _vm.showHeaderOverflow,\n            showFooterOverflow: _vm.showFooterOverflow,\n            className: _vm.className,\n            headerClassName: _vm.headerClassName,\n            footerClassName: _vm.footerClassName,\n            formatter: formatter,\n            footerFormatter: _vm.footerFormatter,\n            padding: _vm.padding,\n            verticalAlign: _vm.verticalAlign,\n            sortable: _vm.sortable,\n            sortBy: _vm.sortBy,\n            sortType: _vm.sortType,\n            filters: toFilters(_vm.filters),\n            filterMultiple: XEUtils.isBoolean(_vm.filterMultiple) ? _vm.filterMultiple : true,\n            filterMethod: _vm.filterMethod,\n            filterResetMethod: _vm.filterResetMethod,\n            filterRecoverMethod: _vm.filterRecoverMethod,\n            filterRender: _vm.filterRender,\n            rowGroupNode: _vm.rowGroupNode,\n            treeNode: _vm.treeNode,\n            dragSort: _vm.dragSort,\n            rowResize: _vm.rowResize,\n            cellType: _vm.cellType,\n            cellRender: _vm.cellRender,\n            editRender: _vm.editRender,\n            contentRender: _vm.contentRender,\n            headerExportMethod: _vm.headerExportMethod,\n            exportMethod: _vm.exportMethod,\n            footerExportMethod: _vm.footerExportMethod,\n            titleHelp: _vm.titleHelp,\n            titlePrefix: _vm.titlePrefix,\n            titleSuffix: _vm.titleSuffix,\n            // 自定义参数\n            params: _vm.params,\n            // 渲染属性\n            id: _vm.colId || XEUtils.uniqueId('col_'),\n            parentId: null,\n            visible,\n            // 内部属性（一旦被使用，将导致不可升级版本）\n            halfVisible: false,\n            defaultVisible: visible,\n            defaultFixed: _vm.fixed,\n            checked: false,\n            halfChecked: false,\n            disabled: false,\n            // 分组层级\n            level: 1,\n            // 跨行\n            rowSpan: 1,\n            // 跨列\n            colSpan: 1,\n            // 数据排序\n            order: null,\n            sortTime: 0,\n            // 列排序\n            sortNumber: 0,\n            renderSortNumber: 0,\n            renderFixed: '',\n            renderVisible: false,\n            renderWidth: 0,\n            renderHeight: 0,\n            renderResizeWidth: 0,\n            renderAutoWidth: 0,\n            resizeWidth: 0,\n            renderLeft: 0,\n            renderArgs: [],\n            model: {},\n            renderHeader: renderHeader || _vm.renderHeader,\n            renderCell: renderCell || _vm.renderCell,\n            renderFooter: renderFooter || _vm.renderFooter,\n            renderData: renderData,\n            // 单元格插槽，只对 grid 有效\n            slots: _vm.slots\n        });\n        if ($xeGrid) {\n            const { computeProxyOpts } = $xeGrid.getComputeMaps();\n            const proxyOpts = computeProxyOpts.value;\n            if (proxyOpts.beforeColumn) {\n                proxyOpts.beforeColumn({ $grid: $xeGrid, column: this });\n            }\n        }\n    }\n    getTitle() {\n        return getFuncText(this.title || (this.type === 'seq' ? getI18n('vxe.table.seqTitle') : ''));\n    }\n    getKey() {\n        const { type } = this;\n        return this.field || (type ? `type=${type}` : null);\n    }\n    update(name, value) {\n        // 不支持直接修改的属性\n        if (name !== 'filters') {\n            if (name === 'field') {\n                // 兼容旧属性\n                this.property = value;\n            }\n            this[name] = value;\n        }\n    }\n}\n", "import { h } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../ui';\nimport { getFuncText, isEnableConf, formatText, eqEmptyValue } from '../../ui/src/utils';\nimport { updateCellTitle } from '../../ui/src/dom';\nimport { createColumn, getRowid } from './util';\nimport { getSlotVNs } from '../../ui/src/vn';\nconst { getI18n, getIcon, renderer, formats, renderEmptyElement } = VxeUI;\nfunction renderTitlePrefixIcon(params) {\n    const { $table, column } = params;\n    const titlePrefix = column.titlePrefix || column.titleHelp;\n    if (titlePrefix) {\n        return h('i', {\n            class: ['vxe-cell-title-prefix-icon', titlePrefix.iconStatus ? `theme--${titlePrefix.iconStatus}` : '', titlePrefix.icon || getIcon().TABLE_TITLE_PREFIX],\n            onMouseenter(evnt) {\n                $table.triggerHeaderTitleEvent(evnt, titlePrefix, params);\n            },\n            onMouseleave(evnt) {\n                $table.handleTargetLeaveEvent(evnt);\n            }\n        });\n    }\n    return renderEmptyElement($table);\n}\nfunction renderTitleSuffixIcon(params) {\n    const { $table, column } = params;\n    const titleSuffix = column.titleSuffix;\n    if (titleSuffix) {\n        return h('i', {\n            class: ['vxe-cell-title-suffix-icon', titleSuffix.iconStatus ? `theme--${titleSuffix.iconStatus}` : '', titleSuffix.icon || getIcon().TABLE_TITLE_SUFFIX],\n            onMouseenter(evnt) {\n                $table.triggerHeaderTitleEvent(evnt, titleSuffix, params);\n            },\n            onMouseleave(evnt) {\n                $table.handleTargetLeaveEvent(evnt);\n            }\n        });\n    }\n    return renderEmptyElement($table);\n}\nfunction renderCellDragIcon(params) {\n    const { $table, column } = params;\n    const { context } = $table;\n    const tableSlots = context.slots;\n    const tableProps = $table.props;\n    const { slots } = column;\n    const { dragConfig } = tableProps;\n    const { computeRowDragOpts } = $table.getComputeMaps();\n    const rowDragOpts = computeRowDragOpts.value;\n    const { icon, trigger, disabledMethod } = rowDragOpts;\n    const rDisabledMethod = disabledMethod || (dragConfig ? dragConfig.rowDisabledMethod : null);\n    const isDisabled = rDisabledMethod && rDisabledMethod(params);\n    const rowDragIconSlot = (slots ? slots.rowDragIcon || slots['row-drag-icon'] : null) || tableSlots.rowDragIcon || tableSlots['row-drag-icon'];\n    const ons = {};\n    if (trigger !== 'cell') {\n        ons.onMousedown = (evnt) => {\n            if (!isDisabled) {\n                $table.handleCellDragMousedownEvent(evnt, params);\n            }\n        };\n        ons.onMouseup = $table.handleCellDragMouseupEvent;\n    }\n    return h('span', Object.assign({ key: 'dg', class: ['vxe-cell--drag-handle', {\n                'is--disabled': isDisabled\n            }] }, ons), rowDragIconSlot\n        ? $table.callSlot(rowDragIconSlot, params)\n        : [\n            h('i', {\n                class: icon || (dragConfig ? dragConfig.rowIcon : '') || getIcon().TABLE_DRAG_ROW\n            })\n        ]);\n}\nfunction renderCellBaseVNs(params, content) {\n    const { $table, column, level } = params;\n    const { dragSort } = column;\n    const tableProps = $table.props;\n    const { treeConfig, dragConfig } = tableProps;\n    const { computeRowOpts, computeRowDragOpts, computeTreeOpts } = $table.getComputeMaps();\n    const rowOpts = computeRowOpts.value;\n    const rowDragOpts = computeRowDragOpts.value;\n    const treeOpts = computeTreeOpts.value;\n    const { showIcon, isPeerDrag, isCrossDrag, visibleMethod } = rowDragOpts;\n    const rVisibleMethod = visibleMethod || (dragConfig ? dragConfig.rowVisibleMethod : null);\n    const vns = [];\n    if (dragSort && rowOpts.drag && ((showIcon || (dragConfig ? dragConfig.showRowIcon : false)) && (!rVisibleMethod || rVisibleMethod(params)))) {\n        if (treeConfig) {\n            if (treeOpts.transform && (isPeerDrag || isCrossDrag || !level)) {\n                vns.push(renderCellDragIcon(params));\n            }\n        }\n        else {\n            vns.push(renderCellDragIcon(params));\n        }\n    }\n    return vns.concat(XEUtils.isArray(content) ? content : [content]);\n}\nfunction renderHeaderCellDragIcon(params) {\n    const { $table, column } = params;\n    const { context } = $table;\n    const tableSlots = context.slots;\n    const { slots } = column;\n    const { computeColumnOpts, computeColumnDragOpts } = $table.getComputeMaps();\n    const columnOpts = computeColumnOpts.value;\n    const columnDragOpts = computeColumnDragOpts.value;\n    const { showIcon, icon, trigger, isPeerDrag, isCrossDrag, visibleMethod, disabledMethod } = columnDragOpts;\n    if (columnOpts.drag && showIcon && (!visibleMethod || visibleMethod(params))) {\n        if (!column.fixed && (isPeerDrag || isCrossDrag || !column.parentId)) {\n            const isDisabled = disabledMethod && disabledMethod(params);\n            const columnDragIconSlot = (slots ? slots.columnDragIcon || slots['column-drag-icon'] : null) || tableSlots.columnDragIcon || tableSlots['column-drag-icon'];\n            const ons = {};\n            if (trigger !== 'cell') {\n                ons.onMousedown = (evnt) => {\n                    if (!isDisabled) {\n                        $table.handleHeaderCellDragMousedownEvent(evnt, params);\n                    }\n                };\n                ons.onMouseup = $table.handleHeaderCellDragMouseupEvent;\n            }\n            return h('span', Object.assign({ key: 'dg', class: ['vxe-cell--drag-handle', {\n                        'is--disabled': isDisabled\n                    }] }, ons), columnDragIconSlot\n                ? $table.callSlot(columnDragIconSlot, params)\n                : [\n                    h('i', {\n                        class: icon || getIcon().TABLE_DRAG_COLUMN\n                    })\n                ]);\n        }\n    }\n    return renderEmptyElement($table);\n}\nfunction renderHeaderCellBaseVNs(params, content) {\n    const vns = [\n        renderTitlePrefixIcon(params),\n        renderHeaderCellDragIcon(params),\n        ...(XEUtils.isArray(content) ? content : [content]),\n        renderTitleSuffixIcon(params)\n    ];\n    return vns;\n}\nfunction renderTitleContent(params, content) {\n    const { $table, column } = params;\n    const tableProps = $table.props;\n    const tableReactData = $table.reactData;\n    const { computeTooltipOpts } = $table.getComputeMaps();\n    const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;\n    const { type, showHeaderOverflow } = column;\n    const tooltipOpts = computeTooltipOpts.value;\n    const showAllTip = tooltipOpts.showAll;\n    const headOverflow = XEUtils.isUndefined(showHeaderOverflow) || XEUtils.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;\n    const showTitle = headOverflow === 'title';\n    const showTooltip = headOverflow === true || headOverflow === 'tooltip';\n    const ons = {};\n    if (showTitle || showTooltip || showAllTip) {\n        ons.onMouseenter = (evnt) => {\n            if (tableReactData.isDragResize) {\n                return;\n            }\n            if (showTitle) {\n                updateCellTitle(evnt.currentTarget, column);\n            }\n            else if (showTooltip || showAllTip) {\n                $table.triggerHeaderTooltipEvent(evnt, params);\n            }\n        };\n    }\n    if (showTooltip || showAllTip) {\n        ons.onMouseleave = (evnt) => {\n            if (tableReactData.isDragResize) {\n                return;\n            }\n            if (showTooltip || showAllTip) {\n                $table.handleTargetLeaveEvent(evnt);\n            }\n        };\n    }\n    return [\n        type === 'html' && XEUtils.isString(content)\n            ? h('span', Object.assign({ class: 'vxe-cell--title', innerHTML: content }, ons))\n            : h('span', Object.assign({ class: 'vxe-cell--title' }, ons), getSlotVNs(content))\n    ];\n}\nfunction getFooterContent(params) {\n    const { $table, column, _columnIndex, items, row } = params;\n    const { slots, editRender, cellRender, footerFormatter } = column;\n    const renderOpts = editRender || cellRender;\n    const footerSlot = slots ? slots.footer : null;\n    if (footerSlot) {\n        return $table.callSlot(footerSlot, params);\n    }\n    let itemValue = '';\n    // 兼容老模式\n    if (XEUtils.isArray(items)) {\n        itemValue = items[_columnIndex];\n    }\n    else {\n        itemValue = XEUtils.get(row, column.field);\n    }\n    const footParams = Object.assign(params, {\n        itemValue\n    });\n    if (footerFormatter) {\n        if (XEUtils.isFunction(footerFormatter)) {\n            return [\n                h('span', {\n                    class: 'vxe-cell--label'\n                }, `${footerFormatter(footParams)}`)\n            ];\n        }\n        const isArr = XEUtils.isArray(footerFormatter);\n        const gFormatOpts = isArr ? formats.get(footerFormatter[0]) : formats.get(footerFormatter);\n        const footerFormatMethod = gFormatOpts ? gFormatOpts.tableFooterCellFormatMethod : null;\n        if (footerFormatMethod) {\n            return [\n                h('span', {\n                    class: 'vxe-cell--label'\n                }, `${isArr ? footerFormatMethod(footParams, ...footerFormatter.slice(1)) : footerFormatMethod(footParams)}`)\n            ];\n        }\n        return [\n            h('span', {\n                class: 'vxe-cell--label'\n            }, '')\n        ];\n    }\n    if (renderOpts) {\n        const compConf = renderer.get(renderOpts.name);\n        if (compConf) {\n            const rtFooter = compConf.renderTableFooter || compConf.renderFooter;\n            if (rtFooter) {\n                return getSlotVNs(rtFooter(renderOpts, footParams));\n            }\n        }\n    }\n    return [\n        h('span', {\n            class: 'vxe-cell--label'\n        }, formatText(itemValue, 1))\n    ];\n}\nfunction getDefaultCellLabel(params) {\n    const { $table, row, column } = params;\n    return formatText($table.getCellLabel(row, column), 1);\n}\nfunction renderCellHandle(params) {\n    const { column, $table } = params;\n    const tableProps = $table.props;\n    const { editConfig } = tableProps;\n    const { type, treeNode, rowGroupNode, editRender } = column;\n    const { computeEditOpts, computeCheckboxOpts } = $table.getComputeMaps();\n    const checkboxOpts = computeCheckboxOpts.value;\n    const editOpts = computeEditOpts.value;\n    const isDeepCell = treeNode || rowGroupNode;\n    switch (type) {\n        case 'seq':\n            return isDeepCell ? Cell.renderDeepIndexCell(params) : Cell.renderSeqCell(params);\n        case 'radio':\n            return isDeepCell ? Cell.renderDeepRadioCell(params) : Cell.renderRadioCell(params);\n        case 'checkbox':\n            return checkboxOpts.checkField ? (isDeepCell ? Cell.renderDeepSelectionCellByProp(params) : Cell.renderCheckboxCellByProp(params)) : (isDeepCell ? Cell.renderDeepSelectionCell(params) : Cell.renderCheckboxCell(params));\n        case 'expand':\n            return Cell.renderExpandCell(params);\n        case 'html':\n            return isDeepCell ? Cell.renderDeepHTMLCell(params) : Cell.renderHTMLCell(params);\n    }\n    if (isEnableConf(editConfig) && editRender) {\n        return editOpts.mode === 'cell' ? (isDeepCell ? Cell.renderDeepCellEdit(params) : Cell.renderCellEdit(params)) : (isDeepCell ? Cell.renderDeepRowEdit(params) : Cell.renderRowEdit(params));\n    }\n    return isDeepCell ? Cell.renderDeepCell(params) : Cell.renderDefaultCell(params);\n}\nfunction renderHeaderHandle(params) {\n    const { column, $table } = params;\n    const tableProps = $table.props;\n    const { editConfig } = tableProps;\n    const { type, filters, sortable, editRender } = column;\n    switch (type) {\n        case 'seq':\n            return Cell.renderSeqHeader(params);\n        case 'radio':\n            return Cell.renderRadioHeader(params);\n        case 'checkbox':\n            return Cell.renderCheckboxHeader(params);\n        case 'html':\n            if (filters && sortable) {\n                return Cell.renderSortAndFilterHeader(params);\n            }\n            else if (sortable) {\n                return Cell.renderSortHeader(params);\n            }\n            else if (filters) {\n                return Cell.renderFilterHeader(params);\n            }\n            break;\n    }\n    if (editConfig && editRender) {\n        return Cell.renderEditHeader(params);\n    }\n    else if (filters && sortable) {\n        return Cell.renderSortAndFilterHeader(params);\n    }\n    else if (sortable) {\n        return Cell.renderSortHeader(params);\n    }\n    else if (filters) {\n        return Cell.renderFilterHeader(params);\n    }\n    return Cell.renderDefaultHeader(params);\n}\nfunction renderFooterHandle(params) {\n    return Cell.renderDefaultFooter(params);\n}\nexport const Cell = {\n    createColumn($xeTable, columnOpts) {\n        const { type } = columnOpts;\n        const renConfs = {\n            renderHeader: renderHeaderHandle,\n            renderCell: renderCellHandle,\n            renderFooter: renderFooterHandle\n        };\n        if (type === 'expand') {\n            renConfs.renderData = Cell.renderExpandData;\n        }\n        return createColumn($xeTable, columnOpts, renConfs);\n    },\n    /**\n     * 列头标题\n     */\n    renderHeaderTitle(params) {\n        const { $table, column } = params;\n        const { slots, editRender, cellRender } = column;\n        const renderOpts = editRender || cellRender;\n        const headerSlot = slots ? slots.header : null;\n        if (headerSlot) {\n            return renderTitleContent(params, $table.callSlot(headerSlot, params));\n        }\n        if (renderOpts) {\n            const compConf = renderer.get(renderOpts.name);\n            if (compConf) {\n                const rtHeader = compConf.renderTableHeader || compConf.renderHeader;\n                if (rtHeader) {\n                    return renderTitleContent(params, getSlotVNs(rtHeader(renderOpts, params)));\n                }\n            }\n        }\n        return renderTitleContent(params, formatText(column.getTitle(), 1));\n    },\n    renderDefaultHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params));\n    },\n    renderDefaultCell(params) {\n        const { $table, row, column } = params;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { isRowGroupStatus } = tableReactData;\n        const { slots, editRender, cellRender, rowGroupNode } = column;\n        const renderOpts = editRender || cellRender;\n        const defaultSlot = slots ? slots.default : null;\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));\n        }\n        if (renderOpts) {\n            const compConf = renderer.get(renderOpts.name);\n            if (compConf) {\n                const rtCell = compConf.renderTableCell || compConf.renderCell;\n                const rtDefault = compConf.renderTableDefault || compConf.renderDefault;\n                const renderFn = editRender ? rtCell : rtDefault;\n                if (renderFn) {\n                    return renderCellBaseVNs(params, getSlotVNs(renderFn(renderOpts, Object.assign({ $type: editRender ? 'edit' : 'cell' }, params))));\n                }\n            }\n        }\n        let cellValue = '';\n        if (isRowGroupStatus && rowGroupNode && row.isAggregate) {\n            const { fullColumnFieldData } = tableInternalData;\n            const { computeRowGroupOpts } = $table.getComputeMaps();\n            const rowGroupOpts = computeRowGroupOpts.value;\n            const { showTotal, totalMethod, contentMethod, mapChildrenField } = rowGroupOpts;\n            const groupField = row.groupField;\n            cellValue = row.groupContent;\n            const childList = mapChildrenField ? (row[mapChildrenField] || []) : [];\n            const totalValue = childList.length;\n            const colRest = fullColumnFieldData[groupField] || {};\n            const params = {\n                $table,\n                groupField,\n                groupColumn: (colRest ? colRest.column : null),\n                column,\n                groupValue: cellValue,\n                children: childList,\n                totalValue: totalValue\n            };\n            if (contentMethod) {\n                cellValue = `${contentMethod(params)}`;\n            }\n            if (showTotal) {\n                cellValue = getI18n('vxe.table.rowGroupContentTotal', [cellValue, totalMethod ? totalMethod(params) : totalValue, totalValue]);\n            }\n        }\n        else if (!(isRowGroupStatus && row.isAggregate)) {\n            cellValue = $table.getCellLabel(row, column);\n        }\n        const cellPlaceholder = editRender ? editRender.placeholder : '';\n        return renderCellBaseVNs(params, [\n            h('span', {\n                class: 'vxe-cell--label'\n            }, [\n                // 如果设置占位符\n                editRender && eqEmptyValue(cellValue)\n                    ? h('span', {\n                        class: 'vxe-cell--placeholder'\n                    }, formatText(getFuncText(cellPlaceholder), 1))\n                    : h('span', formatText(cellValue, 1))\n            ])\n        ]);\n    },\n    renderDeepCell(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderDefaultCell(params));\n    },\n    renderDefaultFooter(params) {\n        return getFooterContent(params);\n    },\n    /**\n     * 行分组\n     */\n    renderRowGroupBtn(params, cellVNodes) {\n        const { $table } = params;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { row, level } = params;\n        const { computeRowGroupOpts } = $table.getComputeMaps();\n        const { rowGroupExpandedFlag } = tableReactData;\n        const { rowGroupExpandedMaps } = tableInternalData;\n        const rowGroupOpts = computeRowGroupOpts.value;\n        const { padding, indent } = rowGroupOpts;\n        const rowid = getRowid($table, row);\n        const isExpand = !!rowGroupExpandedFlag && !!rowGroupExpandedMaps[rowid];\n        return h('div', {\n            class: ['vxe-row-group--tree-node', {\n                    'is--expanded': isExpand\n                }],\n            style: padding && indent\n                ? {\n                    paddingLeft: `${level * indent}px`\n                }\n                : undefined\n        }, [\n            h('span', {\n                class: 'vxe-row-group--node-btn',\n                onClick(evnt) {\n                    $table.triggerRowGroupExpandEvent(evnt, params);\n                }\n            }, [\n                h('i', {\n                    class: isExpand ? getIcon().TABLE_ROW_GROUP_OPEN : getIcon().TABLE_ROW_GROUP_CLOSE\n                })\n            ]),\n            h('div', {\n                class: 'vxe-row-group-cell'\n            }, cellVNodes)\n        ]);\n    },\n    /**\n     * 树\n     */\n    renderTreeNodeBtn(params, cellVNodes) {\n        const { $table, isHidden } = params;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { row, column, level } = params;\n        const { slots } = column;\n        const iconSlot = slots ? slots.icon : null;\n        if (iconSlot) {\n            return $table.callSlot(iconSlot, params);\n        }\n        const { computeTreeOpts } = $table.getComputeMaps();\n        const { treeExpandedFlag } = tableReactData;\n        const { fullAllDataRowIdData, treeExpandedMaps, treeExpandLazyLoadedMaps } = tableInternalData;\n        const treeOpts = computeTreeOpts.value;\n        const { padding, indent, lazy, trigger, iconLoaded, showIcon, iconOpen, iconClose } = treeOpts;\n        const childrenField = treeOpts.children || treeOpts.childrenField;\n        const hasChildField = treeOpts.hasChild || treeOpts.hasChildField;\n        const rowChilds = row[childrenField];\n        const hasChild = rowChilds && rowChilds.length;\n        let hasLazyChilds = false;\n        let isActive = false;\n        let isLazyLoading = false;\n        let isLazyLoaded = false;\n        const ons = {};\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isActive = !!treeExpandedFlag && !!treeExpandedMaps[rowid];\n            if (lazy) {\n                const rest = fullAllDataRowIdData[rowid];\n                isLazyLoading = !!treeExpandLazyLoadedMaps[rowid];\n                hasLazyChilds = row[hasChildField];\n                isLazyLoaded = !!rest.treeLoaded;\n            }\n        }\n        if (!trigger || trigger === 'default') {\n            ons.onClick = (evnt) => {\n                $table.triggerTreeExpandEvent(evnt, params);\n            };\n        }\n        return h('div', {\n            class: ['vxe-cell--tree-node', {\n                    'is--active': isActive\n                }],\n            style: padding && indent\n                ? {\n                    paddingLeft: `${level * indent}px`\n                }\n                : undefined\n        }, [\n            showIcon && (lazy ? (isLazyLoaded ? hasChild : (hasChild || hasLazyChilds)) : hasChild)\n                ? [\n                    h('div', Object.assign({ class: 'vxe-cell--tree-btn' }, ons), [\n                        h('i', {\n                            class: isLazyLoading ? (iconLoaded || getIcon().TABLE_TREE_LOADED) : (isActive ? (iconOpen || getIcon().TABLE_TREE_OPEN) : (iconClose || getIcon().TABLE_TREE_CLOSE))\n                        })\n                    ])\n                ]\n                : null,\n            h('div', {\n                class: 'vxe-tree-cell'\n            }, cellVNodes)\n        ]);\n    },\n    /**\n     * 层级节点。\n     * 行分组、树结构\n     */\n    renderDeepNodeBtn(params, cellVNodes) {\n        const { row, column } = params;\n        const { rowGroupNode } = column;\n        if (rowGroupNode && row.isAggregate) {\n            return [Cell.renderRowGroupBtn(params, cellVNodes)];\n        }\n        return [Cell.renderTreeNodeBtn(params, cellVNodes)];\n    },\n    /**\n     * 序号\n     */\n    renderSeqHeader(params) {\n        const { $table, column } = params;\n        const { slots } = column;\n        const headerSlot = slots ? slots.header : null;\n        return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot ? $table.callSlot(headerSlot, params) : formatText(column.getTitle(), 1)));\n    },\n    renderSeqCell(params) {\n        const { $table, column } = params;\n        const tableProps = $table.props;\n        const { treeConfig } = tableProps;\n        const { computeSeqOpts } = $table.getComputeMaps();\n        const seqOpts = computeSeqOpts.value;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));\n        }\n        const { seq } = params;\n        const seqMethod = seqOpts.seqMethod;\n        return renderCellBaseVNs(params, [\n            h('span', `${formatText(seqMethod ? seqMethod(params) : treeConfig ? seq : (seqOpts.startIndex || 0) + seq, 1)}`)\n        ]);\n    },\n    renderDeepIndexCell(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderSeqCell(params));\n    },\n    /**\n     * 单选\n     */\n    renderRadioHeader(params) {\n        const { $table, column } = params;\n        const { slots } = column;\n        const headerSlot = slots ? slots.header : null;\n        const titleSlot = slots ? slots.title : null;\n        return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot\n            ? $table.callSlot(headerSlot, params)\n            : [\n                h('span', {\n                    class: 'vxe-radio--label'\n                }, titleSlot ? $table.callSlot(titleSlot, params) : formatText(column.getTitle(), 1))\n            ]));\n    },\n    renderRadioCell(params) {\n        const { $table, column, isHidden } = params;\n        const tableReactData = $table.reactData;\n        const { computeRadioOpts } = $table.getComputeMaps();\n        const { selectRadioRow } = tableReactData;\n        const radioOpts = computeRadioOpts.value;\n        const { slots } = column;\n        const { labelField, checkMethod, visibleMethod } = radioOpts;\n        const { row } = params;\n        const defaultSlot = slots ? slots.default : null;\n        const radioSlot = slots ? slots.radio : null;\n        const isChecked = $table.eqRow(row, selectRadioRow);\n        const isVisible = !visibleMethod || visibleMethod({ $table, row });\n        let isDisabled = !!checkMethod;\n        let ons;\n        if (!isHidden) {\n            ons = {\n                onClick(evnt) {\n                    if (!isDisabled && isVisible) {\n                        $table.triggerRadioRowEvent(evnt, params);\n                    }\n                }\n            };\n            if (checkMethod) {\n                isDisabled = !checkMethod({ $table, row });\n            }\n        }\n        const radioParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible });\n        if (radioSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(radioSlot, radioParams));\n        }\n        const radioVNs = [];\n        if (isVisible) {\n            radioVNs.push(h('span', {\n                class: ['vxe-radio--icon', isChecked ? getIcon().TABLE_RADIO_CHECKED : getIcon().TABLE_RADIO_UNCHECKED]\n            }));\n        }\n        if (defaultSlot || labelField) {\n            radioVNs.push(h('span', {\n                class: 'vxe-radio--label'\n            }, defaultSlot ? $table.callSlot(defaultSlot, radioParams) : XEUtils.get(row, labelField)));\n        }\n        return renderCellBaseVNs(params, [\n            h('span', Object.assign({ class: ['vxe-cell--radio', {\n                        'is--checked': isChecked,\n                        'is--disabled': isDisabled\n                    }] }, ons), radioVNs)\n        ]);\n    },\n    renderDeepRadioCell(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderRadioCell(params));\n    },\n    /**\n     * 多选\n     */\n    renderCheckboxHeader(params) {\n        const { $table, column, isHidden } = params;\n        const tableReactData = $table.reactData;\n        const { computeIsAllCheckboxDisabled, computeCheckboxOpts } = $table.getComputeMaps();\n        const { isAllSelected: isAllCheckboxSelected, isIndeterminate: isAllCheckboxIndeterminate } = tableReactData;\n        const isAllCheckboxDisabled = computeIsAllCheckboxDisabled.value;\n        const { slots } = column;\n        const headerSlot = slots ? slots.header : null;\n        const titleSlot = slots ? slots.title : null;\n        const checkboxOpts = computeCheckboxOpts.value;\n        const { checkStrictly, showHeader, headerTitle } = checkboxOpts;\n        const colTitle = column.getTitle();\n        const ons = {};\n        if (!isHidden) {\n            ons.onClick = (evnt) => {\n                if (!isAllCheckboxDisabled) {\n                    $table.triggerCheckAllEvent(evnt, !isAllCheckboxSelected);\n                }\n            };\n        }\n        const checkboxParams = Object.assign(Object.assign({}, params), { checked: isAllCheckboxSelected, disabled: isAllCheckboxDisabled, indeterminate: isAllCheckboxIndeterminate });\n        if (headerSlot) {\n            return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, $table.callSlot(headerSlot, checkboxParams)));\n        }\n        if (checkStrictly ? !showHeader : showHeader === false) {\n            return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [\n                h('span', {\n                    class: 'vxe-checkbox--label'\n                }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : colTitle)\n            ]));\n        }\n        return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [\n            h('span', Object.assign({ class: ['vxe-cell--checkbox', {\n                        'is--checked': isAllCheckboxSelected,\n                        'is--disabled': isAllCheckboxDisabled,\n                        'is--indeterminate': isAllCheckboxIndeterminate\n                    }], title: XEUtils.eqNull(headerTitle) ? getI18n('vxe.table.allTitle') : `${headerTitle || ''}` }, ons), [\n                h('span', {\n                    class: ['vxe-checkbox--icon', isAllCheckboxIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : (isAllCheckboxSelected ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED)]\n                })\n            ].concat(titleSlot || colTitle\n                ? [\n                    h('span', {\n                        class: 'vxe-checkbox--label'\n                    }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : colTitle)\n                ]\n                : []))\n        ]));\n    },\n    renderCheckboxCell(params) {\n        const { $table, row, column, isHidden } = params;\n        const tableProps = $table.props;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { treeConfig } = tableProps;\n        const { updateCheckboxFlag, isRowGroupStatus } = tableReactData;\n        const { selectCheckboxMaps, treeIndeterminateRowMaps } = tableInternalData;\n        const { computeCheckboxOpts } = $table.getComputeMaps();\n        const checkboxOpts = computeCheckboxOpts.value;\n        const { labelField, checkMethod, visibleMethod } = checkboxOpts;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const checkboxSlot = slots ? slots.checkbox : null;\n        let indeterminate = false;\n        let isChecked = false;\n        const isVisible = !visibleMethod || visibleMethod({ $table, row });\n        let isDisabled = !!checkMethod;\n        const ons = {};\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isChecked = !!updateCheckboxFlag && !!selectCheckboxMaps[rowid];\n            ons.onClick = (evnt) => {\n                if (!isDisabled && isVisible) {\n                    $table.triggerCheckRowEvent(evnt, params, !isChecked);\n                }\n            };\n            if (checkMethod) {\n                isDisabled = !checkMethod({ $table, row });\n            }\n            if (treeConfig || isRowGroupStatus) {\n                indeterminate = !!treeIndeterminateRowMaps[rowid];\n            }\n        }\n        const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate });\n        if (checkboxSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));\n        }\n        const checkVNs = [];\n        if (isVisible) {\n            checkVNs.push(h('span', {\n                class: ['vxe-checkbox--icon', indeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : (isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED)]\n            }));\n        }\n        if (defaultSlot || labelField) {\n            checkVNs.push(h('span', {\n                class: 'vxe-checkbox--label'\n            }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : XEUtils.get(row, labelField)));\n        }\n        return renderCellBaseVNs(params, [\n            h('span', Object.assign({ class: ['vxe-cell--checkbox', {\n                        'is--checked': isChecked,\n                        'is--disabled': isDisabled,\n                        'is--indeterminate': indeterminate,\n                        'is--hidden': !isVisible\n                    }] }, ons), checkVNs)\n        ]);\n    },\n    renderDeepSelectionCell(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderCheckboxCell(params));\n    },\n    renderCheckboxCellByProp(params) {\n        const { $table, row, column, isHidden } = params;\n        const tableProps = $table.props;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { treeConfig } = tableProps;\n        const { updateCheckboxFlag, isRowGroupStatus } = tableReactData;\n        const { treeIndeterminateRowMaps } = tableInternalData;\n        const { computeCheckboxOpts } = $table.getComputeMaps();\n        const checkboxOpts = computeCheckboxOpts.value;\n        const { labelField, checkField, checkMethod, visibleMethod } = checkboxOpts;\n        const indeterminateField = checkboxOpts.indeterminateField || checkboxOpts.halfField;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const checkboxSlot = slots ? slots.checkbox : null;\n        let isIndeterminate = false;\n        let isChecked = false;\n        const isVisible = !visibleMethod || visibleMethod({ $table, row });\n        let isDisabled = !!checkMethod;\n        const ons = {};\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isChecked = !!updateCheckboxFlag && XEUtils.get(row, checkField);\n            ons.onClick = (evnt) => {\n                if (!isDisabled && isVisible) {\n                    $table.triggerCheckRowEvent(evnt, params, !isChecked);\n                }\n            };\n            if (checkMethod) {\n                isDisabled = !checkMethod({ $table, row });\n            }\n            if (treeConfig || isRowGroupStatus) {\n                isIndeterminate = !!treeIndeterminateRowMaps[rowid];\n            }\n        }\n        const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate: isIndeterminate });\n        if (checkboxSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));\n        }\n        const checkVNs = [];\n        if (isVisible) {\n            checkVNs.push(h('span', {\n                class: ['vxe-checkbox--icon', isIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : (isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED)]\n            }));\n            if (defaultSlot || labelField) {\n                checkVNs.push(h('span', {\n                    class: 'vxe-checkbox--label'\n                }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : XEUtils.get(row, labelField)));\n            }\n        }\n        return renderCellBaseVNs(params, [\n            h('span', Object.assign({ class: ['vxe-cell--checkbox', {\n                        'is--checked': isChecked,\n                        'is--disabled': isDisabled,\n                        'is--indeterminate': indeterminateField && !isChecked ? row[indeterminateField] : isIndeterminate,\n                        'is--hidden': !isVisible\n                    }] }, ons), checkVNs)\n        ]);\n    },\n    renderDeepSelectionCellByProp(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderCheckboxCellByProp(params));\n    },\n    /**\n     * 展开行\n     */\n    renderExpandCell(params) {\n        const { $table, isHidden, row, column } = params;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { isRowGroupStatus } = tableReactData;\n        const { rowExpandedMaps, rowExpandLazyLoadedMaps } = tableInternalData;\n        const { computeExpandOpts } = $table.getComputeMaps();\n        const expandOpts = computeExpandOpts.value;\n        const { lazy, labelField, iconLoaded, showIcon, iconOpen, iconClose, visibleMethod } = expandOpts;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const iconSlot = slots ? slots.icon : null;\n        let isActive = false;\n        let isLazyLoading = false;\n        if (isRowGroupStatus && row.isAggregate) {\n            return renderCellBaseVNs(params, []);\n        }\n        if (iconSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(iconSlot, params));\n        }\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isActive = !!rowExpandedMaps[rowid];\n            if (lazy) {\n                isLazyLoading = !!rowExpandLazyLoadedMaps[rowid];\n            }\n        }\n        return renderCellBaseVNs(params, [\n            showIcon && (!visibleMethod || visibleMethod(params))\n                ? h('span', {\n                    class: ['vxe-table--expanded', {\n                            'is--active': isActive\n                        }],\n                    onMousedown(evnt) {\n                        evnt.stopPropagation();\n                    },\n                    onClick(evnt) {\n                        $table.triggerRowExpandEvent(evnt, params);\n                    }\n                }, [\n                    h('i', {\n                        class: ['vxe-table--expand-btn', isLazyLoading ? (iconLoaded || getIcon().TABLE_EXPAND_LOADED) : (isActive ? (iconOpen || getIcon().TABLE_EXPAND_OPEN) : (iconClose || getIcon().TABLE_EXPAND_CLOSE))]\n                    })\n                ])\n                : renderEmptyElement($table),\n            defaultSlot || labelField\n                ? h('span', {\n                    class: 'vxe-table--expand-label'\n                }, defaultSlot ? $table.callSlot(defaultSlot, params) : XEUtils.get(row, labelField))\n                : renderEmptyElement($table)\n        ]);\n    },\n    renderExpandData(params) {\n        const { $table, column } = params;\n        const { slots, contentRender } = column;\n        const contentSlot = slots ? slots.content : null;\n        if (contentSlot) {\n            return $table.callSlot(contentSlot, params);\n        }\n        if (contentRender) {\n            const compConf = renderer.get(contentRender.name);\n            if (compConf) {\n                const rtExpand = compConf.renderTableExpand || compConf.renderExpand;\n                if (rtExpand) {\n                    return getSlotVNs(rtExpand(contentRender, params));\n                }\n            }\n        }\n        return [];\n    },\n    /**\n     * HTML 标签\n     */\n    renderHTMLCell(params) {\n        const { $table, column } = params;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));\n        }\n        return renderCellBaseVNs(params, [\n            h('span', {\n                class: 'vxe-cell--html',\n                innerHTML: getDefaultCellLabel(params)\n            })\n        ]);\n    },\n    renderDeepHTMLCell(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderHTMLCell(params));\n    },\n    /**\n     * 排序和筛选\n     */\n    renderSortAndFilterHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params).concat(Cell.renderFilterIcon(params))));\n    },\n    /**\n     * 排序\n     */\n    renderSortHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params)));\n    },\n    renderSortIcon(params) {\n        const { $table, column } = params;\n        const { computeSortOpts } = $table.getComputeMaps();\n        const sortOpts = computeSortOpts.value;\n        const { showIcon, allowBtn, ascTitle, descTitle, iconLayout, iconAsc, iconDesc, iconVisibleMethod } = sortOpts;\n        const { order } = column;\n        if (showIcon && (!iconVisibleMethod || iconVisibleMethod(params))) {\n            return [\n                h('span', {\n                    class: ['vxe-cell--sort', `vxe-cell--sort-${iconLayout}-layout`]\n                }, [\n                    h('i', {\n                        class: ['vxe-sort--asc-btn', iconAsc || getIcon().TABLE_SORT_ASC, {\n                                'sort--active': order === 'asc'\n                            }],\n                        title: XEUtils.eqNull(ascTitle) ? getI18n('vxe.table.sortAsc') : `${ascTitle || ''}`,\n                        onClick: allowBtn\n                            ? (evnt) => {\n                                evnt.stopPropagation();\n                                $table.triggerSortEvent(evnt, column, 'asc');\n                            }\n                            : undefined\n                    }),\n                    h('i', {\n                        class: ['vxe-sort--desc-btn', iconDesc || getIcon().TABLE_SORT_DESC, {\n                                'sort--active': order === 'desc'\n                            }],\n                        title: XEUtils.eqNull(descTitle) ? getI18n('vxe.table.sortDesc') : `${descTitle || ''}`,\n                        onClick: allowBtn\n                            ? (evnt) => {\n                                evnt.stopPropagation();\n                                $table.triggerSortEvent(evnt, column, 'desc');\n                            }\n                            : undefined\n                    })\n                ])\n            ];\n        }\n        return [];\n    },\n    /**\n     * 筛选\n     */\n    renderFilterHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderFilterIcon(params)));\n    },\n    renderFilterIcon(params) {\n        const { $table, column, hasFilter } = params;\n        const tableReactData = $table.reactData;\n        const { filterStore } = tableReactData;\n        const { computeFilterOpts } = $table.getComputeMaps();\n        const filterOpts = computeFilterOpts.value;\n        const { showIcon, iconNone, iconMatch, iconVisibleMethod } = filterOpts;\n        if (showIcon && (!iconVisibleMethod || iconVisibleMethod(params))) {\n            return [\n                h('span', {\n                    class: ['vxe-cell--filter', {\n                            'is--active': filterStore.visible && filterStore.column === column\n                        }],\n                    onClick(evnt) {\n                        if ($table.triggerFilterEvent) {\n                            $table.triggerFilterEvent(evnt, params.column, params);\n                        }\n                    }\n                }, [\n                    h('i', {\n                        class: ['vxe-filter--btn', hasFilter ? (iconMatch || getIcon().TABLE_FILTER_MATCH) : (iconNone || getIcon().TABLE_FILTER_NONE)],\n                        title: getI18n('vxe.table.filter')\n                    })\n                ])\n            ];\n        }\n        return [];\n    },\n    /**\n     * 可编辑\n     */\n    renderEditHeader(params) {\n        const { $table, column } = params;\n        const tableProps = $table.props;\n        const { computeEditOpts } = $table.getComputeMaps();\n        const { editConfig, editRules } = tableProps;\n        const editOpts = computeEditOpts.value;\n        const { sortable, filters, editRender } = column;\n        let isRequired = false;\n        if (editRules) {\n            const columnRules = XEUtils.get(editRules, column.field);\n            if (columnRules) {\n                isRequired = columnRules.some((rule) => rule.required);\n            }\n        }\n        let editIconVNs = [];\n        if (isEnableConf(editConfig)) {\n            editIconVNs = [\n                isRequired && editOpts.showAsterisk\n                    ? h('i', {\n                        class: 'vxe-cell--required-icon'\n                    })\n                    : renderEmptyElement($table),\n                isEnableConf(editRender) && editOpts.showIcon\n                    ? h('i', {\n                        class: ['vxe-cell--edit-icon', editOpts.icon || getIcon().TABLE_EDIT]\n                    })\n                    : renderEmptyElement($table)\n            ];\n        }\n        return renderHeaderCellBaseVNs(params, editIconVNs.concat(Cell.renderHeaderTitle(params))\n            .concat(sortable ? Cell.renderSortIcon(params) : [])\n            .concat(filters ? Cell.renderFilterIcon(params) : []));\n    },\n    // 行格编辑模式\n    renderRowEdit(params) {\n        const { $table, column } = params;\n        const tableReactData = $table.reactData;\n        const { editStore } = tableReactData;\n        const { actived } = editStore;\n        const { editRender } = column;\n        return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row);\n    },\n    renderDeepRowEdit(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderRowEdit(params));\n    },\n    // 单元格编辑模式\n    renderCellEdit(params) {\n        const { $table, column } = params;\n        const tableReactData = $table.reactData;\n        const { editStore } = tableReactData;\n        const { actived } = editStore;\n        const { editRender } = column;\n        return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row && actived.column === params.column);\n    },\n    renderDeepCellEdit(params) {\n        return Cell.renderDeepNodeBtn(params, Cell.renderCellEdit(params));\n    },\n    runRenderer(params, isEdit) {\n        const { $table, column } = params;\n        const { slots, editRender, formatter } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const editSlot = slots ? slots.edit : null;\n        const compConf = renderer.get(editRender.name);\n        const rtEdit = compConf ? (compConf.renderTableEdit || compConf.renderEdit) : null;\n        const cellParams = Object.assign({ $type: '', isEdit }, params);\n        if (isEdit) {\n            cellParams.$type = 'edit';\n            if (editSlot) {\n                return $table.callSlot(editSlot, cellParams);\n            }\n            if (rtEdit) {\n                return getSlotVNs(rtEdit(editRender, cellParams));\n            }\n            return [];\n        }\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, cellParams));\n        }\n        if (formatter) {\n            return renderCellBaseVNs(params, [\n                h('span', {\n                    class: 'vxe-cell--label'\n                }, getDefaultCellLabel(cellParams))\n            ]);\n        }\n        return Cell.renderDefaultCell(cellParams);\n    }\n};\nexport default Cell;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sBAAoB;AACpB,IAAM,WAAW,CAAC;AAClB,IAAI;AACG,SAAS,YAAY;AACxB,MAAI,CAAC,SAAS;AACV,cAAU,IAAI,MAAM;AACpB,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO;AACX;AACO,SAAS,WAAW;AACvB,MAAI,CAAC,SAAS;AACV,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AACX;AACO,SAAS,aAAa,UAAU,QAAQ;AAC3C,SAAO,WAAW,gBAAAA,QAAQ,WAAW,QAAQ,IAAI,SAAS,MAAM,IAAI,WAAW;AACnF;AACA,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,aAAS,GAAG,IAAI,IAAI,OAAO,YAAY,GAAG,WAAW,GAAG;AAAA,EAC5D;AACA,SAAO,SAAS,GAAG;AACvB;AACA,SAAS,cAAc,MAAM,WAAW,MAAM;AAC1C,MAAI,MAAM;AACN,UAAM,aAAa,KAAK;AACxB,SAAK,OAAO,KAAK;AACjB,SAAK,QAAQ,KAAK;AAClB,QAAI,cAAc,eAAe,SAAS,mBAAmB,eAAe,SAAS,MAAM;AACvF,WAAK,OAAO,WAAW;AACvB,WAAK,QAAQ,WAAW;AAAA,IAC5B;AACA,QAAI,cAAc,SAAS,aAAa,KAAK,iBAAiB,aAAa,IAAI,KAAK,cAAc;AAC9F,aAAO,cAAc,KAAK,cAAc,WAAW,IAAI;AAAA,IAC3D;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,KAAK,KAAK;AACtB,SAAO,OAAO,aAAa,KAAK,GAAG;AACvC;AACO,SAAS,QAAQ,KAAK;AACzB,SAAO,OAAO,SAAS,KAAK,GAAG;AACnC;AACO,SAAS,SAAS,MAAM,KAAK;AAChC,SAAO,CAAC,EAAE,QAAQ,KAAK,aAAa,KAAK,UAAU,SAAS,KAAK,UAAU,MAAM,SAAS,GAAG,CAAC;AAClG;AACO,SAAS,YAAY,MAAM,KAAK;AACnC,MAAI,QAAQ,SAAS,MAAM,GAAG,GAAG;AAC7B,SAAK,YAAY,KAAK,UAAU,QAAQ,SAAS,GAAG,GAAG,EAAE;AAAA,EAC7D;AACJ;AACO,SAAS,SAAS,MAAM,KAAK;AAChC,MAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,GAAG;AAC9B,gBAAY,MAAM,GAAG;AACrB,SAAK,YAAY,GAAG,KAAK,SAAS,IAAI,GAAG;AAAA,EAC7C;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,KAAK,WAAW,KAAK;AAChC;AACO,SAAS,UAAU,KAAK,OAAO,MAAM;AACxC,MAAI,gBAAAA,QAAQ,SAAS,GAAG,KAAK,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG;AACjD,WAAO,GAAG,GAAG,GAAG,IAAI;AAAA,EACxB;AACA,SAAO,GAAG,OAAO,EAAE;AACvB;AACO,SAAS,aAAa;AACzB,QAAM,kBAAkB,SAAS;AACjC,QAAM,WAAW,SAAS;AAC1B,SAAO;AAAA,IACH,WAAW,gBAAgB,aAAa,SAAS;AAAA,IACjD,YAAY,gBAAgB,cAAc,SAAS;AAAA,IACnD,eAAe,gBAAgB,gBAAgB,SAAS;AAAA,IACxD,cAAc,gBAAgB,eAAe,SAAS;AAAA,EAC1D;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,OAAO,KAAK,eAAe;AACtC;AACO,SAAS,wBAAwB,MAAM;AAC1C,MAAI,MAAM;AACN,UAAM,gBAAgB,iBAAiB,IAAI;AAC3C,UAAM,aAAa,gBAAAA,QAAQ,SAAS,cAAc,UAAU;AAC5D,UAAM,gBAAgB,gBAAAA,QAAQ,SAAS,cAAc,aAAa;AAClE,WAAO,aAAa;AAAA,EACxB;AACA,SAAO;AACX;AACO,SAAS,aAAa,MAAM,WAAW;AAC1C,MAAI,MAAM;AACN,SAAK,YAAY;AAAA,EACrB;AACJ;AACO,SAAS,cAAc,MAAM,YAAY;AAC5C,MAAI,MAAM;AACN,SAAK,aAAa;AAAA,EACtB;AACJ;AAOO,SAAS,gBAAgB,cAAc,QAAQ;AAClD,QAAM,UAAU,OAAO,SAAS,SAAS,aAAa,YAAY,aAAa;AAC/E,MAAI,aAAa,aAAa,OAAO,MAAM,SAAS;AAChD,iBAAa,aAAa,SAAS,OAAO;AAAA,EAC9C;AACJ;AAIO,SAAS,mBAAmB,MAAM,WAAW,UAAU,aAAa;AACvE,MAAI;AACJ,MAAI,SAAU,KAAK,OAAO,cAAc,KAAK,WAAa,KAAK,aAAa,EAAE,CAAC,KAAK,KAAK,SAAU,KAAK;AACxG,SAAO,UAAU,OAAO,YAAY,WAAW,UAAU;AACrD,QAAI,YAAY,SAAS,QAAQ,QAAQ,MAAM,CAAC,eAAe,YAAY,MAAM,IAAI;AACjF,mBAAa;AAAA,IACjB,WACS,WAAW,WAAW;AAC3B,aAAO,EAAE,MAAM,WAAW,CAAC,CAAC,aAAa,MAAM,WAAW,WAAuB;AAAA,IACrF;AACA,aAAS,OAAO;AAAA,EACpB;AACA,SAAO,EAAE,MAAM,MAAM;AACzB;AAIO,SAAS,aAAa,MAAM,WAAW;AAC1C,SAAO,cAAc,MAAM,WAAW,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;AAC7D;AACO,SAAS,eAAe,MAAM;AACjC,QAAM,WAAW,KAAK,sBAAsB;AAC5C,QAAM,cAAc,SAAS;AAC7B,QAAM,eAAe,SAAS;AAC9B,QAAM,EAAE,WAAW,YAAY,eAAe,aAAa,IAAI,WAAW;AAC1E,SAAO,EAAE,aAAa,KAAK,YAAY,aAAa,cAAc,MAAM,aAAa,cAAc,eAAe,aAAa;AACnI;AACA,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AAChB,SAAS,aAAa,MAAM;AAC/B,MAAI,MAAM;AACN,QAAI,KAAK,sBAAsB,GAAG;AAC9B,WAAK,sBAAsB,EAAE;AAAA,IACjC,WACS,KAAK,cAAc,GAAG;AAC3B,WAAK,cAAc,EAAE;AAAA,IACzB;AAAA,EACJ;AACJ;AACO,SAAS,aAAa,YAAY,MAAM;AAC3C,MAAI,YAAY;AACZ,eAAW,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,EAC5C;AACJ;;;AC9JA,IAAAC,mBAAoB;;;ACDpB,IAAAC,mBAAoB;AAKpB,IAAM,EAAE,SAAS,QAAQ,IAAI;AACtB,IAAM,aAAN,MAAiB;AAAA;AAAA,EAEpB,YAAY,UAAU,KAAK,EAAE,cAAc,YAAY,cAAc,WAAW,IAAI,CAAC,GAAG;AACpF,UAAM,aAAa,SAAS;AAC5B,UAAM,UAAU,SAAS;AACzB,UAAM,YAAY,IAAI;AACtB,UAAM,UAAU,iBAAAC,QAAQ,UAAU,IAAI,OAAO,IAAI,IAAI,UAAU;AAC/D,UAAM,QAAQ,CAAC,OAAO,YAAY,SAAS,UAAU,MAAM;AAC3D,QAAI,IAAI,QAAQ,MAAM,QAAQ,IAAI,IAAI,MAAM,IAAI;AAC5C,cAAQ,qBAAqB,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,IACvE;AACA,QAAI,iBAAAA,QAAQ,UAAU,IAAI,UAAU,KAAM,IAAI,cAAc,CAAC,iBAAAA,QAAQ,SAAS,IAAI,UAAU,GAAI;AAC5F,cAAQ,qBAAqB,CAAC,sBAAsB,IAAI,UAAU,IAAI,uBAAuB,CAAC;AAAA,IAClG;AACA,QAAI,iBAAAA,QAAQ,UAAU,IAAI,UAAU,KAAM,IAAI,cAAc,CAAC,iBAAAA,QAAQ,SAAS,IAAI,UAAU,GAAI;AAC5F,cAAQ,qBAAqB,CAAC,sBAAsB,IAAI,UAAU,IAAI,uBAAuB,CAAC;AAAA,IAClG;AACA,QAAI,IAAI,SAAS,UAAU;AACvB,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,EAAE,gBAAgB,IAAI,SAAS,eAAe;AACpD,YAAM,WAAW,gBAAgB;AACjC,UAAI,eAAe,SAAS,YAAY,SAAS,OAAO;AACpD,eAAO,0BAA0B,CAAC,wBAAwB,oBAAoB,CAAC;AAAA,MACnF;AAAA,IACJ;AACA,QAAI,WAAW;AACX,UAAI,iBAAAA,QAAQ,SAAS,SAAS,GAAG;AAC7B,cAAM,cAAc,QAAQ,IAAI,SAAS,KAAK,iBAAAA,QAAQ,SAAS;AAC/D,YAAI,CAAC,eAAe,CAAC,iBAAAA,QAAQ,WAAW,YAAY,yBAAyB,YAAY,gBAAgB,GAAG;AACxG,iBAAO,wBAAwB,CAAC,SAAS,CAAC;AAAA,QAC9C;AAAA,MACJ,WACS,iBAAAA,QAAQ,QAAQ,SAAS,GAAG;AACjC,cAAM,cAAc,QAAQ,IAAI,UAAU,CAAC,CAAC,KAAK,iBAAAA,QAAQ,UAAU,CAAC,CAAC;AACrE,YAAI,CAAC,eAAe,CAAC,iBAAAA,QAAQ,WAAW,YAAY,yBAAyB,YAAY,gBAAgB,GAAG;AACxG,iBAAO,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,OAAO,MAAM;AAAA;AAAA,MAEhB,MAAM,IAAI;AAAA,MACV,UAAU,IAAI;AAAA,MACd,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,MACd,WAAW,IAAI;AAAA,MACf,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,aAAa,IAAI;AAAA,MACjB,aAAa,IAAI;AAAA,MACjB,cAAc,IAAI;AAAA,MAClB,oBAAoB,IAAI;AAAA,MACxB,oBAAoB,IAAI;AAAA,MACxB,WAAW,IAAI;AAAA,MACf,iBAAiB,IAAI;AAAA,MACrB,iBAAiB,IAAI;AAAA,MACrB;AAAA,MACA,iBAAiB,IAAI;AAAA,MACrB,SAAS,IAAI;AAAA,MACb,eAAe,IAAI;AAAA,MACnB,UAAU,IAAI;AAAA,MACd,QAAQ,IAAI;AAAA,MACZ,UAAU,IAAI;AAAA,MACd,SAAS,UAAU,IAAI,OAAO;AAAA,MAC9B,gBAAgB,iBAAAA,QAAQ,UAAU,IAAI,cAAc,IAAI,IAAI,iBAAiB;AAAA,MAC7E,cAAc,IAAI;AAAA,MAClB,mBAAmB,IAAI;AAAA,MACvB,qBAAqB,IAAI;AAAA,MACzB,cAAc,IAAI;AAAA,MAClB,cAAc,IAAI;AAAA,MAClB,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,MACd,WAAW,IAAI;AAAA,MACf,UAAU,IAAI;AAAA,MACd,YAAY,IAAI;AAAA,MAChB,YAAY,IAAI;AAAA,MAChB,eAAe,IAAI;AAAA,MACnB,oBAAoB,IAAI;AAAA,MACxB,cAAc,IAAI;AAAA,MAClB,oBAAoB,IAAI;AAAA,MACxB,WAAW,IAAI;AAAA,MACf,aAAa,IAAI;AAAA,MACjB,aAAa,IAAI;AAAA;AAAA,MAEjB,QAAQ,IAAI;AAAA;AAAA,MAEZ,IAAI,IAAI,SAAS,iBAAAA,QAAQ,SAAS,MAAM;AAAA,MACxC,UAAU;AAAA,MACV;AAAA;AAAA,MAEA,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc,IAAI;AAAA,MAClB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA;AAAA,MAEV,OAAO;AAAA;AAAA,MAEP,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;AAAA,MAET,OAAO;AAAA,MACP,UAAU;AAAA;AAAA,MAEV,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY,CAAC;AAAA,MACb,OAAO,CAAC;AAAA,MACR,cAAc,gBAAgB,IAAI;AAAA,MAClC,YAAY,cAAc,IAAI;AAAA,MAC9B,cAAc,gBAAgB,IAAI;AAAA,MAClC;AAAA;AAAA,MAEA,OAAO,IAAI;AAAA,IACf,CAAC;AACD,QAAI,SAAS;AACT,YAAM,EAAE,iBAAiB,IAAI,QAAQ,eAAe;AACpD,YAAM,YAAY,iBAAiB;AACnC,UAAI,UAAU,cAAc;AACxB,kBAAU,aAAa,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC;AAAA,MAC3D;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAO,YAAY,KAAK,UAAU,KAAK,SAAS,QAAQ,QAAQ,oBAAoB,IAAI,GAAG;AAAA,EAC/F;AAAA,EACA,SAAS;AACL,UAAM,EAAE,KAAK,IAAI;AACjB,WAAO,KAAK,UAAU,OAAO,QAAQ,IAAI,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,MAAM,OAAO;AAEhB,QAAI,SAAS,WAAW;AACpB,UAAI,SAAS,SAAS;AAElB,aAAK,WAAW;AAAA,MACpB;AACA,WAAK,IAAI,IAAI;AAAA,IACjB;AAAA,EACJ;AACJ;;;AD1JA,IAAM,uBAAuB,CAAC,SAAS,iBAAiB;AACpD,QAAM,SAAS,CAAC;AAChB,UAAQ,QAAQ,CAAC,WAAW;AACxB,WAAO,WAAW,eAAe,aAAa,KAAK;AACnD,QAAI,OAAO,SAAS;AAChB,UAAI,OAAO,YAAY,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,CAACC,YAAWA,QAAO,OAAO,GAAG;AAC/F,eAAO,KAAK,MAAM;AAClB,eAAO,KAAK,GAAG,qBAAqB,OAAO,UAAU,MAAM,CAAC;AAAA,MAChE,OACK;AACD,eAAO,KAAK,MAAM;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACO,IAAM,4BAA4B,CAAC,kBAAkB;AACxD,MAAI,WAAW;AACf,QAAM,WAAW,CAAC,QAAQ,WAAW;AACjC,QAAI,QAAQ;AACR,aAAO,QAAQ,OAAO,QAAQ;AAC9B,UAAI,WAAW,OAAO,OAAO;AACzB,mBAAW,OAAO;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,OAAO,YAAY,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,CAACA,YAAWA,QAAO,OAAO,GAAG;AAC/F,UAAI,UAAU;AACd,aAAO,SAAS,QAAQ,CAAC,cAAc;AACnC,YAAI,UAAU,SAAS;AACnB,mBAAS,WAAW,MAAM;AAC1B,qBAAW,UAAU;AAAA,QACzB;AAAA,MACJ,CAAC;AACD,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,UAAU;AAAA,IACrB;AAAA,EACJ;AACA,gBAAc,QAAQ,CAAC,WAAW;AAC9B,WAAO,QAAQ;AACf,aAAS,MAAM;AAAA,EACnB,CAAC;AACD,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,SAAK,KAAK,CAAC,CAAC;AAAA,EAChB;AACA,QAAM,aAAa,qBAAqB,aAAa;AACrD,aAAW,QAAQ,CAAC,WAAW;AAC3B,QAAI,OAAO,YAAY,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,CAACA,YAAWA,QAAO,OAAO,GAAG;AAC/F,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,UAAU,WAAW,OAAO,QAAQ;AAAA,IAC/C;AACA,SAAK,OAAO,QAAQ,CAAC,EAAE,KAAK,MAAM;AAAA,EACtC,CAAC;AACD,SAAO;AACX;AACO,SAAS,sBAAsB,UAAU,YAAY,WAAW;AACnE,QAAM,eAAe,SAAS;AAC9B,MAAI,cAAc,WAAW;AACzB,iBAAa,gBAAgB;AAC7B,iBAAa,kBAAkB;AAC/B,iBAAa,gBAAgB;AAC7B,iBAAa,iBAAiB;AAC9B,iBAAa,eAAe;AAC5B,iBAAa,iBAAiB;AAC9B,iBAAa,mBAAmB;AAEhC,WAAO,SAAS,SAAS,YAAY,SAAS;AAAA,EAClD;AACA,SAAO,SAAS;AACpB;AAIO,SAAS,iBAAiB;AAC7B,SAAO,iBAAAC,QAAQ,SAAS,MAAM;AAClC;AAEO,SAAS,UAAU,UAAU;AAChC,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,EAAE,eAAe,IAAI,SAAS,eAAe;AACnD,QAAM,UAAU,eAAe;AAC/B,SAAO,GAAG,MAAM,SAAS,QAAQ,YAAY,YAAY;AAC7D;AAEO,SAAS,SAAS,UAAU,KAAK;AACpC,QAAM,QAAQ,iBAAAA,QAAQ,IAAI,KAAK,UAAU,QAAQ,CAAC;AAClD,SAAO,YAAY,KAAK;AAC5B;AACO,SAAS,wBAAwB,UAAU;AAC9C,QAAM,SAAS,UAAU,QAAQ;AACjC,QAAM,YAAY,OAAO,QAAQ,GAAG,IAAI;AACxC,QAAM,YAAY,YAAY,mBAAmB;AACjD,SAAO;AAAA,IACH;AAAA,IACA,kBAAkB,KAAK;AACnB,aAAO,MAAM,UAAU,KAAK,MAAM,IAAI;AAAA,IAC1C;AAAA,EACJ;AACJ;AACO,SAAS,qBAAqB,UAAU;AAC3C,QAAM,SAAS,UAAU,QAAQ;AACjC,QAAM,YAAY,OAAO,QAAQ,GAAG,IAAI;AACxC,QAAM,SAAS,YAAY,oBAAoB;AAC/C,SAAO;AAAA,IACH;AAAA,IACA,eAAe,KAAK;AAChB,aAAO,MAAM,OAAO,KAAK,MAAM,IAAI;AAAA,IACvC;AAAA,EACJ;AACJ;AAEO,SAAS,YAAY,QAAQ;AAChC,SAAO,iBAAAA,QAAQ,OAAO,MAAM,IAAI,KAAK,mBAAmB,MAAM;AAClE;AACA,SAAS,kBAAkB,KAAK,QAAQ;AACpC,SAAO,iBAAAA,QAAQ,IAAI,KAAK,MAAM;AAClC;AACO,SAAS,iBAAiB,KAAK,QAAQ;AAC1C,MAAI,QAAQ,kBAAkB,KAAK,MAAM;AACzC,MAAI,aAAa,KAAK,GAAG;AACrB,YAAQ,eAAe;AACvB,qBAAAA,QAAQ,IAAI,KAAK,QAAQ,KAAK;AAAA,EAClC;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,KAAK,QAAQ;AACpC,SAAO,IAAI,MAAM;AACrB;AACO,SAAS,iBAAiB,KAAK,QAAQ;AAC1C,MAAI,QAAQ,kBAAkB,KAAK,MAAM;AACzC,MAAI,aAAa,KAAK,GAAG;AACrB,YAAQ,eAAe;AACvB,QAAI,MAAM,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACO,IAAM,sBAAsB,CAAC,UAAU,kBAAkB;AAC5D,MAAI,eAAe;AACf,WAAO,iBAAAA,QAAQ,SAAS,aAAa,KAAK,iBAAAA,QAAQ,SAAS,aAAa,IAAI,SAAS,iBAAiB,GAAG,aAAa,EAAE,IAAI;AAAA,EAChI;AACA,SAAO;AACX;AACO,IAAM,mBAAmB,CAAC,UAAU,eAAe;AACtD,MAAI,YAAY;AACZ,UAAM,QAAQ,iBAAAA,QAAQ,SAAS,UAAU,KAAK,iBAAAA,QAAQ,SAAS,UAAU,IAAI,aAAa,SAAS,UAAU,UAAU;AACvH,WAAO,SAAS,WAAW,KAAK;AAAA,EACpC;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,MAAM;AACnC,MAAI,MAAM;AACN,UAAM,gBAAgB,iBAAiB,IAAI;AAC3C,UAAM,cAAc,iBAAAA,QAAQ,SAAS,cAAc,WAAW;AAC9D,UAAM,eAAe,iBAAAA,QAAQ,SAAS,cAAc,YAAY;AAChE,WAAO,cAAc;AAAA,EACzB;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,MAAM;AACjC,MAAI,MAAM;AACN,UAAM,gBAAgB,iBAAiB,IAAI;AAC3C,UAAM,aAAa,iBAAAA,QAAQ,SAAS,cAAc,UAAU;AAC5D,UAAM,cAAc,iBAAAA,QAAQ,SAAS,cAAc,WAAW;AAC9D,WAAO,KAAK,cAAc,aAAa;AAAA,EAC3C;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,UAAU;AACtC,SAAO,KAAK,cAAc,cAAc,QAAQ;AACpD;AACO,SAAS,UAAU,SAAS;AAC/B,MAAI,WAAW,iBAAAA,QAAQ,QAAQ,OAAO,GAAG;AACrC,WAAO,QAAQ,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,YAAY,QAAQ,MAAM;AAChE,aAAO,EAAE,OAAO,OAAO,MAAM,YAAY,SAAS,CAAC,CAAC,SAAS,UAAU,CAAC,CAAC,QAAQ;AAAA,IACrF,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,IAAK,OAAO,GAAG,IAAI,IAAK,GAAG,EAAE,KAAK,EAAE;AAC9E;AACO,SAAS,aAAa,KAAK,QAAQ;AACtC,SAAO,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AACxC;AACO,SAAS,aAAa,KAAK,QAAQ,OAAO;AAC7C,SAAO,iBAAAA,QAAQ,IAAI,KAAK,OAAO,OAAO,KAAK;AAC/C;AACO,SAAS,WAAW,OAAO;AAC9B,MAAI,OAAO;AACP,UAAM,OAAO,MAAM;AACnB,QAAI,MAAM;AACN,aAAQ,KAAK,OAAO;AAAA,IACxB;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,cAAc,QAAQ;AAClC,MAAI,WAAW,SAAS;AACpB,WAAO;AAAA,EACX;AACA,SAAO,UAAU;AACrB;AAyBO,SAAS,iBAAiB,QAAQ;AACrC,QAAM,EAAE,QAAQ,QAAQ,KAAK,IAAI;AACjC,QAAM,aAAa,OAAO;AAC1B,QAAM,eAAe,OAAO;AAC5B,QAAM,EAAE,qBAAqB,IAAI,OAAO,eAAe;AACvD,QAAM,gBAAgB,qBAAqB;AAC3C,QAAM,EAAE,UAAU,WAAW,IAAI;AAEjC,MAAI,YAAY;AACZ,UAAM,iBAAiB,iBAAAC,QAAQ,WAAW,UAAU,IAAI,WAAW,MAAM,IAAI;AAC7E,QAAI,mBAAmB,QAAQ;AAC3B,aAAO,KAAK,IAAI,GAAG,iBAAAA,QAAQ,SAAS,cAAc,CAAC;AAAA,IACvD;AAAA,EACJ;AACA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,oBAAoB,wBAAwB,IAAI;AACxD,QAAM,EAAE,oBAAoB,UAAU,YAAY,IAAI;AACtD,QAAM,eAAe,iBAAAA,QAAQ,YAAY,kBAAkB,KAAK,iBAAAA,QAAQ,OAAO,kBAAkB,IAAI,0BAA0B;AAC/H,QAAM,eAAe,iBAAiB;AACtC,QAAM,YAAY,iBAAiB;AACnC,QAAM,cAAc,iBAAiB,QAAQ,iBAAiB;AAC9D,QAAM,cAAc,aAAa,eAAe;AAChD,QAAM,gBAAgB,iBAAAA,QAAQ,OAAO,iBAAAA,QAAQ,SAAS,iBAAiB,IAAI,EAAE,QAAQ,KAAK,MAAM,GAAG;AACnG,QAAM,mBAAmB,wBAAwB,IAAI,IAAI,wBAAwB,iBAAiB,MAAM,EAAE,CAAC;AAC3G,MAAI,SAAS,gBAAgB;AAE7B,MAAI,aAAa;AACb,UAAM,gBAAgB,wBAAwB,iBAAiB,MAAM,yBAAyB,CAAC;AAC/F,UAAM,oBAAoB,wBAAwB,iBAAiB,MAAM,sBAAsB,CAAC;AAChG,UAAM,oBAAoB,sBAAsB,iBAAiB,MAAM,2BAA2B,CAAC;AACnG,UAAM,gBAAgB,sBAAsB,iBAAiB,MAAM,uBAAuB,CAAC;AAC3F,UAAM,kBAAkB,sBAAsB,iBAAiB,MAAM,8BAA8B,CAAC;AACpG,UAAM,kBAAkB,sBAAsB,iBAAiB,MAAM,8BAA8B,CAAC;AACpG,UAAM,gBAAgB,sBAAsB,iBAAiB,MAAM,kBAAkB,CAAC;AACtF,UAAM,kBAAkB,sBAAsB,iBAAiB,MAAM,oBAAoB,CAAC;AAC1F,cAAU,gBAAgB,oBAAoB,oBAAoB,gBAAgB,kBAAkB,kBAAkB,kBAAkB;AAAA,EAC5I;AAEA,MAAI,aAAa;AACb,UAAM,iBAAiB,WAAW,UAAU,kBAAkB,CAAC;AAC/D,QAAI,gBAAgB;AAChB,UAAI,QAAQ,WAAW,GAAG;AACtB,cAAM,YAAY,eAAe,cAAc;AAC/C,cAAM,YAAY,YAAY;AAC9B,eAAO,KAAK,IAAI,QAAQ,KAAK,MAAM,iBAAAA,QAAQ,UAAU,WAAW,IAAI,SAAS,CAAC;AAAA,MAClF,WACS,KAAK,WAAW,GAAG;AACxB,eAAO,KAAK,IAAI,QAAQ,iBAAAA,QAAQ,UAAU,WAAW,CAAC;AAAA,MAC1D;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,aAAa,QAAQ;AACjC,SAAO,WAAW,OAAO,gBAAgB,cAAc,kBAAkB;AAC7E;AACO,SAAS,aAAa,UAAU,SAAS,eAAe;AAC3D,SAAO,aAAa,OAAO,IAAI,UAAU,SAAS,IAAI,WAAW,UAAU,SAAS,aAAa,CAAC;AACtG;AACO,SAAS,YAAY,UAAU,OAAO,QAAQ;AACjD,SAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AAC/B,UAAM,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU;AAChC,aAAO,OAAO,MAAM,KAAK;AACzB,UAAI,UAAU;AACV,YAAI,SAAS,WAAW;AACpB,mBAAS,UAAU,QAAQ,KAAK;AAChC,mBAAS,sBAAsB;AAAA,QACnC,WACS,CAAC,WAAW,SAAS,SAAS,YAAY,UAAU,EAAE,SAAS,IAAI,GAAG;AAC3E,mBAAS,yBAAyB;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACO,SAAS,eAAe,UAAU,MAAM,QAAQ,UAAU;AAC7D,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,aAAa,KAAK;AACxB,QAAM,eAAe,WAAW,SAAS,eAAe;AACxD,QAAM,aAAa,eAAe,aAAa,WAAW;AAC1D,MAAI,cAAc,YAAY;AAC1B,eAAW,OAAO,iBAAAA,QAAQ,aAAa,WAAW,UAAU,IAAI,GAAG,GAAG,MAAM;AAC5E,cAAU,gBAAgB,cAAc,MAAM,CAAC;AAAA,EACnD;AACJ;AACO,SAAS,cAAc,UAAU,QAAQ;AAC5C,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,WAAW,iBAAAA,QAAQ,SAAS,eAAe,UAAQ,KAAK,OAAO,OAAO,IAAI,EAAE,UAAU,WAAW,CAAC;AACxG,MAAI,UAAU;AACV,aAAS,MAAM,OAAO,SAAS,OAAO,CAAC;AAAA,EAC3C;AACA,YAAU,gBAAgB,cAAc,MAAM,CAAC;AACnD;AACO,SAAS,cAAc,UAAU,QAAQ;AAC5C,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,iBAAiB,IAAI;AAC7B,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,MAAI,cAAc,OAAO;AACzB,SAAO,iBAAiB,WAAW,GAAG;AAClC,UAAMC,UAAS,iBAAiB,WAAW,EAAE;AAC7C,kBAAcA,QAAO;AACrB,QAAI,CAAC,aAAa;AACd,aAAOA;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AACX;AACA,IAAM,kBAAkB,CAAC,SAAS,WAAW;AACzC,MAAI,QAAQ;AACZ,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,QAAM,WAAW,gBAAgB;AACjC,QAAM,EAAE,WAAW,iBAAiB,IAAI;AACxC,QAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,QAAM,cAAc,QAAQ,YAAY,mBAAmB,aAAa;AACxE,MAAI,eAAe,OAAO,kBAAkB,OAAO,GAAG;AAClD,aAAS,QAAQ,GAAG,QAAQ,YAAY,QAAQ,SAAS;AACrD,eAAS,gBAAgB,YAAY,KAAK,GAAG,MAAM;AAAA,IACvD;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAM,gBAAgB,CAAC,aAAa;AACvC,QAAM,EAAE,YAAY,IAAI,SAAS,eAAe;AAChD,QAAM,QAAQ,YAAY;AAC1B,MAAI,OAAO;AACP,WAAO,gBAAgB,KAAK,KAAK;AAAA,EACrC;AACA,SAAO;AACX;AACO,SAAS,aAAa,QAAQ,SAAS;AAC1C,QAAM,EAAE,QAAQ,IAAI,IAAI;AACxB,QAAM,aAAa,OAAO;AAC1B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,oBAAoB,OAAO;AACjC,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,EAAE,qBAAqB,IAAI;AACjC,QAAM,EAAE,gBAAgB,iBAAiB,wBAAwB,IAAI,OAAO,eAAe;AAC3F,QAAM,UAAU,eAAe;AAC/B,QAAM,WAAW,gBAAgB;AACjC,QAAM,mBAAmB,wBAAwB;AACjD,QAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,QAAM,UAAU,qBAAqB,KAAK;AAC1C,QAAM,iBAAiB,QAAQ,gBAAgB,SAAS,UAAU,QAAQ,UAAU,QAAQ,UAAU;AACtG,MAAI,aAAa;AACjB,MAAI,SAAS;AACT,iBAAa,gBAAgB,SAAS,MAAM;AAAA,EAChD;AACA,MAAI,aAAa;AACjB,QAAM,WAAW,QAAQ;AACzB,MAAI,aAAa;AACb,QAAI,CAAC,cAAc;AACf,mBAAa,YAAY;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO,aAAa,cAAc,UAAU,IAAK,KAAK,cAAc,MAAM;AAC9E;AACO,SAAS,wBAAwB,UAAU;AAC9C,QAAM,EAAE,OAAO,aAAa,IAAI;AAChC,eAAa,aAAa;AAC1B,WAAS,UAAU;AACnB,WAAS,gBAAgB;AACzB,WAAS,mBAAmB;AAC5B,WAAS,cAAc;AACvB,WAAS,kBAAkB;AAC3B,WAAS,iBAAiB;AAC1B,WAAS,qBAAqB;AAC9B,WAAS,eAAe;AACxB,WAAS,gBAAgB;AACzB,WAAS,uBAAuB;AAChC,WAAS,gBAAgB;AACzB,MAAI,SAAS,aAAa;AACtB,aAAS,YAAY;AAAA,EACzB;AACA,MAAI,SAAS,kBAAkB,MAAM,kBAAkB,MAAM,cAAc;AACvE,aAAS,cAAc;AAAA,EAC3B;AACA,MAAI,SAAS,kBAAkB,MAAM,aAAa;AAC9C,aAAS,eAAe;AACxB,aAAS,kBAAkB;AAAA,EAC/B;AACA,SAAO,SAAS,YAAY;AAChC;AACO,SAAS,oBAAoB,UAAU;AAC1C,MAAI,SAAS,aAAa;AACtB,aAAS,YAAY;AAAA,EACzB;AACA,SAAO,wBAAwB,QAAQ;AAC3C;AACO,SAAS,aAAa,UAAU,KAAK;AACxC,QAAM,aAAa,SAAS;AAC5B,QAAM,YAAY,SAAS;AAC3B,QAAM,eAAe,SAAS;AAC9B,QAAM,EAAE,uBAAuB,wBAAwB,gBAAgB,iBAAiB,wBAAwB,IAAI,SAAS,eAAe;AAC5I,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,EAAE,WAAW,eAAe,sBAAsB,mBAAmB,IAAI;AAC/E,QAAM,UAAU,eAAe;AAC/B,QAAM,WAAW,gBAAgB;AACjC,QAAM,mBAAmB,wBAAwB;AACjD,QAAM,iBAAiB,sBAAsB;AAC7C,QAAM,kBAAkB,uBAAuB;AAC/C,QAAM,iBAAiB,WAAW,UAAU,kBAAkB,CAAC;AAC/D,QAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,MAAI,gBAAgB;AAChB,UAAM,aAAa,eAAe;AAClC,UAAM,gBAAgB,eAAe;AACrC,UAAM,SAAS,eAAe,cAAc,WAAW,KAAK,IAAI;AAChE,QAAI,QAAQ;AACR,YAAM,iBAAiB,OAAO;AAC9B,YAAM,cAAc,OAAO,aAAa,iBAAiB,eAAe,YAAY;AACpF,YAAM,WAAW,OAAO;AAExB,UAAI,cAAc,iBAAiB,cAAc,gBAAgB,YAAY;AACzE,eAAO,SAAS,SAAS,MAAM,WAAW;AAAA,MAC9C,WACS,cAAc,YAAY,aAAa,eAAe;AAC3D,eAAO,SAAS,SAAS,MAAM,gBAAgB,QAAQ;AAAA,MAC3D;AAAA,IACJ,OACK;AAED,UAAI,aAAa;AACb,cAAM,qBAAqB,sBAAsB,SAAS,UAAU,QAAQ;AAC5E,YAAI,CAAC,sBAAsB,cAAc;AACrC,iBAAO,SAAS,SAAS,OAAO,SAAS,eAAe,eAAe,GAAG,IAAI,KAAK,gBAAgB;AAAA,QACvG;AACA,YAAI,YAAY;AAChB,cAAM,UAAU,qBAAqB,KAAK,KAAK,CAAC;AAChD,cAAM,UAAU,QAAQ,gBAAgB,SAAS,UAAU,QAAQ,UAAU,QAAQ,UAAU;AAC/F,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,gBAAM,UAAU,cAAc,CAAC;AAC/B,gBAAM,YAAY,SAAS,UAAU,OAAO;AAC5C,cAAI,YAAY,OAAO,cAAc,OAAO;AACxC;AAAA,UACJ;AACA,gBAAM,cAAc,qBAAqB,SAAS,KAAK,CAAC;AACxD,uBAAa,YAAY,gBAAgB,SAAS,UAAU,QAAQ,UAAU,YAAY,UAAU;AAAA,QACxG;AACA,YAAI,YAAY,eAAe;AAC3B,iBAAO,SAAS,SAAS,MAAM,YAAY,iBAAiB,CAAC;AAAA,QACjE;AACA,eAAO,SAAS,SAAS,MAAO,YAAY,WAAY,aAAa,kBAAkB,EAAE;AAAA,MAC7F;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ,QAAQ;AAC3B;AACO,SAAS,aAAa,UAAU,QAAQ,KAAK;AAChD,QAAM,YAAY,SAAS;AAC3B,QAAM,eAAe,SAAS;AAC9B,QAAM,EAAE,uBAAuB,uBAAuB,IAAI,SAAS,eAAe;AAClF,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,EAAE,WAAW,cAAc,IAAI;AACrC,QAAM,iBAAiB,sBAAsB;AAC7C,QAAM,kBAAkB,uBAAuB;AAC/C,QAAM,iBAAiB,WAAW,UAAU,kBAAkB,CAAC;AAC/D,MAAI,OAAO,OAAO;AACd,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AACA,MAAI,gBAAgB;AAChB,UAAM,YAAY,eAAe;AACjC,UAAM,iBAAiB,eAAe;AACtC,QAAI,SAAS;AACb,QAAI,KAAK;AACL,YAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,eAAS,eAAe,cAAc,WAAW,KAAK,OAAO,OAAO,EAAE,EAAE;AAAA,IAC5E;AACA,QAAI,CAAC,QAAQ;AACT,eAAS,eAAe,cAAc,IAAI,OAAO,EAAE,EAAE;AAAA,IACzD;AACA,QAAI,QAAQ;AACR,YAAM,iBAAiB,OAAO;AAC9B,YAAM,eAAe,OAAO,cAAc,iBAAiB,eAAe,aAAa;AACvF,YAAM,YAAY,OAAO;AAEzB,UAAI,eAAgB,iBAAiB,gBAAiB;AAClD,eAAO,SAAS,SAAS,eAAe,iBAAiB,CAAC;AAAA,MAC9D,WACU,eAAe,YAAY,iBAAmB,YAAY,iBAAkB;AAClF,eAAO,SAAS,SAAU,eAAe,aAAc,YAAY,kBAAkB,EAAE;AAAA,MAC3F;AAAA,IACJ,OACK;AAED,UAAI,aAAa;AACb,YAAI,aAAa;AACjB,cAAM,YAAY,OAAO;AACzB,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,gBAAM,UAAU,cAAc,CAAC;AAC/B,cAAI,YAAY,UAAU,QAAQ,OAAO,OAAO,IAAI;AAChD;AAAA,UACJ;AACA,wBAAc,QAAQ;AAAA,QAC1B;AACA,YAAI,aAAa,gBAAgB;AAC7B,iBAAO,SAAS,SAAS,aAAa,iBAAiB,CAAC;AAAA,QAC5D;AACA,eAAO,SAAS,SAAU,aAAa,aAAc,YAAY,kBAAkB,EAAE;AAAA,MACzF;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ,QAAQ;AAC3B;;;AEviBA,IAAAC,mBAAoB;AAMpB,IAAM,EAAE,SAAAC,UAAS,SAAS,UAAU,SAAAC,UAAS,mBAAmB,IAAI;AACpE,SAAS,sBAAsB,QAAQ;AACnC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,cAAc,OAAO,eAAe,OAAO;AACjD,MAAI,aAAa;AACb,WAAO,EAAE,KAAK;AAAA,MACV,OAAO,CAAC,8BAA8B,YAAY,aAAa,UAAU,YAAY,UAAU,KAAK,IAAI,YAAY,QAAQ,QAAQ,EAAE,kBAAkB;AAAA,MACxJ,aAAa,MAAM;AACf,eAAO,wBAAwB,MAAM,aAAa,MAAM;AAAA,MAC5D;AAAA,MACA,aAAa,MAAM;AACf,eAAO,uBAAuB,IAAI;AAAA,MACtC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO,mBAAmB,MAAM;AACpC;AACA,SAAS,sBAAsB,QAAQ;AACnC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,cAAc,OAAO;AAC3B,MAAI,aAAa;AACb,WAAO,EAAE,KAAK;AAAA,MACV,OAAO,CAAC,8BAA8B,YAAY,aAAa,UAAU,YAAY,UAAU,KAAK,IAAI,YAAY,QAAQ,QAAQ,EAAE,kBAAkB;AAAA,MACxJ,aAAa,MAAM;AACf,eAAO,wBAAwB,MAAM,aAAa,MAAM;AAAA,MAC5D;AAAA,MACA,aAAa,MAAM;AACf,eAAO,uBAAuB,IAAI;AAAA,MACtC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO,mBAAmB,MAAM;AACpC;AACA,SAAS,mBAAmB,QAAQ;AAChC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,EAAE,QAAQ,IAAI;AACpB,QAAM,aAAa,QAAQ;AAC3B,QAAM,aAAa,OAAO;AAC1B,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,mBAAmB,IAAI,OAAO,eAAe;AACrD,QAAM,cAAc,mBAAmB;AACvC,QAAM,EAAE,MAAM,SAAS,eAAe,IAAI;AAC1C,QAAM,kBAAkB,mBAAmB,aAAa,WAAW,oBAAoB;AACvF,QAAM,aAAa,mBAAmB,gBAAgB,MAAM;AAC5D,QAAM,mBAAmB,QAAQ,MAAM,eAAe,MAAM,eAAe,IAAI,SAAS,WAAW,eAAe,WAAW,eAAe;AAC5I,QAAM,MAAM,CAAC;AACb,MAAI,YAAY,QAAQ;AACpB,QAAI,cAAc,CAAC,SAAS;AACxB,UAAI,CAAC,YAAY;AACb,eAAO,6BAA6B,MAAM,MAAM;AAAA,MACpD;AAAA,IACJ;AACA,QAAI,YAAY,OAAO;AAAA,EAC3B;AACA,SAAO,EAAE,QAAQ,OAAO,OAAO,EAAE,KAAK,MAAM,OAAO,CAAC,yBAAyB;AAAA,IACjE,gBAAgB;AAAA,EACpB,CAAC,EAAE,GAAG,GAAG,GAAG,kBACd,OAAO,SAAS,iBAAiB,MAAM,IACvC;AAAA,IACE,EAAE,KAAK;AAAA,MACH,OAAO,SAAS,aAAa,WAAW,UAAU,OAAO,QAAQ,EAAE;AAAA,IACvE,CAAC;AAAA,EACL,CAAC;AACT;AACA,SAAS,kBAAkB,QAAQ,SAAS;AACxC,QAAM,EAAE,QAAQ,QAAQ,MAAM,IAAI;AAClC,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,aAAa,OAAO;AAC1B,QAAM,EAAE,YAAY,WAAW,IAAI;AACnC,QAAM,EAAE,gBAAgB,oBAAoB,gBAAgB,IAAI,OAAO,eAAe;AACtF,QAAM,UAAU,eAAe;AAC/B,QAAM,cAAc,mBAAmB;AACvC,QAAM,WAAW,gBAAgB;AACjC,QAAM,EAAE,UAAU,YAAY,aAAa,cAAc,IAAI;AAC7D,QAAM,iBAAiB,kBAAkB,aAAa,WAAW,mBAAmB;AACpF,QAAM,MAAM,CAAC;AACb,MAAI,YAAY,QAAQ,UAAU,aAAa,aAAa,WAAW,cAAc,YAAY,CAAC,kBAAkB,eAAe,MAAM,KAAK;AAC1I,QAAI,YAAY;AACZ,UAAI,SAAS,cAAc,cAAc,eAAe,CAAC,QAAQ;AAC7D,YAAI,KAAK,mBAAmB,MAAM,CAAC;AAAA,MACvC;AAAA,IACJ,OACK;AACD,UAAI,KAAK,mBAAmB,MAAM,CAAC;AAAA,IACvC;AAAA,EACJ;AACA,SAAO,IAAI,OAAO,iBAAAC,QAAQ,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC;AACpE;AACA,SAAS,yBAAyB,QAAQ;AACtC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,EAAE,QAAQ,IAAI;AACpB,QAAM,aAAa,QAAQ;AAC3B,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,EAAE,mBAAmB,sBAAsB,IAAI,OAAO,eAAe;AAC3E,QAAM,aAAa,kBAAkB;AACrC,QAAM,iBAAiB,sBAAsB;AAC7C,QAAM,EAAE,UAAU,MAAM,SAAS,YAAY,aAAa,eAAe,eAAe,IAAI;AAC5F,MAAI,WAAW,QAAQ,aAAa,CAAC,iBAAiB,cAAc,MAAM,IAAI;AAC1E,QAAI,CAAC,OAAO,UAAU,cAAc,eAAe,CAAC,OAAO,WAAW;AAClE,YAAM,aAAa,kBAAkB,eAAe,MAAM;AAC1D,YAAM,sBAAsB,QAAQ,MAAM,kBAAkB,MAAM,kBAAkB,IAAI,SAAS,WAAW,kBAAkB,WAAW,kBAAkB;AAC3J,YAAM,MAAM,CAAC;AACb,UAAI,YAAY,QAAQ;AACpB,YAAI,cAAc,CAAC,SAAS;AACxB,cAAI,CAAC,YAAY;AACb,mBAAO,mCAAmC,MAAM,MAAM;AAAA,UAC1D;AAAA,QACJ;AACA,YAAI,YAAY,OAAO;AAAA,MAC3B;AACA,aAAO,EAAE,QAAQ,OAAO,OAAO,EAAE,KAAK,MAAM,OAAO,CAAC,yBAAyB;AAAA,QACjE,gBAAgB;AAAA,MACpB,CAAC,EAAE,GAAG,GAAG,GAAG,qBACd,OAAO,SAAS,oBAAoB,MAAM,IAC1C;AAAA,QACE,EAAE,KAAK;AAAA,UACH,OAAO,QAAQ,QAAQ,EAAE;AAAA,QAC7B,CAAC;AAAA,MACL,CAAC;AAAA,IACT;AAAA,EACJ;AACA,SAAO,mBAAmB,MAAM;AACpC;AACA,SAAS,wBAAwB,QAAQ,SAAS;AAC9C,QAAM,MAAM;AAAA,IACR,sBAAsB,MAAM;AAAA,IAC5B,yBAAyB,MAAM;AAAA,IAC/B,GAAI,iBAAAA,QAAQ,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAAA,IACjD,sBAAsB,MAAM;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,QAAQ,SAAS;AACzC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,aAAa,OAAO;AAC1B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,EAAE,mBAAmB,IAAI,OAAO,eAAe;AACrD,QAAM,EAAE,oBAAoB,wBAAwB,IAAI;AACxD,QAAM,EAAE,MAAM,mBAAmB,IAAI;AACrC,QAAM,cAAc,mBAAmB;AACvC,QAAM,aAAa,YAAY;AAC/B,QAAM,eAAe,iBAAAA,QAAQ,YAAY,kBAAkB,KAAK,iBAAAA,QAAQ,OAAO,kBAAkB,IAAI,0BAA0B;AAC/H,QAAM,YAAY,iBAAiB;AACnC,QAAM,cAAc,iBAAiB,QAAQ,iBAAiB;AAC9D,QAAM,MAAM,CAAC;AACb,MAAI,aAAa,eAAe,YAAY;AACxC,QAAI,eAAe,CAAC,SAAS;AACzB,UAAI,eAAe,cAAc;AAC7B;AAAA,MACJ;AACA,UAAI,WAAW;AACX,wBAAgB,KAAK,eAAe,MAAM;AAAA,MAC9C,WACS,eAAe,YAAY;AAChC,eAAO,0BAA0B,MAAM,MAAM;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,eAAe,YAAY;AAC3B,QAAI,eAAe,CAAC,SAAS;AACzB,UAAI,eAAe,cAAc;AAC7B;AAAA,MACJ;AACA,UAAI,eAAe,YAAY;AAC3B,eAAO,uBAAuB,IAAI;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH,SAAS,UAAU,iBAAAA,QAAQ,SAAS,OAAO,IACrC,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,mBAAmB,WAAW,QAAQ,GAAG,GAAG,CAAC,IAC9E,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,kBAAkB,GAAG,GAAG,GAAG,WAAW,OAAO,CAAC;AAAA,EACzF;AACJ;AACA,SAAS,iBAAiB,QAAQ;AAC9B,QAAM,EAAE,QAAQ,QAAQ,cAAc,OAAO,IAAI,IAAI;AACrD,QAAM,EAAE,OAAO,YAAY,YAAY,gBAAgB,IAAI;AAC3D,QAAM,aAAa,cAAc;AACjC,QAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,MAAI,YAAY;AACZ,WAAO,OAAO,SAAS,YAAY,MAAM;AAAA,EAC7C;AACA,MAAI,YAAY;AAEhB,MAAI,iBAAAA,QAAQ,QAAQ,KAAK,GAAG;AACxB,gBAAY,MAAM,YAAY;AAAA,EAClC,OACK;AACD,gBAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAAA,EAC7C;AACA,QAAM,aAAa,OAAO,OAAO,QAAQ;AAAA,IACrC;AAAA,EACJ,CAAC;AACD,MAAI,iBAAiB;AACjB,QAAI,iBAAAA,QAAQ,WAAW,eAAe,GAAG;AACrC,aAAO;AAAA,QACH,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,GAAG,gBAAgB,UAAU,CAAC,EAAE;AAAA,MACvC;AAAA,IACJ;AACA,UAAM,QAAQ,iBAAAA,QAAQ,QAAQ,eAAe;AAC7C,UAAM,cAAc,QAAQD,SAAQ,IAAI,gBAAgB,CAAC,CAAC,IAAIA,SAAQ,IAAI,eAAe;AACzF,UAAM,qBAAqB,cAAc,YAAY,8BAA8B;AACnF,QAAI,oBAAoB;AACpB,aAAO;AAAA,QACH,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,GAAG,QAAQ,mBAAmB,YAAY,GAAG,gBAAgB,MAAM,CAAC,CAAC,IAAI,mBAAmB,UAAU,CAAC,EAAE;AAAA,MAChH;AAAA,IACJ;AACA,WAAO;AAAA,MACH,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG,EAAE;AAAA,IACT;AAAA,EACJ;AACA,MAAI,YAAY;AACZ,UAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,QAAI,UAAU;AACV,YAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,UAAI,UAAU;AACV,eAAO,WAAW,SAAS,YAAY,UAAU,CAAC;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH,EAAE,QAAQ;AAAA,MACN,OAAO;AAAA,IACX,GAAG,WAAW,WAAW,CAAC,CAAC;AAAA,EAC/B;AACJ;AACA,SAAS,oBAAoB,QAAQ;AACjC,QAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,SAAO,WAAW,OAAO,aAAa,KAAK,MAAM,GAAG,CAAC;AACzD;AACA,SAAS,iBAAiB,QAAQ;AAC9B,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,aAAa,OAAO;AAC1B,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,MAAM,UAAU,cAAc,WAAW,IAAI;AACrD,QAAM,EAAE,iBAAiB,oBAAoB,IAAI,OAAO,eAAe;AACvE,QAAM,eAAe,oBAAoB;AACzC,QAAM,WAAW,gBAAgB;AACjC,QAAM,aAAa,YAAY;AAC/B,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,aAAa,KAAK,oBAAoB,MAAM,IAAI,KAAK,cAAc,MAAM;AAAA,IACpF,KAAK;AACD,aAAO,aAAa,KAAK,oBAAoB,MAAM,IAAI,KAAK,gBAAgB,MAAM;AAAA,IACtF,KAAK;AACD,aAAO,aAAa,aAAc,aAAa,KAAK,8BAA8B,MAAM,IAAI,KAAK,yBAAyB,MAAM,IAAM,aAAa,KAAK,wBAAwB,MAAM,IAAI,KAAK,mBAAmB,MAAM;AAAA,IAC5N,KAAK;AACD,aAAO,KAAK,iBAAiB,MAAM;AAAA,IACvC,KAAK;AACD,aAAO,aAAa,KAAK,mBAAmB,MAAM,IAAI,KAAK,eAAe,MAAM;AAAA,EACxF;AACA,MAAI,aAAa,UAAU,KAAK,YAAY;AACxC,WAAO,SAAS,SAAS,SAAU,aAAa,KAAK,mBAAmB,MAAM,IAAI,KAAK,eAAe,MAAM,IAAM,aAAa,KAAK,kBAAkB,MAAM,IAAI,KAAK,cAAc,MAAM;AAAA,EAC7L;AACA,SAAO,aAAa,KAAK,eAAe,MAAM,IAAI,KAAK,kBAAkB,MAAM;AACnF;AACA,SAAS,mBAAmB,QAAQ;AAChC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,aAAa,OAAO;AAC1B,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,MAAM,SAAS,UAAU,WAAW,IAAI;AAChD,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,KAAK,gBAAgB,MAAM;AAAA,IACtC,KAAK;AACD,aAAO,KAAK,kBAAkB,MAAM;AAAA,IACxC,KAAK;AACD,aAAO,KAAK,qBAAqB,MAAM;AAAA,IAC3C,KAAK;AACD,UAAI,WAAW,UAAU;AACrB,eAAO,KAAK,0BAA0B,MAAM;AAAA,MAChD,WACS,UAAU;AACf,eAAO,KAAK,iBAAiB,MAAM;AAAA,MACvC,WACS,SAAS;AACd,eAAO,KAAK,mBAAmB,MAAM;AAAA,MACzC;AACA;AAAA,EACR;AACA,MAAI,cAAc,YAAY;AAC1B,WAAO,KAAK,iBAAiB,MAAM;AAAA,EACvC,WACS,WAAW,UAAU;AAC1B,WAAO,KAAK,0BAA0B,MAAM;AAAA,EAChD,WACS,UAAU;AACf,WAAO,KAAK,iBAAiB,MAAM;AAAA,EACvC,WACS,SAAS;AACd,WAAO,KAAK,mBAAmB,MAAM;AAAA,EACzC;AACA,SAAO,KAAK,oBAAoB,MAAM;AAC1C;AACA,SAAS,mBAAmB,QAAQ;AAChC,SAAO,KAAK,oBAAoB,MAAM;AAC1C;AACO,IAAM,OAAO;AAAA,EAChB,aAAa,UAAU,YAAY;AAC/B,UAAM,EAAE,KAAK,IAAI;AACjB,UAAM,WAAW;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AACA,QAAI,SAAS,UAAU;AACnB,eAAS,aAAa,KAAK;AAAA,IAC/B;AACA,WAAO,aAAa,UAAU,YAAY,QAAQ;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ;AACtB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,OAAO,YAAY,WAAW,IAAI;AAC1C,UAAM,aAAa,cAAc;AACjC,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,QAAI,YAAY;AACZ,aAAO,mBAAmB,QAAQ,OAAO,SAAS,YAAY,MAAM,CAAC;AAAA,IACzE;AACA,QAAI,YAAY;AACZ,YAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,UAAI,UAAU;AACV,cAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,YAAI,UAAU;AACV,iBAAO,mBAAmB,QAAQ,WAAW,SAAS,YAAY,MAAM,CAAC,CAAC;AAAA,QAC9E;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,mBAAmB,QAAQ,WAAW,OAAO,SAAS,GAAG,CAAC,CAAC;AAAA,EACtE;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,CAAC;AAAA,EACzE;AAAA,EACA,kBAAkB,QAAQ;AACtB,UAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,iBAAiB,IAAI;AAC7B,UAAM,EAAE,OAAO,YAAY,YAAY,aAAa,IAAI;AACxD,UAAM,aAAa,cAAc;AACjC,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,MAAM,CAAC;AAAA,IACzE;AACA,QAAI,YAAY;AACZ,YAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,UAAI,UAAU;AACV,cAAM,SAAS,SAAS,mBAAmB,SAAS;AACpD,cAAM,YAAY,SAAS,sBAAsB,SAAS;AAC1D,cAAM,WAAW,aAAa,SAAS;AACvC,YAAI,UAAU;AACV,iBAAO,kBAAkB,QAAQ,WAAW,SAAS,YAAY,OAAO,OAAO,EAAE,OAAO,aAAa,SAAS,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;AAAA,QACrI;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,YAAY;AAChB,QAAI,oBAAoB,gBAAgB,IAAI,aAAa;AACrD,YAAM,EAAE,oBAAoB,IAAI;AAChC,YAAM,EAAE,oBAAoB,IAAI,OAAO,eAAe;AACtD,YAAM,eAAe,oBAAoB;AACzC,YAAM,EAAE,WAAW,aAAa,eAAe,iBAAiB,IAAI;AACpE,YAAM,aAAa,IAAI;AACvB,kBAAY,IAAI;AAChB,YAAM,YAAY,mBAAoB,IAAI,gBAAgB,KAAK,CAAC,IAAK,CAAC;AACtE,YAAM,aAAa,UAAU;AAC7B,YAAM,UAAU,oBAAoB,UAAU,KAAK,CAAC;AACpD,YAAME,UAAS;AAAA,QACX;AAAA,QACA;AAAA,QACA,aAAc,UAAU,QAAQ,SAAS;AAAA,QACzC;AAAA,QACA,YAAY;AAAA,QACZ,UAAU;AAAA,QACV;AAAA,MACJ;AACA,UAAI,eAAe;AACf,oBAAY,GAAG,cAAcA,OAAM,CAAC;AAAA,MACxC;AACA,UAAI,WAAW;AACX,oBAAYH,SAAQ,kCAAkC,CAAC,WAAW,cAAc,YAAYG,OAAM,IAAI,YAAY,UAAU,CAAC;AAAA,MACjI;AAAA,IACJ,WACS,EAAE,oBAAoB,IAAI,cAAc;AAC7C,kBAAY,OAAO,aAAa,KAAK,MAAM;AAAA,IAC/C;AACA,UAAM,kBAAkB,aAAa,WAAW,cAAc;AAC9D,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG;AAAA;AAAA,QAEC,cAAc,aAAa,SAAS,IAC9B,EAAE,QAAQ;AAAA,UACR,OAAO;AAAA,QACX,GAAG,WAAW,YAAY,eAAe,GAAG,CAAC,CAAC,IAC5C,EAAE,QAAQ,WAAW,WAAW,CAAC,CAAC;AAAA,MAC5C,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,eAAe,QAAQ;AACnB,WAAO,KAAK,kBAAkB,QAAQ,KAAK,kBAAkB,MAAM,CAAC;AAAA,EACxE;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,iBAAiB,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ,YAAY;AAClC,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,KAAK,MAAM,IAAI;AACvB,UAAM,EAAE,oBAAoB,IAAI,OAAO,eAAe;AACtD,UAAM,EAAE,qBAAqB,IAAI;AACjC,UAAM,EAAE,qBAAqB,IAAI;AACjC,UAAM,eAAe,oBAAoB;AACzC,UAAM,EAAE,SAAS,OAAO,IAAI;AAC5B,UAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,UAAM,WAAW,CAAC,CAAC,wBAAwB,CAAC,CAAC,qBAAqB,KAAK;AACvE,WAAO,EAAE,OAAO;AAAA,MACZ,OAAO,CAAC,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,MACpB,CAAC;AAAA,MACL,OAAO,WAAW,SACZ;AAAA,QACE,aAAa,GAAG,QAAQ,MAAM;AAAA,MAClC,IACE;AAAA,IACV,GAAG;AAAA,MACC,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,MAAM;AACV,iBAAO,2BAA2B,MAAM,MAAM;AAAA,QAClD;AAAA,MACJ,GAAG;AAAA,QACC,EAAE,KAAK;AAAA,UACH,OAAO,WAAW,QAAQ,EAAE,uBAAuB,QAAQ,EAAE;AAAA,QACjE,CAAC;AAAA,MACL,CAAC;AAAA,MACD,EAAE,OAAO;AAAA,QACL,OAAO;AAAA,MACX,GAAG,UAAU;AAAA,IACjB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ,YAAY;AAClC,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,KAAK,QAAQ,MAAM,IAAI;AAC/B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,WAAW,QAAQ,MAAM,OAAO;AACtC,QAAI,UAAU;AACV,aAAO,OAAO,SAAS,UAAU,MAAM;AAAA,IAC3C;AACA,UAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,UAAM,EAAE,iBAAiB,IAAI;AAC7B,UAAM,EAAE,sBAAsB,kBAAkB,yBAAyB,IAAI;AAC7E,UAAM,WAAW,gBAAgB;AACjC,UAAM,EAAE,SAAS,QAAQ,MAAM,SAAS,YAAY,UAAU,UAAU,UAAU,IAAI;AACtF,UAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,UAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,UAAM,YAAY,IAAI,aAAa;AACnC,UAAM,WAAW,aAAa,UAAU;AACxC,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,QAAI,gBAAgB;AACpB,QAAI,eAAe;AACnB,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,iBAAW,CAAC,CAAC,oBAAoB,CAAC,CAAC,iBAAiB,KAAK;AACzD,UAAI,MAAM;AACN,cAAM,OAAO,qBAAqB,KAAK;AACvC,wBAAgB,CAAC,CAAC,yBAAyB,KAAK;AAChD,wBAAgB,IAAI,aAAa;AACjC,uBAAe,CAAC,CAAC,KAAK;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,CAAC,WAAW,YAAY,WAAW;AACnC,UAAI,UAAU,CAAC,SAAS;AACpB,eAAO,uBAAuB,MAAM,MAAM;AAAA,MAC9C;AAAA,IACJ;AACA,WAAO,EAAE,OAAO;AAAA,MACZ,OAAO,CAAC,uBAAuB;AAAA,QACvB,cAAc;AAAA,MAClB,CAAC;AAAA,MACL,OAAO,WAAW,SACZ;AAAA,QACE,aAAa,GAAG,QAAQ,MAAM;AAAA,MAClC,IACE;AAAA,IACV,GAAG;AAAA,MACC,aAAa,OAAQ,eAAe,WAAY,YAAY,gBAAkB,YACxE;AAAA,QACE,EAAE,OAAO,OAAO,OAAO,EAAE,OAAO,qBAAqB,GAAG,GAAG,GAAG;AAAA,UAC1D,EAAE,KAAK;AAAA,YACH,OAAO,gBAAiB,cAAc,QAAQ,EAAE,oBAAsB,WAAY,YAAY,QAAQ,EAAE,kBAAoB,aAAa,QAAQ,EAAE;AAAA,UACvJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL,IACE;AAAA,MACN,EAAE,OAAO;AAAA,QACL,OAAO;AAAA,MACX,GAAG,UAAU;AAAA,IACjB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,QAAQ,YAAY;AAClC,UAAM,EAAE,KAAK,OAAO,IAAI;AACxB,UAAM,EAAE,aAAa,IAAI;AACzB,QAAI,gBAAgB,IAAI,aAAa;AACjC,aAAO,CAAC,KAAK,kBAAkB,QAAQ,UAAU,CAAC;AAAA,IACtD;AACA,WAAO,CAAC,KAAK,kBAAkB,QAAQ,UAAU,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,QAAQ;AACpB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,WAAO,wBAAwB,QAAQ,mBAAmB,QAAQ,aAAa,OAAO,SAAS,YAAY,MAAM,IAAI,WAAW,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC;AAAA,EAC1J;AAAA,EACA,cAAc,QAAQ;AAClB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,aAAa,OAAO;AAC1B,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,eAAe,IAAI,OAAO,eAAe;AACjD,UAAM,UAAU,eAAe;AAC/B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,MAAM,CAAC;AAAA,IACzE;AACA,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,YAAY,QAAQ;AAC1B,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,GAAG,WAAW,YAAY,UAAU,MAAM,IAAI,aAAa,OAAO,QAAQ,cAAc,KAAK,KAAK,CAAC,CAAC,EAAE;AAAA,IACpH,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,KAAK,kBAAkB,QAAQ,KAAK,cAAc,MAAM,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ;AACtB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,UAAM,YAAY,QAAQ,MAAM,QAAQ;AACxC,WAAO,wBAAwB,QAAQ,mBAAmB,QAAQ,aAC5D,OAAO,SAAS,YAAY,MAAM,IAClC;AAAA,MACE,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG,YAAY,OAAO,SAAS,WAAW,MAAM,IAAI,WAAW,OAAO,SAAS,GAAG,CAAC,CAAC;AAAA,IACxF,CAAC,CAAC;AAAA,EACV;AAAA,EACA,gBAAgB,QAAQ;AACpB,UAAM,EAAE,QAAQ,QAAQ,SAAS,IAAI;AACrC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,iBAAiB,IAAI,OAAO,eAAe;AACnD,UAAM,EAAE,eAAe,IAAI;AAC3B,UAAM,YAAY,iBAAiB;AACnC,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,EAAE,YAAY,aAAa,cAAc,IAAI;AACnD,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,YAAY,QAAQ,MAAM,QAAQ;AACxC,UAAM,YAAY,OAAO,MAAM,KAAK,cAAc;AAClD,UAAM,YAAY,CAAC,iBAAiB,cAAc,EAAE,QAAQ,IAAI,CAAC;AACjE,QAAI,aAAa,CAAC,CAAC;AACnB,QAAI;AACJ,QAAI,CAAC,UAAU;AACX,YAAM;AAAA,QACF,QAAQ,MAAM;AACV,cAAI,CAAC,cAAc,WAAW;AAC1B,mBAAO,qBAAqB,MAAM,MAAM;AAAA,UAC5C;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,aAAa;AACb,qBAAa,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC;AAAA,MAC7C;AAAA,IACJ;AACA,UAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,WAAW,UAAU,YAAY,SAAS,UAAU,CAAC;AAC7H,QAAI,WAAW;AACX,aAAO,kBAAkB,QAAQ,OAAO,SAAS,WAAW,WAAW,CAAC;AAAA,IAC5E;AACA,UAAM,WAAW,CAAC;AAClB,QAAI,WAAW;AACX,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,mBAAmB,YAAY,QAAQ,EAAE,sBAAsB,QAAQ,EAAE,qBAAqB;AAAA,MAC1G,CAAC,CAAC;AAAA,IACN;AACA,QAAI,eAAe,YAAY;AAC3B,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO;AAAA,MACX,GAAG,cAAc,OAAO,SAAS,aAAa,WAAW,IAAI,iBAAAD,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,IAC9F;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,mBAAmB;AAAA,QACzC,eAAe;AAAA,QACf,gBAAgB;AAAA,MACpB,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,KAAK,kBAAkB,QAAQ,KAAK,gBAAgB,MAAM,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,QAAQ;AACzB,UAAM,EAAE,QAAQ,QAAQ,SAAS,IAAI;AACrC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,8BAA8B,oBAAoB,IAAI,OAAO,eAAe;AACpF,UAAM,EAAE,eAAe,uBAAuB,iBAAiB,2BAA2B,IAAI;AAC9F,UAAM,wBAAwB,6BAA6B;AAC3D,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,UAAM,YAAY,QAAQ,MAAM,QAAQ;AACxC,UAAM,eAAe,oBAAoB;AACzC,UAAM,EAAE,eAAe,YAAY,YAAY,IAAI;AACnD,UAAM,WAAW,OAAO,SAAS;AACjC,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,UAAI,UAAU,CAAC,SAAS;AACpB,YAAI,CAAC,uBAAuB;AACxB,iBAAO,qBAAqB,MAAM,CAAC,qBAAqB;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,uBAAuB,UAAU,uBAAuB,eAAe,2BAA2B,CAAC;AAC9K,QAAI,YAAY;AACZ,aAAO,wBAAwB,QAAQ,mBAAmB,gBAAgB,OAAO,SAAS,YAAY,cAAc,CAAC,CAAC;AAAA,IAC1H;AACA,QAAI,gBAAgB,CAAC,aAAa,eAAe,OAAO;AACpD,aAAO,wBAAwB,QAAQ,mBAAmB,gBAAgB;AAAA,QACtE,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,YAAY,OAAO,SAAS,WAAW,cAAc,IAAI,QAAQ;AAAA,MACxE,CAAC,CAAC;AAAA,IACN;AACA,WAAO,wBAAwB,QAAQ,mBAAmB,gBAAgB;AAAA,MACtE,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,sBAAsB;AAAA,QAC5C,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,MACzB,CAAC,GAAG,OAAO,iBAAAA,QAAQ,OAAO,WAAW,IAAIF,SAAQ,oBAAoB,IAAI,GAAG,eAAe,EAAE,GAAG,GAAG,GAAG,GAAG;AAAA,QAC7G,EAAE,QAAQ;AAAA,UACN,OAAO,CAAC,sBAAsB,6BAA6B,QAAQ,EAAE,+BAAgC,wBAAwB,QAAQ,EAAE,yBAAyB,QAAQ,EAAE,wBAAyB;AAAA,QACvM,CAAC;AAAA,MACL,EAAE,OAAO,aAAa,WAChB;AAAA,QACE,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,YAAY,OAAO,SAAS,WAAW,cAAc,IAAI,QAAQ;AAAA,MACxE,IACE,CAAC,CAAC,CAAC;AAAA,IACb,CAAC,CAAC;AAAA,EACN;AAAA,EACA,mBAAmB,QAAQ;AACvB,UAAM,EAAE,QAAQ,KAAK,QAAQ,SAAS,IAAI;AAC1C,UAAM,aAAa,OAAO;AAC1B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,oBAAoB,iBAAiB,IAAI;AACjD,UAAM,EAAE,oBAAoB,yBAAyB,IAAI;AACzD,UAAM,EAAE,oBAAoB,IAAI,OAAO,eAAe;AACtD,UAAM,eAAe,oBAAoB;AACzC,UAAM,EAAE,YAAY,aAAa,cAAc,IAAI;AACnD,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,eAAe,QAAQ,MAAM,WAAW;AAC9C,QAAI,gBAAgB;AACpB,QAAI,YAAY;AAChB,UAAM,YAAY,CAAC,iBAAiB,cAAc,EAAE,QAAQ,IAAI,CAAC;AACjE,QAAI,aAAa,CAAC,CAAC;AACnB,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,kBAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,mBAAmB,KAAK;AAC9D,UAAI,UAAU,CAAC,SAAS;AACpB,YAAI,CAAC,cAAc,WAAW;AAC1B,iBAAO,qBAAqB,MAAM,QAAQ,CAAC,SAAS;AAAA,QACxD;AAAA,MACJ;AACA,UAAI,aAAa;AACb,qBAAa,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC;AAAA,MAC7C;AACA,UAAI,cAAc,kBAAkB;AAChC,wBAAgB,CAAC,CAAC,yBAAyB,KAAK;AAAA,MACpD;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,WAAW,UAAU,YAAY,SAAS,WAAW,cAAc,CAAC;AAC/I,QAAI,cAAc;AACd,aAAO,kBAAkB,QAAQ,OAAO,SAAS,cAAc,cAAc,CAAC;AAAA,IAClF;AACA,UAAM,WAAW,CAAC;AAClB,QAAI,WAAW;AACX,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,sBAAsB,gBAAgB,QAAQ,EAAE,+BAAgC,YAAY,QAAQ,EAAE,yBAAyB,QAAQ,EAAE,wBAAyB;AAAA,MAC9K,CAAC,CAAC;AAAA,IACN;AACA,QAAI,eAAe,YAAY;AAC3B,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO;AAAA,MACX,GAAG,cAAc,OAAO,SAAS,aAAa,cAAc,IAAI,iBAAAE,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,IACjG;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,sBAAsB;AAAA,QAC5C,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,cAAc,CAAC;AAAA,MACnB,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EACA,wBAAwB,QAAQ;AAC5B,WAAO,KAAK,kBAAkB,QAAQ,KAAK,mBAAmB,MAAM,CAAC;AAAA,EACzE;AAAA,EACA,yBAAyB,QAAQ;AAC7B,UAAM,EAAE,QAAQ,KAAK,QAAQ,SAAS,IAAI;AAC1C,UAAM,aAAa,OAAO;AAC1B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,oBAAoB,iBAAiB,IAAI;AACjD,UAAM,EAAE,yBAAyB,IAAI;AACrC,UAAM,EAAE,oBAAoB,IAAI,OAAO,eAAe;AACtD,UAAM,eAAe,oBAAoB;AACzC,UAAM,EAAE,YAAY,YAAY,aAAa,cAAc,IAAI;AAC/D,UAAM,qBAAqB,aAAa,sBAAsB,aAAa;AAC3E,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,eAAe,QAAQ,MAAM,WAAW;AAC9C,QAAI,kBAAkB;AACtB,QAAI,YAAY;AAChB,UAAM,YAAY,CAAC,iBAAiB,cAAc,EAAE,QAAQ,IAAI,CAAC;AACjE,QAAI,aAAa,CAAC,CAAC;AACnB,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,kBAAY,CAAC,CAAC,sBAAsB,iBAAAA,QAAQ,IAAI,KAAK,UAAU;AAC/D,UAAI,UAAU,CAAC,SAAS;AACpB,YAAI,CAAC,cAAc,WAAW;AAC1B,iBAAO,qBAAqB,MAAM,QAAQ,CAAC,SAAS;AAAA,QACxD;AAAA,MACJ;AACA,UAAI,aAAa;AACb,qBAAa,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC;AAAA,MAC7C;AACA,UAAI,cAAc,kBAAkB;AAChC,0BAAkB,CAAC,CAAC,yBAAyB,KAAK;AAAA,MACtD;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,WAAW,UAAU,YAAY,SAAS,WAAW,eAAe,gBAAgB,CAAC;AAChK,QAAI,cAAc;AACd,aAAO,kBAAkB,QAAQ,OAAO,SAAS,cAAc,cAAc,CAAC;AAAA,IAClF;AACA,UAAM,WAAW,CAAC;AAClB,QAAI,WAAW;AACX,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,sBAAsB,kBAAkB,QAAQ,EAAE,+BAAgC,YAAY,QAAQ,EAAE,yBAAyB,QAAQ,EAAE,wBAAyB;AAAA,MAChL,CAAC,CAAC;AACF,UAAI,eAAe,YAAY;AAC3B,iBAAS,KAAK,EAAE,QAAQ;AAAA,UACpB,OAAO;AAAA,QACX,GAAG,cAAc,OAAO,SAAS,aAAa,cAAc,IAAI,iBAAAA,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,MACjG;AAAA,IACJ;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,sBAAsB;AAAA,QAC5C,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,qBAAqB,sBAAsB,CAAC,YAAY,IAAI,kBAAkB,IAAI;AAAA,QAClF,cAAc,CAAC;AAAA,MACnB,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EACA,8BAA8B,QAAQ;AAClC,WAAO,KAAK,kBAAkB,QAAQ,KAAK,yBAAyB,MAAM,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,UAAU,KAAK,OAAO,IAAI;AAC1C,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,iBAAiB,IAAI;AAC7B,UAAM,EAAE,iBAAiB,wBAAwB,IAAI;AACrD,UAAM,EAAE,kBAAkB,IAAI,OAAO,eAAe;AACpD,UAAM,aAAa,kBAAkB;AACrC,UAAM,EAAE,MAAM,YAAY,YAAY,UAAU,UAAU,WAAW,cAAc,IAAI;AACvF,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,WAAW,QAAQ,MAAM,OAAO;AACtC,QAAI,WAAW;AACf,QAAI,gBAAgB;AACpB,QAAI,oBAAoB,IAAI,aAAa;AACrC,aAAO,kBAAkB,QAAQ,CAAC,CAAC;AAAA,IACvC;AACA,QAAI,UAAU;AACV,aAAO,kBAAkB,QAAQ,OAAO,SAAS,UAAU,MAAM,CAAC;AAAA,IACtE;AACA,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,iBAAW,CAAC,CAAC,gBAAgB,KAAK;AAClC,UAAI,MAAM;AACN,wBAAgB,CAAC,CAAC,wBAAwB,KAAK;AAAA,MACnD;AAAA,IACJ;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,aAAa,CAAC,iBAAiB,cAAc,MAAM,KAC7C,EAAE,QAAQ;AAAA,QACR,OAAO,CAAC,uBAAuB;AAAA,UACvB,cAAc;AAAA,QAClB,CAAC;AAAA,QACL,YAAY,MAAM;AACd,eAAK,gBAAgB;AAAA,QACzB;AAAA,QACA,QAAQ,MAAM;AACV,iBAAO,sBAAsB,MAAM,MAAM;AAAA,QAC7C;AAAA,MACJ,GAAG;AAAA,QACC,EAAE,KAAK;AAAA,UACH,OAAO,CAAC,yBAAyB,gBAAiB,cAAc,QAAQ,EAAE,sBAAwB,WAAY,YAAY,QAAQ,EAAE,oBAAsB,aAAa,QAAQ,EAAE,kBAAoB;AAAA,QACzM,CAAC;AAAA,MACL,CAAC,IACC,mBAAmB,MAAM;AAAA,MAC/B,eAAe,aACT,EAAE,QAAQ;AAAA,QACR,OAAO;AAAA,MACX,GAAG,cAAc,OAAO,SAAS,aAAa,MAAM,IAAI,iBAAAA,QAAQ,IAAI,KAAK,UAAU,CAAC,IAClF,mBAAmB,MAAM;AAAA,IACnC,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,OAAO,cAAc,IAAI;AACjC,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,OAAO,SAAS,aAAa,MAAM;AAAA,IAC9C;AACA,QAAI,eAAe;AACf,YAAM,WAAW,SAAS,IAAI,cAAc,IAAI;AAChD,UAAI,UAAU;AACV,cAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,YAAI,UAAU;AACV,iBAAO,WAAW,SAAS,eAAe,MAAM,CAAC;AAAA,QACrD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,QAAQ;AACnB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,MAAM,CAAC;AAAA,IACzE;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,WAAW,oBAAoB,MAAM;AAAA,MACzC,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,mBAAmB,QAAQ;AACvB,WAAO,KAAK,kBAAkB,QAAQ,KAAK,eAAe,MAAM,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B,QAAQ;AAC9B,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK,eAAe,MAAM,EAAE,OAAO,KAAK,iBAAiB,MAAM,CAAC,CAAC,CAAC;AAAA,EACnJ;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACrB,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK,eAAe,MAAM,CAAC,CAAC;AAAA,EAC7G;AAAA,EACA,eAAe,QAAQ;AACnB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,UAAM,WAAW,gBAAgB;AACjC,UAAM,EAAE,UAAU,UAAU,UAAU,WAAW,YAAY,SAAS,UAAU,kBAAkB,IAAI;AACtG,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,aAAa,CAAC,qBAAqB,kBAAkB,MAAM,IAAI;AAC/D,aAAO;AAAA,QACH,EAAE,QAAQ;AAAA,UACN,OAAO,CAAC,kBAAkB,kBAAkB,UAAU,SAAS;AAAA,QACnE,GAAG;AAAA,UACC,EAAE,KAAK;AAAA,YACH,OAAO,CAAC,qBAAqB,WAAW,QAAQ,EAAE,gBAAgB;AAAA,cAC1D,gBAAgB,UAAU;AAAA,YAC9B,CAAC;AAAA,YACL,OAAO,iBAAAA,QAAQ,OAAO,QAAQ,IAAIF,SAAQ,mBAAmB,IAAI,GAAG,YAAY,EAAE;AAAA,YAClF,SAAS,WACH,CAAC,SAAS;AACR,mBAAK,gBAAgB;AACrB,qBAAO,iBAAiB,MAAM,QAAQ,KAAK;AAAA,YAC/C,IACE;AAAA,UACV,CAAC;AAAA,UACD,EAAE,KAAK;AAAA,YACH,OAAO,CAAC,sBAAsB,YAAY,QAAQ,EAAE,iBAAiB;AAAA,cAC7D,gBAAgB,UAAU;AAAA,YAC9B,CAAC;AAAA,YACL,OAAO,iBAAAE,QAAQ,OAAO,SAAS,IAAIF,SAAQ,oBAAoB,IAAI,GAAG,aAAa,EAAE;AAAA,YACrF,SAAS,WACH,CAAC,SAAS;AACR,mBAAK,gBAAgB;AACrB,qBAAO,iBAAiB,MAAM,QAAQ,MAAM;AAAA,YAChD,IACE;AAAA,UACV,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,QAAQ;AACvB,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK,iBAAiB,MAAM,CAAC,CAAC;AAAA,EAC/G;AAAA,EACA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,QAAQ,UAAU,IAAI;AACtC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,YAAY,IAAI;AACxB,UAAM,EAAE,kBAAkB,IAAI,OAAO,eAAe;AACpD,UAAM,aAAa,kBAAkB;AACrC,UAAM,EAAE,UAAU,UAAU,WAAW,kBAAkB,IAAI;AAC7D,QAAI,aAAa,CAAC,qBAAqB,kBAAkB,MAAM,IAAI;AAC/D,aAAO;AAAA,QACH,EAAE,QAAQ;AAAA,UACN,OAAO,CAAC,oBAAoB;AAAA,YACpB,cAAc,YAAY,WAAW,YAAY,WAAW;AAAA,UAChE,CAAC;AAAA,UACL,QAAQ,MAAM;AACV,gBAAI,OAAO,oBAAoB;AAC3B,qBAAO,mBAAmB,MAAM,OAAO,QAAQ,MAAM;AAAA,YACzD;AAAA,UACJ;AAAA,QACJ,GAAG;AAAA,UACC,EAAE,KAAK;AAAA,YACH,OAAO,CAAC,mBAAmB,YAAa,aAAa,QAAQ,EAAE,qBAAuB,YAAY,QAAQ,EAAE,iBAAkB;AAAA,YAC9H,OAAOA,SAAQ,kBAAkB;AAAA,UACrC,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,aAAa,OAAO;AAC1B,UAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,UAAM,EAAE,YAAY,UAAU,IAAI;AAClC,UAAM,WAAW,gBAAgB;AACjC,UAAM,EAAE,UAAU,SAAS,WAAW,IAAI;AAC1C,QAAI,aAAa;AACjB,QAAI,WAAW;AACX,YAAM,cAAc,iBAAAE,QAAQ,IAAI,WAAW,OAAO,KAAK;AACvD,UAAI,aAAa;AACb,qBAAa,YAAY,KAAK,CAAC,SAAS,KAAK,QAAQ;AAAA,MACzD;AAAA,IACJ;AACA,QAAI,cAAc,CAAC;AACnB,QAAI,aAAa,UAAU,GAAG;AAC1B,oBAAc;AAAA,QACV,cAAc,SAAS,eACjB,EAAE,KAAK;AAAA,UACL,OAAO;AAAA,QACX,CAAC,IACC,mBAAmB,MAAM;AAAA,QAC/B,aAAa,UAAU,KAAK,SAAS,WAC/B,EAAE,KAAK;AAAA,UACL,OAAO,CAAC,uBAAuB,SAAS,QAAQ,QAAQ,EAAE,UAAU;AAAA,QACxE,CAAC,IACC,mBAAmB,MAAM;AAAA,MACnC;AAAA,IACJ;AACA,WAAO,wBAAwB,QAAQ,YAAY,OAAO,KAAK,kBAAkB,MAAM,CAAC,EACnF,OAAO,WAAW,KAAK,eAAe,MAAM,IAAI,CAAC,CAAC,EAClD,OAAO,UAAU,KAAK,iBAAiB,MAAM,IAAI,CAAC,CAAC,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,cAAc,QAAQ;AAClB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,EAAE,QAAQ,IAAI;AACpB,UAAM,EAAE,WAAW,IAAI;AACvB,WAAO,KAAK,YAAY,QAAQ,aAAa,UAAU,KAAK,WAAW,QAAQ,QAAQ,OAAO,GAAG;AAAA,EACrG;AAAA,EACA,kBAAkB,QAAQ;AACtB,WAAO,KAAK,kBAAkB,QAAQ,KAAK,cAAc,MAAM,CAAC;AAAA,EACpE;AAAA;AAAA,EAEA,eAAe,QAAQ;AACnB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,EAAE,QAAQ,IAAI;AACpB,UAAM,EAAE,WAAW,IAAI;AACvB,WAAO,KAAK,YAAY,QAAQ,aAAa,UAAU,KAAK,WAAW,QAAQ,QAAQ,OAAO,OAAO,QAAQ,WAAW,OAAO,MAAM;AAAA,EACzI;AAAA,EACA,mBAAmB,QAAQ;AACvB,WAAO,KAAK,kBAAkB,QAAQ,KAAK,eAAe,MAAM,CAAC;AAAA,EACrE;AAAA,EACA,YAAY,QAAQ,QAAQ;AACxB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,OAAO,YAAY,UAAU,IAAI;AACzC,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,WAAW,QAAQ,MAAM,OAAO;AACtC,UAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,UAAM,SAAS,WAAY,SAAS,mBAAmB,SAAS,aAAc;AAC9E,UAAM,aAAa,OAAO,OAAO,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM;AAC9D,QAAI,QAAQ;AACR,iBAAW,QAAQ;AACnB,UAAI,UAAU;AACV,eAAO,OAAO,SAAS,UAAU,UAAU;AAAA,MAC/C;AACA,UAAI,QAAQ;AACR,eAAO,WAAW,OAAO,YAAY,UAAU,CAAC;AAAA,MACpD;AACA,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,UAAU,CAAC;AAAA,IAC7E;AACA,QAAI,WAAW;AACX,aAAO,kBAAkB,QAAQ;AAAA,QAC7B,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,oBAAoB,UAAU,CAAC;AAAA,MACtC,CAAC;AAAA,IACL;AACA,WAAO,KAAK,kBAAkB,UAAU;AAAA,EAC5C;AACJ;AACA,IAAO,eAAQ;", "names": ["XEUtils", "import_xe_utils", "import_xe_utils", "XEUtils", "column", "XEUtils", "XEUtils", "column", "import_xe_utils", "getI18n", "formats", "XEUtils", "params"]}