{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/omit.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nfunction omit(obj, fields) {\n  // eslint-disable-next-line prefer-object-spread\n  const shallowCopy = _extends({}, obj);\n  for (let i = 0; i < fields.length; i += 1) {\n    const key = fields[i];\n    delete shallowCopy[key];\n  }\n  return shallowCopy;\n}\nexport default omit;"], "mappings": ";;;;;AACA,SAAS,KAAK,KAAK,QAAQ;AAEzB,QAAM,cAAc,SAAS,CAAC,GAAG,GAAG;AACpC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAM,MAAM,OAAO,CAAC;AACpB,WAAO,YAAY,GAAG;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAO,eAAQ;", "names": []}