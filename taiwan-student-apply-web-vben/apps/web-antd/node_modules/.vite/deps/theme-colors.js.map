{"version": 3, "sources": ["../../../../../node_modules/.pnpm/theme-colors@0.1.0/node_modules/theme-colors/dist/index.mjs"], "sourcesContent": ["function parseColor(color = \"\") {\n  if (typeof color !== \"string\") {\n    throw new TypeError(\"Color should be string!\");\n  }\n  const hexMatch = /^#?([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/i.exec(color);\n  if (hexMatch) {\n    return hexMatch.splice(1).map((c) => Number.parseInt(c, 16));\n  }\n  const hexMatchShort = /^#?([\\da-f])([\\da-f])([\\da-f])$/i.exec(color);\n  if (hexMatchShort) {\n    return hexMatchShort.splice(1).map((c) => Number.parseInt(c + c, 16));\n  }\n  if (color.includes(\",\")) {\n    return color.split(\",\").map((p) => Number.parseInt(p));\n  }\n  throw new Error(\"Invalid color format! Use #ABC or #AABBCC or r,g,b\");\n}\nfunction hexValue(components) {\n  return \"#\" + components.map((c) => `0${c.toString(16).toUpperCase()}`.slice(-2)).join(\"\");\n}\nfunction tint(components, intensity) {\n  return components.map((c) => Math.round(c + (255 - c) * intensity));\n}\nfunction shade(components, intensity) {\n  return components.map((c) => Math.round(c * intensity));\n}\nconst withTint = (intensity) => (hex) => tint(hex, intensity);\nconst withShade = (intensity) => (hex) => shade(hex, intensity);\n\nconst _variants = {\n  50: withTint(0.95),\n  100: withTint(0.9),\n  200: withTint(0.75),\n  300: withTint(0.6),\n  400: withTint(0.3),\n  500: (c) => c,\n  600: withShade(0.9),\n  700: withShade(0.6),\n  800: withShade(0.45),\n  900: withShade(0.3),\n  950: withShade(0.2)\n};\nfunction getColors(color, variants = _variants) {\n  const colors = {};\n  const components = parseColor(color);\n  for (const [name, fn] of Object.entries(variants)) {\n    colors[name] = hexValue(fn(components));\n  }\n  return colors;\n}\n\nexport { _variants, getColors };\n"], "mappings": ";;;AAAA,SAAS,WAAW,QAAQ,IAAI;AAC9B,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,UAAU,yBAAyB;AAAA,EAC/C;AACA,QAAM,WAAW,4CAA4C,KAAK,KAAK;AACvE,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,OAAO,SAAS,GAAG,EAAE,CAAC;AAAA,EAC7D;AACA,QAAM,gBAAgB,mCAAmC,KAAK,KAAK;AACnE,MAAI,eAAe;AACjB,WAAO,cAAc,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,OAAO,SAAS,IAAI,GAAG,EAAE,CAAC;AAAA,EACtE;AACA,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB,WAAO,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,OAAO,SAAS,CAAC,CAAC;AAAA,EACvD;AACA,QAAM,IAAI,MAAM,oDAAoD;AACtE;AACA,SAAS,SAAS,YAAY;AAC5B,SAAO,MAAM,WAAW,IAAI,CAAC,MAAM,IAAI,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE;AAC1F;AACA,SAAS,KAAK,YAAY,WAAW;AACnC,SAAO,WAAW,IAAI,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC;AACpE;AACA,SAAS,MAAM,YAAY,WAAW;AACpC,SAAO,WAAW,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC;AACxD;AACA,IAAM,WAAW,CAAC,cAAc,CAAC,QAAQ,KAAK,KAAK,SAAS;AAC5D,IAAM,YAAY,CAAC,cAAc,CAAC,QAAQ,MAAM,KAAK,SAAS;AAE9D,IAAM,YAAY;AAAA,EAChB,IAAI,SAAS,IAAI;AAAA,EACjB,KAAK,SAAS,GAAG;AAAA,EACjB,KAAK,SAAS,IAAI;AAAA,EAClB,KAAK,SAAS,GAAG;AAAA,EACjB,KAAK,SAAS,GAAG;AAAA,EACjB,KAAK,CAAC,MAAM;AAAA,EACZ,KAAK,UAAU,GAAG;AAAA,EAClB,KAAK,UAAU,GAAG;AAAA,EAClB,KAAK,UAAU,IAAI;AAAA,EACnB,KAAK,UAAU,GAAG;AAAA,EAClB,KAAK,UAAU,GAAG;AACpB;AACA,SAAS,UAAU,OAAO,WAAW,WAAW;AAC9C,QAAM,SAAS,CAAC;AAChB,QAAM,aAAa,WAAW,KAAK;AACnC,aAAW,CAAC,MAAM,EAAE,KAAK,OAAO,QAAQ,QAAQ,GAAG;AACjD,WAAO,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC;AAAA,EACxC;AACA,SAAO;AACT;", "names": []}