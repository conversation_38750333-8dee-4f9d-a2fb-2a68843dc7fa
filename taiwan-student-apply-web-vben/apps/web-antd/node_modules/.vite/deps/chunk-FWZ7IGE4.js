import {
  require_xe_utils
} from "./chunk-TV7URO3H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/number-input/src/util.js
var import_xe_utils = __toESM(require_xe_utils());
function handleNumber(val) {
  return import_xe_utils.default.isString(val) ? val.replace(/[^0-9e.-]/g, "") : val;
}
function toFloatValueFixed(inputValue, digitsValue) {
  if (/^-/.test("" + inputValue)) {
    return import_xe_utils.default.toFixed(import_xe_utils.default.ceil(inputValue, digitsValue), digitsValue);
  }
  return import_xe_utils.default.toFixed(import_xe_utils.default.floor(inputValue, digitsValue), digitsValue);
}

export {
  handleNumber,
  toFloatValueFixed
};
//# sourceMappingURL=chunk-FWZ7IGE4.js.map
