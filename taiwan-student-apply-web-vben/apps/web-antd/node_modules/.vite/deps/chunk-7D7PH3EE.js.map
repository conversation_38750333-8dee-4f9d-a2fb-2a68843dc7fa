{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/vnode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { filterEmpty } from './props-util';\nimport { cloneVNode, isVNode, Comment, Fragment, render as VueRender } from 'vue';\nimport warning from './warning';\nexport function cloneElement(vnode) {\n  let nodeProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let override = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  let mergeRef = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  let ele = vnode;\n  if (Array.isArray(vnode)) {\n    ele = filterEmpty(vnode)[0];\n  }\n  if (!ele) {\n    return null;\n  }\n  const node = cloneVNode(ele, nodeProps, mergeRef);\n  // cloneVNode内部是合并属性，这里改成覆盖属性\n  node.props = override ? _extends(_extends({}, node.props), nodeProps) : node.props;\n  warning(typeof node.props.class !== 'object', 'class must be string');\n  return node;\n}\nexport function cloneVNodes(vnodes) {\n  let nodeProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let override = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  return vnodes.map(vnode => cloneElement(vnode, nodeProps, override));\n}\nexport function deepCloneElement(vnode) {\n  let nodeProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let override = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  let mergeRef = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (Array.isArray(vnode)) {\n    return vnode.map(item => deepCloneElement(item, nodeProps, override, mergeRef));\n  } else {\n    // 需要判断是否为vnode方可进行clone操作\n    if (!isVNode(vnode)) {\n      return vnode;\n    }\n    const cloned = cloneElement(vnode, nodeProps, override, mergeRef);\n    if (Array.isArray(cloned.children)) {\n      cloned.children = deepCloneElement(cloned.children);\n    }\n    return cloned;\n  }\n}\nexport function triggerVNodeUpdate(vm, attrs, dom) {\n  VueRender(cloneVNode(vm, _extends({}, attrs)), dom);\n}\nconst ensureValidVNode = slot => {\n  return (slot || []).some(child => {\n    if (!isVNode(child)) return true;\n    if (child.type === Comment) return false;\n    if (child.type === Fragment && !ensureValidVNode(child.children)) return false;\n    return true;\n  }) ? slot : null;\n};\nexport function customRenderSlot(slots, name, props, fallback) {\n  var _a;\n  const slot = (_a = slots[name]) === null || _a === void 0 ? void 0 : _a.call(slots, props);\n  if (ensureValidVNode(slot)) {\n    return slot;\n  }\n  return fallback === null || fallback === void 0 ? void 0 : fallback();\n}"], "mappings": ";;;;;;;;;;;;;;;;AAIO,SAAS,aAAa,OAAO;AAClC,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,MAAM;AACV,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,UAAM,YAAY,KAAK,EAAE,CAAC;AAAA,EAC5B;AACA,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,QAAM,OAAO,WAAW,KAAK,WAAW,QAAQ;AAEhD,OAAK,QAAQ,WAAW,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,SAAS,IAAI,KAAK;AAC7E,kBAAQ,OAAO,KAAK,MAAM,UAAU,UAAU,sBAAsB;AACpE,SAAO;AACT;AACO,SAAS,YAAY,QAAQ;AAClC,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,SAAO,OAAO,IAAI,WAAS,aAAa,OAAO,WAAW,QAAQ,CAAC;AACrE;AACO,SAAS,iBAAiB,OAAO;AACtC,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,UAAQ,iBAAiB,MAAM,WAAW,UAAU,QAAQ,CAAC;AAAA,EAChF,OAAO;AAEL,QAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,aAAa,OAAO,WAAW,UAAU,QAAQ;AAChE,QAAI,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAClC,aAAO,WAAW,iBAAiB,OAAO,QAAQ;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AACF;AACO,SAAS,mBAAmB,IAAI,OAAO,KAAK;AACjD,SAAU,WAAW,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG;AACpD;AACA,IAAM,mBAAmB,UAAQ;AAC/B,UAAQ,QAAQ,CAAC,GAAG,KAAK,WAAS;AAChC,QAAI,CAAC,QAAQ,KAAK,EAAG,QAAO;AAC5B,QAAI,MAAM,SAAS,QAAS,QAAO;AACnC,QAAI,MAAM,SAAS,YAAY,CAAC,iBAAiB,MAAM,QAAQ,EAAG,QAAO;AACzE,WAAO;AAAA,EACT,CAAC,IAAI,OAAO;AACd;AACO,SAAS,iBAAiB,OAAO,MAAM,OAAO,UAAU;AAC7D,MAAI;AACJ,QAAM,QAAQ,KAAK,MAAM,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,KAAK;AACzF,MAAI,iBAAiB,IAAI,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AACtE;", "names": []}