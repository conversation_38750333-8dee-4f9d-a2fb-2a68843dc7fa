{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-util/Dom/class.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/collapseMotion.js"], "sourcesContent": ["export function hasClass(node, className) {\n  if (node.classList) {\n    return node.classList.contains(className);\n  }\n  const originClass = node.className;\n  return ` ${originClass} `.indexOf(` ${className} `) > -1;\n}\nexport function addClass(node, className) {\n  if (node.classList) {\n    node.classList.add(className);\n  } else {\n    if (!hasClass(node, className)) {\n      node.className = `${node.className} ${className}`;\n    }\n  }\n}\nexport function removeClass(node, className) {\n  if (node.classList) {\n    node.classList.remove(className);\n  } else {\n    if (hasClass(node, className)) {\n      const originClass = node.className;\n      node.className = ` ${originClass} `.replace(` ${className} `, ' ');\n    }\n  }\n}", "import { nextTick } from 'vue';\nimport { addClass, removeClass } from '../vc-util/Dom/class';\nconst collapseMotion = function () {\n  let name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'ant-motion-collapse';\n  let appear = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  return {\n    name,\n    appear,\n    css: true,\n    onBeforeEnter: node => {\n      node.style.height = '0px';\n      node.style.opacity = '0';\n      addClass(node, name);\n    },\n    onEnter: node => {\n      nextTick(() => {\n        node.style.height = `${node.scrollHeight}px`;\n        node.style.opacity = '1';\n      });\n    },\n    onAfterEnter: node => {\n      if (node) {\n        removeClass(node, name);\n        node.style.height = null;\n        node.style.opacity = null;\n      }\n    },\n    onBeforeLeave: node => {\n      addClass(node, name);\n      node.style.height = `${node.offsetHeight}px`;\n      node.style.opacity = null;\n    },\n    onLeave: node => {\n      setTimeout(() => {\n        node.style.height = '0px';\n        node.style.opacity = '0';\n      });\n    },\n    onAfterLeave: node => {\n      if (node) {\n        removeClass(node, name);\n        if (node.style) {\n          node.style.height = null;\n          node.style.opacity = null;\n        }\n      }\n    }\n  };\n};\nexport default collapseMotion;"], "mappings": ";;;;;AAAO,SAAS,SAAS,MAAM,WAAW;AACxC,MAAI,KAAK,WAAW;AAClB,WAAO,KAAK,UAAU,SAAS,SAAS;AAAA,EAC1C;AACA,QAAM,cAAc,KAAK;AACzB,SAAO,IAAI,WAAW,IAAI,QAAQ,IAAI,SAAS,GAAG,IAAI;AACxD;AACO,SAAS,SAAS,MAAM,WAAW;AACxC,MAAI,KAAK,WAAW;AAClB,SAAK,UAAU,IAAI,SAAS;AAAA,EAC9B,OAAO;AACL,QAAI,CAAC,SAAS,MAAM,SAAS,GAAG;AAC9B,WAAK,YAAY,GAAG,KAAK,SAAS,IAAI,SAAS;AAAA,IACjD;AAAA,EACF;AACF;AACO,SAAS,YAAY,MAAM,WAAW;AAC3C,MAAI,KAAK,WAAW;AAClB,SAAK,UAAU,OAAO,SAAS;AAAA,EACjC,OAAO;AACL,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,YAAM,cAAc,KAAK;AACzB,WAAK,YAAY,IAAI,WAAW,IAAI,QAAQ,IAAI,SAAS,KAAK,GAAG;AAAA,IACnE;AAAA,EACF;AACF;;;ACvBA,IAAM,iBAAiB,WAAY;AACjC,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC/E,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,eAAe,UAAQ;AACrB,WAAK,MAAM,SAAS;AACpB,WAAK,MAAM,UAAU;AACrB,eAAS,MAAM,IAAI;AAAA,IACrB;AAAA,IACA,SAAS,UAAQ;AACf,eAAS,MAAM;AACb,aAAK,MAAM,SAAS,GAAG,KAAK,YAAY;AACxC,aAAK,MAAM,UAAU;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,IACA,cAAc,UAAQ;AACpB,UAAI,MAAM;AACR,oBAAY,MAAM,IAAI;AACtB,aAAK,MAAM,SAAS;AACpB,aAAK,MAAM,UAAU;AAAA,MACvB;AAAA,IACF;AAAA,IACA,eAAe,UAAQ;AACrB,eAAS,MAAM,IAAI;AACnB,WAAK,MAAM,SAAS,GAAG,KAAK,YAAY;AACxC,WAAK,MAAM,UAAU;AAAA,IACvB;AAAA,IACA,SAAS,UAAQ;AACf,iBAAW,MAAM;AACf,aAAK,MAAM,SAAS;AACpB,aAAK,MAAM,UAAU;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,IACA,cAAc,UAAQ;AACpB,UAAI,MAAM;AACR,oBAAY,MAAM,IAAI;AACtB,YAAI,KAAK,OAAO;AACd,eAAK,MAAM,SAAS;AACpB,eAAK,MAAM,UAAU;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,yBAAQ;", "names": []}