import {
  commonProps,
  datePickerProps,
  dayjs_default,
  generatePicker_default,
  rangePickerProps
} from "./chunk-PM4QA3RG.js";
import {
  omit_default
} from "./chunk-ITH5DYXO.js";
import {
  useInjectFormItemContext
} from "./chunk-XEB4S444.js";
import {
  devWarning_default
} from "./chunk-MWM4MJC4.js";
import {
  _objectSpread2,
  booleanType,
  stringType
} from "./chunk-BYUVPZUH.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  createVNode,
  defineComponent,
  ref
} from "./chunk-GI5RXSOE.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/time-picker/time-picker.js
var timePickerProps = () => ({
  format: String,
  showNow: booleanType(),
  showHour: booleanType(),
  showMinute: booleanType(),
  showSecond: booleanType(),
  use12Hours: booleanType(),
  hourStep: Number,
  minuteStep: Number,
  secondStep: Number,
  hideDisabledOptions: booleanType(),
  popupClassName: String,
  status: stringType()
});
function createTimePicker(generateConfig) {
  const DatePicker = generatePicker_default(generateConfig, _extends(_extends({}, timePickerProps()), {
    order: {
      type: Boolean,
      default: true
    }
  }));
  const {
    TimePicker: InternalTimePicker,
    RangePicker: InternalRangePicker
  } = DatePicker;
  const TimePicker2 = defineComponent({
    name: "ATimePicker",
    inheritAttrs: false,
    props: _extends(_extends(_extends(_extends({}, commonProps()), datePickerProps()), timePickerProps()), {
      addon: {
        type: Function
      }
    }),
    slots: Object,
    setup(p, _ref) {
      let {
        slots,
        expose,
        emit,
        attrs
      } = _ref;
      const props = p;
      const formItemContext = useInjectFormItemContext();
      devWarning_default(!(slots.addon || props.addon), "TimePicker", "`addon` is deprecated. Please use `v-slot:renderExtraFooter` instead.");
      const pickerRef = ref();
      expose({
        focus: () => {
          var _a;
          (_a = pickerRef.value) === null || _a === void 0 ? void 0 : _a.focus();
        },
        blur: () => {
          var _a;
          (_a = pickerRef.value) === null || _a === void 0 ? void 0 : _a.blur();
        }
      });
      const onChange = (value, dateString) => {
        emit("update:value", value);
        emit("change", value, dateString);
        formItemContext.onFieldChange();
      };
      const onOpenChange = (open) => {
        emit("update:open", open);
        emit("openChange", open);
      };
      const onFocus = (e) => {
        emit("focus", e);
      };
      const onBlur = (e) => {
        emit("blur", e);
        formItemContext.onFieldBlur();
      };
      const onOk = (value) => {
        emit("ok", value);
      };
      return () => {
        const {
          id = formItemContext.id.value
        } = props;
        return createVNode(InternalTimePicker, _objectSpread2(_objectSpread2(_objectSpread2({}, attrs), omit_default(props, ["onUpdate:value", "onUpdate:open"])), {}, {
          "id": id,
          "dropdownClassName": props.popupClassName,
          "mode": void 0,
          "ref": pickerRef,
          "renderExtraFooter": props.addon || slots.addon || props.renderExtraFooter || slots.renderExtraFooter,
          "onChange": onChange,
          "onOpenChange": onOpenChange,
          "onFocus": onFocus,
          "onBlur": onBlur,
          "onOk": onOk
        }), slots);
      };
    }
  });
  const TimeRangePicker2 = defineComponent({
    name: "ATimeRangePicker",
    inheritAttrs: false,
    props: _extends(_extends(_extends(_extends({}, commonProps()), rangePickerProps()), timePickerProps()), {
      order: {
        type: Boolean,
        default: true
      }
    }),
    slots: Object,
    setup(p, _ref2) {
      let {
        slots,
        expose,
        emit,
        attrs
      } = _ref2;
      const props = p;
      const pickerRef = ref();
      const formItemContext = useInjectFormItemContext();
      expose({
        focus: () => {
          var _a;
          (_a = pickerRef.value) === null || _a === void 0 ? void 0 : _a.focus();
        },
        blur: () => {
          var _a;
          (_a = pickerRef.value) === null || _a === void 0 ? void 0 : _a.blur();
        }
      });
      const onChange = (values, dateStrings) => {
        emit("update:value", values);
        emit("change", values, dateStrings);
        formItemContext.onFieldChange();
      };
      const onOpenChange = (open) => {
        emit("update:open", open);
        emit("openChange", open);
      };
      const onFocus = (e) => {
        emit("focus", e);
      };
      const onBlur = (e) => {
        emit("blur", e);
        formItemContext.onFieldBlur();
      };
      const onPanelChange = (values, modes) => {
        emit("panelChange", values, modes);
      };
      const onOk = (values) => {
        emit("ok", values);
      };
      const onCalendarChange = (values, dateStrings, info) => {
        emit("calendarChange", values, dateStrings, info);
      };
      return () => {
        const {
          id = formItemContext.id.value
        } = props;
        return createVNode(InternalRangePicker, _objectSpread2(_objectSpread2(_objectSpread2({}, attrs), omit_default(props, ["onUpdate:open", "onUpdate:value"])), {}, {
          "id": id,
          "dropdownClassName": props.popupClassName,
          "picker": "time",
          "mode": void 0,
          "ref": pickerRef,
          "onChange": onChange,
          "onOpenChange": onOpenChange,
          "onFocus": onFocus,
          "onBlur": onBlur,
          "onPanelChange": onPanelChange,
          "onOk": onOk,
          "onCalendarChange": onCalendarChange
        }), slots);
      };
    }
  });
  return {
    TimePicker: TimePicker2,
    TimeRangePicker: TimeRangePicker2
  };
}
var time_picker_default = createTimePicker;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/time-picker/dayjs.js
var {
  TimePicker,
  TimeRangePicker
} = time_picker_default(dayjs_default);
var dayjs_default2 = _extends(TimePicker, {
  TimePicker,
  TimeRangePicker,
  install: (app) => {
    app.component(TimePicker.name, TimePicker);
    app.component(TimeRangePicker.name, TimeRangePicker);
    return app;
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/time-picker/index.js
var time_picker_default2 = dayjs_default2;

export {
  TimePicker,
  TimeRangePicker,
  time_picker_default2 as time_picker_default
};
//# sourceMappingURL=chunk-2OEHXQLK.js.map
