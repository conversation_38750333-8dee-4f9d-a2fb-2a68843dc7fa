{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/group.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/colgroup/index.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-colgroup/index.js"], "sourcesContent": ["import { defineComponent, h, onUnmounted, provide, inject, ref, onMounted, createCommentVNode } from 'vue';\nimport { columnProps } from './column';\nimport { watchColumn, assembleColumn, destroyColumn } from './util';\nimport Cell from './cell';\nexport default defineComponent({\n    name: 'VxeColgroup',\n    props: columnProps,\n    setup(props, { slots }) {\n        const refElem = ref();\n        const $xeTable = inject('$xeTable', null);\n        const $xeParentColgroup = inject('$xeColgroup', null);\n        if (!$xeTable) {\n            return () => createCommentVNode();\n        }\n        const columnConfig = Cell.createColumn($xeTable, props);\n        const columnSlots = {};\n        if (slots.header) {\n            columnSlots.header = slots.header;\n        }\n        columnConfig.slots = columnSlots;\n        columnConfig.children = [];\n        watchColumn($xeTable, props, columnConfig);\n        onMounted(() => {\n            const elem = refElem.value;\n            if (elem) {\n                assembleColumn($xeTable, elem, columnConfig, $xeParentColgroup);\n            }\n        });\n        onUnmounted(() => {\n            destroyColumn($xeTable, columnConfig);\n        });\n        const renderVN = () => {\n            return h('div', {\n                ref: refElem\n            }, slots.default ? slots.default() : []);\n        };\n        const $xeColgroup = { columnConfig };\n        provide('$xeColgroup', $xeColgroup);\n        provide('$xeGrid', null);\n        return renderVN;\n    }\n});\n", "import { VxeUI } from '../ui';\nimport VxeColgroupComponent from '../table/src/group';\nexport const VxeColgroup = Object.assign({}, VxeColgroupComponent, {\n    install(app) {\n        app.component(VxeColgroupComponent.name, VxeColgroupComponent);\n        // 兼容旧用法\n        app.component('VxeTableColgroup', VxeColgroupComponent);\n    }\n});\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeColgroupComponent.name, VxeColgroupComponent);\n    // 兼容旧用法\n    VxeUI.dynamicApp.component('VxeTableColgroup', VxeColgroupComponent);\n}\nVxeUI.component(VxeColgroupComponent);\nexport const Colgroup = VxeColgroup;\nexport default VxeColgroup;\n", "import VxeColgroup from '../colgroup';\nexport * from '../colgroup';\nexport default VxeColgroup;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAO,gBAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM,OAAO,EAAE,MAAM,GAAG;AACpB,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,OAAO,YAAY,IAAI;AACxC,UAAM,oBAAoB,OAAO,eAAe,IAAI;AACpD,QAAI,CAAC,UAAU;AACX,aAAO,MAAM,mBAAmB;AAAA,IACpC;AACA,UAAM,eAAe,aAAK,aAAa,UAAU,KAAK;AACtD,UAAM,cAAc,CAAC;AACrB,QAAI,MAAM,QAAQ;AACd,kBAAY,SAAS,MAAM;AAAA,IAC/B;AACA,iBAAa,QAAQ;AACrB,iBAAa,WAAW,CAAC;AACzB,gBAAY,UAAU,OAAO,YAAY;AACzC,cAAU,MAAM;AACZ,YAAM,OAAO,QAAQ;AACrB,UAAI,MAAM;AACN,uBAAe,UAAU,MAAM,cAAc,iBAAiB;AAAA,MAClE;AAAA,IACJ,CAAC;AACD,gBAAY,MAAM;AACd,oBAAc,UAAU,YAAY;AAAA,IACxC,CAAC;AACD,UAAM,WAAW,MAAM;AACnB,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,MACT,GAAG,MAAM,UAAU,MAAM,QAAQ,IAAI,CAAC,CAAC;AAAA,IAC3C;AACA,UAAM,cAAc,EAAE,aAAa;AACnC,YAAQ,eAAe,WAAW;AAClC,YAAQ,WAAW,IAAI;AACvB,WAAO;AAAA,EACX;AACJ,CAAC;;;ACvCM,IAAM,cAAc,OAAO,OAAO,CAAC,GAAG,eAAsB;AAAA,EAC/D,QAAQ,KAAK;AACT,QAAI,UAAU,cAAqB,MAAM,aAAoB;AAE7D,QAAI,UAAU,oBAAoB,aAAoB;AAAA,EAC1D;AACJ,CAAC;AACD,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,cAAqB,MAAM,aAAoB;AAE1E,QAAM,WAAW,UAAU,oBAAoB,aAAoB;AACvE;AACA,MAAM,UAAU,aAAoB;AAC7B,IAAM,WAAW;AACxB,IAAO,mBAAQ;;;ACdf,IAAO,uBAAQ;", "names": []}