import {
  column_default
} from "./chunk-BOJ725D2.js";
import "./chunk-GDZBY3D5.js";
import "./chunk-XP32B7RH.js";
import "./chunk-LLNCDZ36.js";
import {
  VxeUI
} from "./chunk-I6LGDOPB.js";
import "./chunk-K2IVEY5U.js";
import "./chunk-GI5RXSOE.js";
import "./chunk-GEKVEWYT.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/column/index.js
var VxeColumn = Object.assign({}, column_default, {
  install(app) {
    app.component(column_default.name, column_default);
    app.component("VxeTableColumn", column_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(column_default.name, column_default);
  VxeUI.dynamicApp.component("VxeTableColumn", column_default);
}
VxeUI.component(column_default);
var Column = VxeColumn;
var column_default2 = VxeColumn;

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-column/index.js
var vxe_column_default = column_default2;
export {
  Column,
  VxeColumn,
  vxe_column_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-column_index__js.js.map
