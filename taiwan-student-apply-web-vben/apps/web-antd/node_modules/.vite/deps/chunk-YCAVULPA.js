import {
  onBeforeUpdate,
  ref
} from "./chunk-ZLVVKZUX.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useRefs.js
var useRefs = () => {
  const refs = ref(/* @__PURE__ */ new Map());
  const setRef = (key) => (el) => {
    refs.value.set(key, el);
  };
  onBeforeUpdate(() => {
    refs.value = /* @__PURE__ */ new Map();
  });
  return [setRef, refs];
};
var useRefs_default = useRefs;

export {
  useRefs_default
};
//# sourceMappingURL=chunk-YCAVULPA.js.map
