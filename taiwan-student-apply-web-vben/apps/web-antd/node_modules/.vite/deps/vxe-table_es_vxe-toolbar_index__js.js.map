{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/index.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-toolbar/index.js"], "sourcesContent": ["import { VxeUI } from '../ui';\nimport VxeToolbarComponent from './src/toolbar';\nexport const VxeToolbar = Object.assign({}, VxeToolbarComponent, {\n    install(app) {\n        app.component(VxeToolbarComponent.name, VxeToolbarComponent);\n    }\n});\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeToolbarComponent.name, VxeToolbarComponent);\n}\nVxeUI.component(VxeToolbarComponent);\nexport const Toolbar = VxeToolbar;\nexport default VxeToolbar;\n", "import VxeToolbar from '../toolbar';\nexport * from '../toolbar';\nexport default VxeToolbar;\n"], "mappings": ";;;;;;;;;;;;;;AAEO,IAAM,aAAa,OAAO,OAAO,CAAC,GAAG,iBAAqB;AAAA,EAC7D,QAAQ,KAAK;AACT,QAAI,UAAU,gBAAoB,MAAM,eAAmB;AAAA,EAC/D;AACJ,CAAC;AACD,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,gBAAoB,MAAM,eAAmB;AAC5E;AACA,MAAM,UAAU,eAAmB;AAC5B,IAAM,UAAU;AACvB,IAAOA,mBAAQ;;;ACVf,IAAO,sBAAQC;", "names": ["toolbar_default", "toolbar_default"]}