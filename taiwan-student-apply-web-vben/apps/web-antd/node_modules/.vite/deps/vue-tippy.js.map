{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vue-tippy@6.7.0_vue@3.5.13_typescript@5.8.3_/node_modules/vue-tippy/dist/vue-tippy.esm-browser.js"], "sourcesContent": ["/*!\n  * vue-tippy v6.7.0\n  * (c) 2025 \n  * @license MIT\n  */\nimport { getCurrentInstance, ref, shallowRef, onMounted, onUnmounted, isRef, isReactive, watch, isVNode, createApp, h, defineComponent, nextTick, unref, reactive } from 'vue';\n\nvar top = 'top';\nvar bottom = 'bottom';\nvar right = 'right';\nvar left = 'left';\nvar auto = 'auto';\nvar basePlacements = [top, bottom, right, left];\nvar start = 'start';\nvar end = 'end';\nvar clippingParents = 'clippingParents';\nvar viewport = 'viewport';\nvar popper = 'popper';\nvar reference = 'reference';\nvar variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nvar placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nvar beforeRead = 'beforeRead';\nvar read = 'read';\nvar afterRead = 'afterRead'; // pure-logic modifiers\n\nvar beforeMain = 'beforeMain';\nvar main = 'main';\nvar afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nvar beforeWrite = 'beforeWrite';\nvar write = 'write';\nvar afterWrite = 'afterWrite';\nvar modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];\n\nfunction getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n\nfunction getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar applyStyles$1 = {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};\n\nfunction getBasePlacement(placement) {\n  return placement.split('-')[0];\n}\n\nvar max = Math.max;\nvar min = Math.min;\nvar round = Math.round;\n\nfunction getBoundingClientRect(element, includeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (isHTMLElement(element) && includeScale) {\n    var offsetHeight = element.offsetHeight;\n    var offsetWidth = element.offsetWidth; // Do not attempt to divide by 0, otherwise we get `Infinity` as scale\n    // Fallback to 1 in case both values are `0`\n\n    if (offsetWidth > 0) {\n      scaleX = round(rect.width) / offsetWidth || 1;\n    }\n\n    if (offsetHeight > 0) {\n      scaleY = round(rect.height) / offsetHeight || 1;\n    }\n  }\n\n  return {\n    width: rect.width / scaleX,\n    height: rect.height / scaleY,\n    top: rect.top / scaleY,\n    right: rect.right / scaleX,\n    bottom: rect.bottom / scaleY,\n    left: rect.left / scaleX,\n    x: rect.left / scaleX,\n    y: rect.top / scaleY\n  };\n}\n\n// means it doesn't take into account transforms.\n\nfunction getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}\n\nfunction contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}\n\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\n\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n\nfunction getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}\n\nfunction getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nfunction getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n\nfunction getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n\nfunction within(min$1, value, max$1) {\n  return max(min$1, min(value, max$1));\n}\nfunction withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}\n\nfunction getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}\n\nfunction mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}\n\nfunction expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect$1(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar arrow$1 = {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect$1,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};\n\nfunction getVariation(placement) {\n  return placement.split('-')[1];\n}\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nfunction mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar computeStyles$1 = {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};\n\nvar passive = {\n  passive: true\n};\n\nfunction effect$2(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar eventListeners = {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect$2,\n  data: {}\n};\n\nvar hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\nvar hash$1 = {\n  start: 'end',\n  end: 'start'\n};\nfunction getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash$1[matched];\n  });\n}\n\nfunction getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}\n\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}\n\nfunction getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}\n\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nfunction getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}\n\nfunction isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n\nfunction getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nfunction listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}\n\nfunction rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body' && (canEscapeClipping ? getComputedStyle(clippingParent).position !== 'static' : true);\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nfunction getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}\n\nfunction computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n    }\n  }\n\n  return offsets;\n}\n\nfunction detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n\nfunction computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements$1 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements$1.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements$1;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar flip$1 = {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar hide$1 = {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};\n\nfunction distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar offset$1 = {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar popperOffsets$1 = {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};\n\nfunction getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min$1 = offset + overflow[mainSide];\n    var max$1 = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? min(min$1, tetherMin) : min$1, offset, tether ? max(max$1, tetherMax) : max$1);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar preventOverflow$1 = {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};\n\nfunction getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}\n\nfunction getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nfunction getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nfunction orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}\n\nfunction debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n\nfunction mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}\n\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nfunction popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nvar defaultModifiers = [eventListeners, popperOffsets$1, computeStyles$1, applyStyles$1, offset$1, flip$1, preventOverflow$1, arrow$1, hide$1];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\n/**!\n* tippy.js v6.3.7\n* (c) 2017-2021 atomiks\n* MIT License\n*/\n\nvar ROUND_ARROW = '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\nvar BOX_CLASS = \"tippy-box\";\nvar CONTENT_CLASS = \"tippy-content\";\nvar BACKDROP_CLASS = \"tippy-backdrop\";\nvar ARROW_CLASS = \"tippy-arrow\";\nvar SVG_ARROW_CLASS = \"tippy-svg-arrow\";\nvar TOUCH_OPTIONS = {\n  passive: true,\n  capture: true\n};\nvar TIPPY_DEFAULT_APPEND_TO = function TIPPY_DEFAULT_APPEND_TO() {\n  return document.body;\n};\nfunction getValueAtIndexOrReturn(value, index, defaultValue) {\n  if (Array.isArray(value)) {\n    var v = value[index];\n    return v == null ? Array.isArray(defaultValue) ? defaultValue[index] : defaultValue : v;\n  }\n\n  return value;\n}\nfunction isType(value, type) {\n  var str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(type + \"]\") > -1;\n}\nfunction invokeWithArgsOrReturn(value, args) {\n  return typeof value === 'function' ? value.apply(void 0, args) : value;\n}\nfunction debounce$1(fn, ms) {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  var timeout;\n  return function (arg) {\n    clearTimeout(timeout);\n    timeout = setTimeout(function () {\n      fn(arg);\n    }, ms);\n  };\n}\nfunction removeProperties(obj, keys) {\n  var clone = Object.assign({}, obj);\n  keys.forEach(function (key) {\n    delete clone[key];\n  });\n  return clone;\n}\nfunction splitBySpaces(value) {\n  return value.split(/\\s+/).filter(Boolean);\n}\nfunction normalizeToArray(value) {\n  return [].concat(value);\n}\nfunction pushIfUnique(arr, value) {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\nfunction unique(arr) {\n  return arr.filter(function (item, index) {\n    return arr.indexOf(item) === index;\n  });\n}\nfunction getBasePlacement$1(placement) {\n  return placement.split('-')[0];\n}\nfunction arrayFrom(value) {\n  return [].slice.call(value);\n}\nfunction removeUndefinedProps(obj) {\n  return Object.keys(obj).reduce(function (acc, key) {\n    if (obj[key] !== undefined) {\n      acc[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction div() {\n  return document.createElement('div');\n}\nfunction isElement$1(value) {\n  return ['Element', 'Fragment'].some(function (type) {\n    return isType(value, type);\n  });\n}\nfunction isNodeList(value) {\n  return isType(value, 'NodeList');\n}\nfunction isMouseEvent(value) {\n  return isType(value, 'MouseEvent');\n}\nfunction isReferenceElement(value) {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\nfunction getArrayOfElements(value) {\n  if (isElement$1(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\nfunction setTransitionDuration(els, value) {\n  els.forEach(function (el) {\n    if (el) {\n      el.style.transitionDuration = value + \"ms\";\n    }\n  });\n}\nfunction setVisibilityState(els, state) {\n  els.forEach(function (el) {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\nfunction getOwnerDocument(elementOrElements) {\n  var _element$ownerDocumen;\n\n  var _normalizeToArray = normalizeToArray(elementOrElements),\n      element = _normalizeToArray[0]; // Elements created via a <template> have an ownerDocument with no reference to the body\n\n\n  return element != null && (_element$ownerDocumen = element.ownerDocument) != null && _element$ownerDocumen.body ? element.ownerDocument : document;\n}\nfunction isCursorOutsideInteractiveBorder(popperTreeData, event) {\n  var clientX = event.clientX,\n      clientY = event.clientY;\n  return popperTreeData.every(function (_ref) {\n    var popperRect = _ref.popperRect,\n        popperState = _ref.popperState,\n        props = _ref.props;\n    var interactiveBorder = props.interactiveBorder;\n    var basePlacement = getBasePlacement$1(popperState.placement);\n    var offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    var topDistance = basePlacement === 'bottom' ? offsetData.top.y : 0;\n    var bottomDistance = basePlacement === 'top' ? offsetData.bottom.y : 0;\n    var leftDistance = basePlacement === 'right' ? offsetData.left.x : 0;\n    var rightDistance = basePlacement === 'left' ? offsetData.right.x : 0;\n    var exceedsTop = popperRect.top - clientY + topDistance > interactiveBorder;\n    var exceedsBottom = clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    var exceedsLeft = popperRect.left - clientX + leftDistance > interactiveBorder;\n    var exceedsRight = clientX - popperRect.right - rightDistance > interactiveBorder;\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\nfunction updateTransitionEndListener(box, action, listener) {\n  var method = action + \"EventListener\"; // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n\n  ['transitionend', 'webkitTransitionEnd'].forEach(function (event) {\n    box[method](event, listener);\n  });\n}\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\n\nfunction actualContains(parent, child) {\n  var target = child;\n\n  while (target) {\n    var _target$getRootNode;\n\n    if (parent.contains(target)) {\n      return true;\n    }\n\n    target = target.getRootNode == null ? void 0 : (_target$getRootNode = target.getRootNode()) == null ? void 0 : _target$getRootNode.host;\n  }\n\n  return false;\n}\n\nvar currentInput = {\n  isTouch: false\n};\nvar lastMouseMoveTime = 0;\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\n\nfunction onDocumentTouchStart() {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\n\nfunction onDocumentMouseMove() {\n  var now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\n\nfunction onWindowBlur() {\n  var activeElement = document.activeElement;\n\n  if (isReferenceElement(activeElement)) {\n    var instance = activeElement._tippy;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\nfunction bindGlobalEventListeners() {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nvar isIE11 = isBrowser ? // @ts-ignore\n!!window.msCrypto : false;\n\nvar pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false\n};\nvar renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999\n};\nvar defaultProps = Object.assign({\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto'\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate: function onAfterUpdate() {},\n  onBeforeUpdate: function onBeforeUpdate() {},\n  onCreate: function onCreate() {},\n  onDestroy: function onDestroy() {},\n  onHidden: function onHidden() {},\n  onHide: function onHide() {},\n  onMount: function onMount() {},\n  onShow: function onShow() {},\n  onShown: function onShown() {},\n  onTrigger: function onTrigger() {},\n  onUntrigger: function onUntrigger() {},\n  onClickOutside: function onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null\n}, pluginProps, renderProps);\nvar defaultKeys = Object.keys(defaultProps);\nvar setDefaultProps = function setDefaultProps(partialProps) {\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (key) {\n    defaultProps[key] = partialProps[key];\n  });\n};\nfunction getExtendedPassedProps(passedProps) {\n  var plugins = passedProps.plugins || [];\n  var pluginProps = plugins.reduce(function (acc, plugin) {\n    var name = plugin.name,\n        defaultValue = plugin.defaultValue;\n\n    if (name) {\n      var _name;\n\n      acc[name] = passedProps[name] !== undefined ? passedProps[name] : (_name = defaultProps[name]) != null ? _name : defaultValue;\n    }\n\n    return acc;\n  }, {});\n  return Object.assign({}, passedProps, pluginProps);\n}\nfunction getDataAttributeProps(reference, plugins) {\n  var propKeys = plugins ? Object.keys(getExtendedPassedProps(Object.assign({}, defaultProps, {\n    plugins: plugins\n  }))) : defaultKeys;\n  var props = propKeys.reduce(function (acc, key) {\n    var valueAsString = (reference.getAttribute(\"data-tippy-\" + key) || '').trim();\n\n    if (!valueAsString) {\n      return acc;\n    }\n\n    if (key === 'content') {\n      acc[key] = valueAsString;\n    } else {\n      try {\n        acc[key] = JSON.parse(valueAsString);\n      } catch (e) {\n        acc[key] = valueAsString;\n      }\n    }\n\n    return acc;\n  }, {});\n  return props;\n}\nfunction evaluateProps(reference, props) {\n  var out = Object.assign({}, props, {\n    content: invokeWithArgsOrReturn(props.content, [reference])\n  }, props.ignoreAttributes ? {} : getDataAttributeProps(reference, props.plugins));\n  out.aria = Object.assign({}, defaultProps.aria, out.aria);\n  out.aria = {\n    expanded: out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content: out.aria.content === 'auto' ? props.interactive ? null : 'describedby' : out.aria.content\n  };\n  return out;\n}\n\nvar innerHTML = function innerHTML() {\n  return 'innerHTML';\n};\n\nfunction dangerouslySetInnerHTML(element, html) {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value) {\n  var arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement$1(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value);\n    }\n  }\n\n  return arrow;\n}\n\nfunction setContent(content, props) {\n  if (isElement$1(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\nfunction getChildren(popper) {\n  var box = popper.firstElementChild;\n  var boxChildren = arrayFrom(box.children);\n  return {\n    box: box,\n    content: boxChildren.find(function (node) {\n      return node.classList.contains(CONTENT_CLASS);\n    }),\n    arrow: boxChildren.find(function (node) {\n      return node.classList.contains(ARROW_CLASS) || node.classList.contains(SVG_ARROW_CLASS);\n    }),\n    backdrop: boxChildren.find(function (node) {\n      return node.classList.contains(BACKDROP_CLASS);\n    })\n  };\n}\nfunction render(instance) {\n  var popper = div();\n  var box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n  var content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n  setContent(content, instance.props);\n  popper.appendChild(box);\n  box.appendChild(content);\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps, nextProps) {\n    var _getChildren = getChildren(popper),\n        box = _getChildren.box,\n        content = _getChildren.content,\n        arrow = _getChildren.arrow;\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth = typeof nextProps.maxWidth === 'number' ? nextProps.maxWidth + \"px\" : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (prevProps.content !== nextProps.content || prevProps.allowHTML !== nextProps.allowHTML) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow);\n    }\n  }\n\n  return {\n    popper: popper,\n    onUpdate: onUpdate\n  };\n} // Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\n\nrender.$$tippy = true;\n\nvar idCounter = 1;\nvar mouseMoveListeners = []; // Used by `hideAll()`\n\nvar mountedInstances = [];\nfunction createTippy(reference, passedProps) {\n  var props = evaluateProps(reference, Object.assign({}, defaultProps, getExtendedPassedProps(removeUndefinedProps(passedProps)))); // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n\n  var showTimeout;\n  var hideTimeout;\n  var scheduleHideAnimationFrame;\n  var isVisibleFromClick = false;\n  var didHideDueToDocumentMouseDown = false;\n  var didTouchMove = false;\n  var ignoreOnFirstUpdate = false;\n  var lastTriggerEvent;\n  var currentTransitionEndListener;\n  var onFirstUpdate;\n  var listeners = [];\n  var debouncedOnMouseMove = debounce$1(onMouseMove, props.interactiveDebounce);\n  var currentTarget; // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n\n  var id = idCounter++;\n  var popperInstance = null;\n  var plugins = unique(props.plugins);\n  var state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false\n  };\n  var instance = {\n    // properties\n    id: id,\n    reference: reference,\n    popper: div(),\n    popperInstance: popperInstance,\n    props: props,\n    state: state,\n    plugins: plugins,\n    // methods\n    clearDelayTimeouts: clearDelayTimeouts,\n    setProps: setProps,\n    setContent: setContent,\n    show: show,\n    hide: hide,\n    hideWithInteractivity: hideWithInteractivity,\n    enable: enable,\n    disable: disable,\n    unmount: unmount,\n    destroy: destroy\n  }; // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n\n  /* istanbul ignore if */\n\n  if (!props.render) {\n\n    return instance;\n  } // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n\n\n  var _props$render = props.render(instance),\n      popper = _props$render.popper,\n      onUpdate = _props$render.onUpdate;\n\n  popper.setAttribute('data-tippy-root', '');\n  popper.id = \"tippy-\" + instance.id;\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n  var pluginsHooks = plugins.map(function (plugin) {\n    return plugin.fn(instance);\n  });\n  var hasAriaExpanded = reference.hasAttribute('aria-expanded');\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  } // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n\n\n  popper.addEventListener('mouseenter', function () {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n  popper.addEventListener('mouseleave', function () {\n    if (instance.props.interactive && instance.props.trigger.indexOf('mouseenter') >= 0) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n  return instance; // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n\n  function getNormalizedTouchSettings() {\n    var touch = instance.props.touch;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior() {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn() {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    return !!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy);\n  }\n\n  function getCurrentTarget() {\n    return currentTarget || reference;\n  }\n\n  function getDocument() {\n    var parent = getCurrentTarget().parentNode;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren() {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow) {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (instance.state.isMounted && !instance.state.isVisible || currentInput.isTouch || lastTriggerEvent && lastTriggerEvent.type === 'focus') {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(instance.props.delay, isShow ? 0 : 1, defaultProps.delay);\n  }\n\n  function handleStyles(fromHide) {\n    if (fromHide === void 0) {\n      fromHide = false;\n    }\n\n    popper.style.pointerEvents = instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = \"\" + instance.props.zIndex;\n  }\n\n  function invokeHook(hook, args, shouldInvokePropsHook) {\n    if (shouldInvokePropsHook === void 0) {\n      shouldInvokePropsHook = true;\n    }\n\n    pluginsHooks.forEach(function (pluginHooks) {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook].apply(pluginHooks, args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      var _instance$props;\n\n      (_instance$props = instance.props)[hook].apply(_instance$props, args);\n    }\n  }\n\n  function handleAriaContentAttribute() {\n    var aria = instance.props.aria;\n\n    if (!aria.content) {\n      return;\n    }\n\n    var attr = \"aria-\" + aria.content;\n    var id = popper.id;\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      var currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? currentValue + \" \" + id : id);\n      } else {\n        var nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute() {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      if (instance.props.interactive) {\n        node.setAttribute('aria-expanded', instance.state.isVisible && node === getCurrentTarget() ? 'true' : 'false');\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners() {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(function (listener) {\n      return listener !== debouncedOnMouseMove;\n    });\n  }\n\n  function onDocumentPress(event) {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    var actualTarget = event.composedPath && event.composedPath()[0] || event.target; // Clicked on interactive popper\n\n    if (instance.props.interactive && actualContains(popper, actualTarget)) {\n      return;\n    } // Clicked on the event listeners target\n\n\n    if (normalizeToArray(instance.props.triggerTarget || reference).some(function (el) {\n      return actualContains(el, actualTarget);\n    })) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (instance.state.isVisible && instance.props.trigger.indexOf('click') >= 0) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide(); // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(function () {\n        didHideDueToDocumentMouseDown = false;\n      }); // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove() {\n    didTouchMove = true;\n  }\n\n  function onTouchStart() {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress() {\n    var doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress() {\n    var doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration, callback) {\n    onTransitionEnd(duration, function () {\n      if (!instance.state.isVisible && popper.parentNode && popper.parentNode.contains(popper)) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration, callback) {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration, callback) {\n    var box = getDefaultTemplateChildren().box;\n\n    function listener(event) {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    } // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n\n\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n    currentTransitionEndListener = listener;\n  }\n\n  function on(eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({\n        node: node,\n        eventType: eventType,\n        handler: handler,\n        options: options\n      });\n    });\n  }\n\n  function addListeners() {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {\n        passive: true\n      });\n      on('touchend', onMouseLeave, {\n        passive: true\n      });\n    }\n\n    splitBySpaces(instance.props.trigger).forEach(function (eventType) {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave);\n          break;\n\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut);\n          break;\n\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut);\n          break;\n      }\n    });\n  }\n\n  function removeListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event) {\n    var _lastTriggerEvent;\n\n    var shouldScheduleClickHide = false;\n\n    if (!instance.state.isEnabled || isEventListenerStopped(event) || didHideDueToDocumentMouseDown) {\n      return;\n    }\n\n    var wasFocused = ((_lastTriggerEvent = lastTriggerEvent) == null ? void 0 : _lastTriggerEvent.type) === 'focus';\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget;\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach(function (listener) {\n        return listener(event);\n      });\n    } // Toggle show/hide when clicking click-triggered tooltips\n\n\n    if (event.type === 'click' && (instance.props.trigger.indexOf('mouseenter') < 0 || isVisibleFromClick) && instance.props.hideOnClick !== false && instance.state.isVisible) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event) {\n    var target = event.target;\n    var isCursorOverReferenceOrPopper = getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    var popperTreeData = getNestedPopperTree().concat(popper).map(function (popper) {\n      var _instance$popperInsta;\n\n      var instance = popper._tippy;\n      var state = (_instance$popperInsta = instance.popperInstance) == null ? void 0 : _instance$popperInsta.state;\n\n      if (state) {\n        return {\n          popperRect: popper.getBoundingClientRect(),\n          popperState: state,\n          props: props\n        };\n      }\n\n      return null;\n    }).filter(Boolean);\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event) {\n    var shouldBail = isEventListenerStopped(event) || instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick;\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event) {\n    if (instance.props.trigger.indexOf('focusin') < 0 && event.target !== getCurrentTarget()) {\n      return;\n    } // If focus was moved to within the popper\n\n\n    if (instance.props.interactive && event.relatedTarget && popper.contains(event.relatedTarget)) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event) {\n    return currentInput.isTouch ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0 : false;\n  }\n\n  function createPopperInstance() {\n    destroyPopperInstance();\n    var _instance$props2 = instance.props,\n        popperOptions = _instance$props2.popperOptions,\n        placement = _instance$props2.placement,\n        offset = _instance$props2.offset,\n        getReferenceClientRect = _instance$props2.getReferenceClientRect,\n        moveTransition = _instance$props2.moveTransition;\n    var arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n    var computedReference = getReferenceClientRect ? {\n      getBoundingClientRect: getReferenceClientRect,\n      contextElement: getReferenceClientRect.contextElement || getCurrentTarget()\n    } : reference;\n    var tippyModifier = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (getIsDefaultRenderFn()) {\n          var _getDefaultTemplateCh = getDefaultTemplateChildren(),\n              box = _getDefaultTemplateCh.box;\n\n          ['placement', 'reference-hidden', 'escaped'].forEach(function (attr) {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[\"data-popper-\" + attr]) {\n                box.setAttribute(\"data-\" + attr, '');\n              } else {\n                box.removeAttribute(\"data-\" + attr);\n              }\n            }\n          });\n          state.attributes.popper = {};\n        }\n      }\n    };\n    var modifiers = [{\n      name: 'offset',\n      options: {\n        offset: offset\n      }\n    }, {\n      name: 'preventOverflow',\n      options: {\n        padding: {\n          top: 2,\n          bottom: 2,\n          left: 5,\n          right: 5\n        }\n      }\n    }, {\n      name: 'flip',\n      options: {\n        padding: 5\n      }\n    }, {\n      name: 'computeStyles',\n      options: {\n        adaptive: !moveTransition\n      }\n    }, tippyModifier];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3\n        }\n      });\n    }\n\n    modifiers.push.apply(modifiers, (popperOptions == null ? void 0 : popperOptions.modifiers) || []);\n    instance.popperInstance = createPopper(computedReference, popper, Object.assign({}, popperOptions, {\n      placement: placement,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: modifiers\n    }));\n  }\n\n  function destroyPopperInstance() {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount() {\n    var appendTo = instance.props.appendTo;\n    var parentNode; // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n\n    var node = getCurrentTarget();\n\n    if (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO || appendTo === 'parent') {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    } // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n\n\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n    createPopperInstance();\n  }\n\n  function getNestedPopperTree() {\n    return arrayFrom(popper.querySelectorAll('[data-tippy-root]'));\n  }\n\n  function scheduleShow(event) {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n    var delay = getDelay(true);\n\n    var _getNormalizedTouchSe = getNormalizedTouchSettings(),\n        touchValue = _getNormalizedTouchSe[0],\n        touchDelay = _getNormalizedTouchSe[1];\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(function () {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event) {\n    instance.clearDelayTimeouts();\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n      return;\n    } // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n\n\n    if (instance.props.trigger.indexOf('mouseenter') >= 0 && instance.props.trigger.indexOf('click') >= 0 && ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 && isVisibleFromClick) {\n      return;\n    }\n\n    var delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(function () {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(function () {\n        instance.hide();\n      });\n    }\n  } // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n\n\n  function enable() {\n    instance.state.isEnabled = true;\n  }\n\n  function disable() {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts() {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps) {\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n    removeListeners();\n    var prevProps = instance.props;\n    var nextProps = evaluateProps(reference, Object.assign({}, prevProps, removeUndefinedProps(partialProps), {\n      ignoreAttributes: true\n    }));\n    instance.props = nextProps;\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce$1(onMouseMove, nextProps.interactiveDebounce);\n    } // Ensure stale aria-expanded attributes are removed\n\n\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach(function (node) {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance(); // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n\n      getNestedPopperTree().forEach(function (nestedPopper) {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy.popperInstance.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content) {\n    instance.setProps({\n      content: content\n    });\n  }\n\n  function show() {\n\n\n    var isAlreadyVisible = instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var isTouchAndTouchDisabled = currentInput.isTouch && !instance.props.touch;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 0, defaultProps.duration);\n\n    if (isAlreadyVisible || isDestroyed || isDisabled || isTouchAndTouchDisabled) {\n      return;\n    } // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n\n\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    } // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh2 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh2.box,\n          content = _getDefaultTemplateCh2.content;\n\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = function onFirstUpdate() {\n      var _instance$popperInsta2;\n\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true; // reflow\n\n      void popper.offsetHeight;\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        var _getDefaultTemplateCh3 = getDefaultTemplateChildren(),\n            _box = _getDefaultTemplateCh3.box,\n            _content = _getDefaultTemplateCh3.content;\n\n        setTransitionDuration([_box, _content], duration);\n        setVisibilityState([_box, _content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n      pushIfUnique(mountedInstances, instance); // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n\n      (_instance$popperInsta2 = instance.popperInstance) == null ? void 0 : _instance$popperInsta2.forceUpdate();\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, function () {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide() {\n\n\n    var isAlreadyHidden = !instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 1, defaultProps.duration);\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh4 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh4.box,\n          content = _getDefaultTemplateCh4.content;\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event) {\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount() {\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance(); // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n\n    getNestedPopperTree().forEach(function (nestedPopper) {\n      nestedPopper._tippy.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter(function (i) {\n      return i !== instance;\n    });\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy() {\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n    removeListeners();\n    delete reference._tippy;\n    instance.state.isDestroyed = true;\n    invokeHook('onDestroy', [instance]);\n  }\n}\n\nfunction tippy(targets, optionalProps) {\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  var plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  bindGlobalEventListeners();\n  var passedProps = Object.assign({}, optionalProps, {\n    plugins: plugins\n  });\n  var elements = getArrayOfElements(targets);\n\n  var instances = elements.reduce(function (acc, reference) {\n    var instance = reference && createTippy(reference, passedProps);\n\n    if (instance) {\n      acc.push(instance);\n    }\n\n    return acc;\n  }, []);\n  return isElement$1(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\nvar hideAll = function hideAll(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      excludedReferenceOrInstance = _ref.exclude,\n      duration = _ref.duration;\n\n  mountedInstances.forEach(function (instance) {\n    var isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance) ? instance.reference === excludedReferenceOrInstance : instance.popper === excludedReferenceOrInstance.popper;\n    }\n\n    if (!isExcluded) {\n      var originalDuration = instance.props.duration;\n      instance.setProps({\n        duration: duration\n      });\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({\n          duration: originalDuration\n        });\n      }\n    }\n  });\n};\n\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\n\nvar applyStylesModifier = Object.assign({}, applyStyles$1, {\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    var initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      },\n      reference: {}\n    };\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    } // intentionally return no cleanup function\n    // return () => { ... }\n\n  }\n});\n\nvar createSingleton = function createSingleton(tippyInstances, optionalProps) {\n  var _optionalProps$popper;\n\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  var individualInstances = tippyInstances;\n  var references = [];\n  var triggerTargets = [];\n  var currentTarget;\n  var overrides = optionalProps.overrides;\n  var interceptSetPropsCleanups = [];\n  var shownOnCreate = false;\n\n  function setTriggerTargets() {\n    triggerTargets = individualInstances.map(function (instance) {\n      return normalizeToArray(instance.props.triggerTarget || instance.reference);\n    }).reduce(function (acc, item) {\n      return acc.concat(item);\n    }, []);\n  }\n\n  function setReferences() {\n    references = individualInstances.map(function (instance) {\n      return instance.reference;\n    });\n  }\n\n  function enableInstances(isEnabled) {\n    individualInstances.forEach(function (instance) {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton) {\n    return individualInstances.map(function (instance) {\n      var originalSetProps = instance.setProps;\n\n      instance.setProps = function (props) {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return function () {\n        instance.setProps = originalSetProps;\n      };\n    });\n  } // have to pass singleton, as it maybe undefined on first call\n\n\n  function prepareInstance(singleton, target) {\n    var index = triggerTargets.indexOf(target); // bail-out\n\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n    var overrideProps = (overrides || []).concat('content').reduce(function (acc, prop) {\n      acc[prop] = individualInstances[index].props[prop];\n      return acc;\n    }, {});\n    singleton.setProps(Object.assign({}, overrideProps, {\n      getReferenceClientRect: typeof overrideProps.getReferenceClientRect === 'function' ? overrideProps.getReferenceClientRect : function () {\n        var _references$index;\n\n        return (_references$index = references[index]) == null ? void 0 : _references$index.getBoundingClientRect();\n      }\n    }));\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n  var plugin = {\n    fn: function fn() {\n      return {\n        onDestroy: function onDestroy() {\n          enableInstances(true);\n        },\n        onHidden: function onHidden() {\n          currentTarget = null;\n        },\n        onClickOutside: function onClickOutside(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow: function onShow(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger: function onTrigger(instance, event) {\n          prepareInstance(instance, event.currentTarget);\n        }\n      };\n    }\n  };\n  var singleton = tippy(div(), Object.assign({}, removeProperties(optionalProps, ['overrides']), {\n    plugins: [plugin].concat(optionalProps.plugins || []),\n    triggerTarget: triggerTargets,\n    popperOptions: Object.assign({}, optionalProps.popperOptions, {\n      modifiers: [].concat(((_optionalProps$popper = optionalProps.popperOptions) == null ? void 0 : _optionalProps$popper.modifiers) || [], [applyStylesModifier])\n    })\n  }));\n  var originalShow = singleton.show;\n\n  singleton.show = function (target) {\n    originalShow(); // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    } // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n\n\n    if (currentTarget && target == null) {\n      return;\n    } // target is index of instance\n\n\n    if (typeof target === 'number') {\n      return references[target] && prepareInstance(singleton, references[target]);\n    } // target is a child tippy instance\n\n\n    if (individualInstances.indexOf(target) >= 0) {\n      var ref = target.reference;\n      return prepareInstance(singleton, ref);\n    } // target is a ReferenceElement\n\n\n    if (references.indexOf(target) >= 0) {\n      return prepareInstance(singleton, target);\n    }\n  };\n\n  singleton.showNext = function () {\n    var first = references[0];\n\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n\n    var index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = function () {\n    var last = references[references.length - 1];\n\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n\n    var index = references.indexOf(currentTarget);\n    var target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  var originalSetProps = singleton.setProps;\n\n  singleton.setProps = function (props) {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = function (nextInstances) {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach(function (fn) {\n      return fn();\n    });\n    individualInstances = nextInstances;\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n    singleton.setProps({\n      triggerTarget: triggerTargets\n    });\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n  return singleton;\n};\n\nvar animateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    if (!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy)) {\n\n      return {};\n    }\n\n    var _getChildren = getChildren(instance.popper),\n        box = _getChildren.box,\n        content = _getChildren.content;\n\n    var backdrop = instance.props.animateFill ? createBackdropElement() : null;\n    return {\n      onCreate: function onCreate() {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n          instance.setProps({\n            arrow: false,\n            animation: 'shift-away'\n          });\n        }\n      },\n      onMount: function onMount() {\n        if (backdrop) {\n          var transitionDuration = box.style.transitionDuration;\n          var duration = Number(transitionDuration.replace('ms', '')); // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n\n          content.style.transitionDelay = Math.round(duration / 10) + \"ms\";\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow: function onShow() {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide: function onHide() {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      }\n    };\n  }\n};\n\nfunction createBackdropElement() {\n  var backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n\nvar mouseCoords = {\n  clientX: 0,\n  clientY: 0\n};\nvar activeInstances = [];\n\nfunction storeMouseCoords(_ref) {\n  var clientX = _ref.clientX,\n      clientY = _ref.clientY;\n  mouseCoords = {\n    clientX: clientX,\n    clientY: clientY\n  };\n}\n\nfunction addMouseCoordsListener(doc) {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc) {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nvar followCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n    var doc = getOwnerDocument(instance.props.triggerTarget || reference);\n    var isInternalUpdate = false;\n    var wasFocusEvent = false;\n    var isUnmounted = true;\n    var prevProps = instance.props;\n\n    function getIsInitialBehavior() {\n      return instance.props.followCursor === 'initial' && instance.state.isVisible;\n    }\n\n    function addListener() {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener() {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect() {\n      isInternalUpdate = true;\n      instance.setProps({\n        getReferenceClientRect: null\n      });\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event) {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      var isCursorOverReference = event.target ? reference.contains(event.target) : true;\n      var followCursor = instance.props.followCursor;\n      var clientX = event.clientX,\n          clientY = event.clientY;\n      var rect = reference.getBoundingClientRect();\n      var relativeX = clientX - rect.left;\n      var relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect: function getReferenceClientRect() {\n            var rect = reference.getBoundingClientRect();\n            var x = clientX;\n            var y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            var top = followCursor === 'horizontal' ? rect.top : y;\n            var right = followCursor === 'vertical' ? rect.right : x;\n            var bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            var left = followCursor === 'vertical' ? rect.left : x;\n            return {\n              width: right - left,\n              height: bottom - top,\n              top: top,\n              right: right,\n              bottom: bottom,\n              left: left\n            };\n          }\n        });\n      }\n    }\n\n    function create() {\n      if (instance.props.followCursor) {\n        activeInstances.push({\n          instance: instance,\n          doc: doc\n        });\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy() {\n      activeInstances = activeInstances.filter(function (data) {\n        return data.instance !== instance;\n      });\n\n      if (activeInstances.filter(function (data) {\n        return data.doc === doc;\n      }).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate: function onBeforeUpdate() {\n        prevProps = instance.props;\n      },\n      onAfterUpdate: function onAfterUpdate(_, _ref2) {\n        var followCursor = _ref2.followCursor;\n\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (followCursor !== undefined && prevProps.followCursor !== followCursor) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (instance.state.isMounted && !wasFocusEvent && !getIsInitialBehavior()) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount: function onMount() {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          mouseCoords = {\n            clientX: event.clientX,\n            clientY: event.clientY\n          };\n        }\n\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden: function onHidden() {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      }\n    };\n  }\n};\n\nfunction getProps(props, modifier) {\n  var _props$popperOptions;\n\n  return {\n    popperOptions: Object.assign({}, props.popperOptions, {\n      modifiers: [].concat((((_props$popperOptions = props.popperOptions) == null ? void 0 : _props$popperOptions.modifiers) || []).filter(function (_ref) {\n        var name = _ref.name;\n        return name !== modifier.name;\n      }), [modifier])\n    })\n  };\n}\n\nvar inlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n\n    function isEnabled() {\n      return !!instance.props.inlinePositioning;\n    }\n\n    var placement;\n    var cursorRectIndex = -1;\n    var isInternalUpdate = false;\n    var triedPlacements = [];\n    var modifier = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (placement !== state.placement && triedPlacements.indexOf(state.placement) === -1) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: function getReferenceClientRect() {\n                return _getReferenceClientRect(state.placement);\n              }\n            });\n          }\n\n          placement = state.placement;\n        }\n      }\n    };\n\n    function _getReferenceClientRect(placement) {\n      return getInlineBoundingClientRect(getBasePlacement$1(placement), reference.getBoundingClientRect(), arrayFrom(reference.getClientRects()), cursorRectIndex);\n    }\n\n    function setInternalProps(partialProps) {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier() {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          var rects = arrayFrom(instance.reference.getClientRects());\n          var cursorRect = rects.find(function (rect) {\n            return rect.left - 2 <= event.clientX && rect.right + 2 >= event.clientX && rect.top - 2 <= event.clientY && rect.bottom + 2 >= event.clientY;\n          });\n          var index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden: function onHidden() {\n        cursorRectIndex = -1;\n      }\n    };\n  }\n};\nfunction getInlineBoundingClientRect(currentBasePlacement, boundingRect, clientRects, cursorRectIndex) {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  } // There are two rects and they are disjoined\n\n\n  if (clientRects.length === 2 && cursorRectIndex >= 0 && clientRects[0].left > clientRects[1].right) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom':\n      {\n        var firstRect = clientRects[0];\n        var lastRect = clientRects[clientRects.length - 1];\n        var isTop = currentBasePlacement === 'top';\n        var top = firstRect.top;\n        var bottom = lastRect.bottom;\n        var left = isTop ? firstRect.left : lastRect.left;\n        var right = isTop ? firstRect.right : lastRect.right;\n        var width = right - left;\n        var height = bottom - top;\n        return {\n          top: top,\n          bottom: bottom,\n          left: left,\n          right: right,\n          width: width,\n          height: height\n        };\n      }\n\n    case 'left':\n    case 'right':\n      {\n        var minLeft = Math.min.apply(Math, clientRects.map(function (rects) {\n          return rects.left;\n        }));\n        var maxRight = Math.max.apply(Math, clientRects.map(function (rects) {\n          return rects.right;\n        }));\n        var measureRects = clientRects.filter(function (rect) {\n          return currentBasePlacement === 'left' ? rect.left === minLeft : rect.right === maxRight;\n        });\n        var _top = measureRects[0].top;\n        var _bottom = measureRects[measureRects.length - 1].bottom;\n        var _left = minLeft;\n        var _right = maxRight;\n\n        var _width = _right - _left;\n\n        var _height = _bottom - _top;\n\n        return {\n          top: _top,\n          bottom: _bottom,\n          left: _left,\n          right: _right,\n          width: _width,\n          height: _height\n        };\n      }\n\n    default:\n      {\n        return boundingRect;\n      }\n  }\n}\n\nvar sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference,\n        popper = instance.popper;\n\n    function getReference() {\n      return instance.popperInstance ? instance.popperInstance.state.elements.reference : reference;\n    }\n\n    function shouldCheck(value) {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    var prevRefRect = null;\n    var prevPopRect = null;\n\n    function updatePosition() {\n      var currentRefRect = shouldCheck('reference') ? getReference().getBoundingClientRect() : null;\n      var currentPopRect = shouldCheck('popper') ? popper.getBoundingClientRect() : null;\n\n      if (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect) || currentPopRect && areRectsDifferent(prevPopRect, currentPopRect)) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount: function onMount() {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      }\n    };\n  }\n};\n\nfunction areRectsDifferent(rectA, rectB) {\n  if (rectA && rectB) {\n    return rectA.top !== rectB.top || rectA.right !== rectB.right || rectA.bottom !== rectB.bottom || rectA.left !== rectB.left;\n  }\n\n  return true;\n}\n\ntippy.setDefaultProps({\n  render: render\n});\n\ntippy.setDefaultProps({\r\n    //@ts-ignore\r\n    onShow: instance => {\r\n        if (!instance.props.content)\r\n            return false;\r\n    },\r\n});\r\nconst isComponentInstance = (value) => {\r\n    return value instanceof Object && '$' in value && '$el' in value;\r\n};\r\nfunction useTippy(el, opts = {}, settings = { mount: true, appName: 'Tippy' }) {\r\n    settings = Object.assign({ mount: true, appName: 'Tippy' }, settings);\r\n    const vm = getCurrentInstance();\r\n    const instance = ref();\r\n    const state = ref({\r\n        isEnabled: false,\r\n        isVisible: false,\r\n        isDestroyed: false,\r\n        isMounted: false,\r\n        isShown: false,\r\n    });\r\n    const headlessApp = shallowRef();\r\n    let container = null;\r\n    const getContainer = () => {\r\n        if (container)\r\n            return container;\r\n        container = document.createDocumentFragment();\r\n        return container;\r\n    };\r\n    const getContent = (content) => {\r\n        let newContent;\r\n        let unwrappedContent = isRef(content)\r\n            ? content.value\r\n            : content;\r\n        if (isVNode(unwrappedContent)) {\r\n            if (!headlessApp.value) {\r\n                headlessApp.value = createApp({\r\n                    name: settings.appName,\r\n                    setup: () => {\r\n                        return () => isRef(content) ? content.value : content;\r\n                    },\r\n                });\r\n                if (vm) {\r\n                    Object.assign(headlessApp.value._context, vm.appContext);\r\n                }\r\n                headlessApp.value.mount(getContainer());\r\n            }\r\n            newContent = () => getContainer();\r\n        }\r\n        else if (typeof unwrappedContent === 'object') {\r\n            if (!headlessApp.value) {\r\n                headlessApp.value = createApp({\r\n                    name: settings.appName,\r\n                    setup: () => {\r\n                        return () => h(isRef(content) ? content.value : content);\r\n                    },\r\n                });\r\n                if (vm) {\r\n                    Object.assign(headlessApp.value._context, vm.appContext);\r\n                }\r\n                headlessApp.value.mount(getContainer());\r\n            }\r\n            newContent = () => getContainer();\r\n        }\r\n        else {\r\n            newContent = unwrappedContent;\r\n        }\r\n        return newContent;\r\n    };\r\n    const getProps = (opts) => {\r\n        let options = {};\r\n        if (isRef(opts)) {\r\n            options = opts.value || {};\r\n        }\r\n        else if (isReactive(opts)) {\r\n            options = { ...opts };\r\n        }\r\n        else {\r\n            options = { ...opts };\r\n        }\r\n        if (options.content) {\r\n            options.content = getContent(options.content);\r\n        }\r\n        if (options.triggerTarget) {\r\n            options.triggerTarget = isRef(options.triggerTarget)\r\n                ? options.triggerTarget.value\r\n                : options.triggerTarget;\r\n        }\r\n        if (!options.plugins || !Array.isArray(options.plugins)) {\r\n            options.plugins = [];\r\n        }\r\n        options.plugins = options.plugins.filter((plugin) => plugin.name !== 'vueTippyReactiveState');\r\n        options.plugins.push({\r\n            name: 'vueTippyReactiveState',\r\n            fn: () => {\r\n                return {\r\n                    onCreate() {\r\n                        state.value.isEnabled = true;\r\n                    },\r\n                    onMount() {\r\n                        state.value.isMounted = true;\r\n                    },\r\n                    onShow() {\r\n                        state.value.isMounted = true;\r\n                        state.value.isVisible = true;\r\n                    },\r\n                    onShown() {\r\n                        state.value.isShown = true;\r\n                    },\r\n                    onHide() {\r\n                        state.value.isMounted = false;\r\n                        state.value.isVisible = false;\r\n                    },\r\n                    onHidden() {\r\n                        state.value.isShown = false;\r\n                    },\r\n                    onUnmounted() {\r\n                        state.value.isMounted = false;\r\n                    },\r\n                    onDestroy() {\r\n                        state.value.isDestroyed = true;\r\n                    },\r\n                };\r\n            }\r\n        });\r\n        return options;\r\n    };\r\n    const refresh = () => {\r\n        if (!instance.value)\r\n            return;\r\n        instance.value.setProps(getProps(opts));\r\n    };\r\n    const refreshContent = () => {\r\n        if (!instance.value || !opts.content)\r\n            return;\r\n        instance.value.setContent(getContent(opts.content));\r\n    };\r\n    const setContent = (value) => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.setContent(getContent(value));\r\n    };\r\n    const setProps = (value) => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.setProps(getProps(value));\r\n    };\r\n    const destroy = () => {\r\n        var _a;\r\n        if (instance.value) {\r\n            instance.value.destroy();\r\n            instance.value = undefined;\r\n        }\r\n        container = null;\r\n        (_a = headlessApp.value) === null || _a === void 0 ? void 0 : _a.unmount();\r\n        headlessApp.value = undefined;\r\n    };\r\n    const show = () => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.show();\r\n    };\r\n    const hide = () => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.hide();\r\n    };\r\n    const disable = () => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.disable();\r\n        state.value.isEnabled = false;\r\n    };\r\n    const enable = () => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.enable();\r\n        state.value.isEnabled = true;\r\n    };\r\n    const unmount = () => {\r\n        var _a;\r\n        (_a = instance.value) === null || _a === void 0 ? void 0 : _a.unmount();\r\n    };\r\n    const mount = () => {\r\n        if (!el)\r\n            return;\r\n        let target = isRef(el) ? el.value : el;\r\n        if (typeof target === 'function')\r\n            target = target();\r\n        if (isComponentInstance(target)) {\r\n            target = target.$el;\r\n        }\r\n        if (target) {\r\n            //@ts-ignore\r\n            instance.value = tippy(target, getProps(opts));\r\n            //@ts-ignore\r\n            target.$tippy = response;\r\n        }\r\n    };\r\n    const response = {\r\n        tippy: instance,\r\n        refresh,\r\n        refreshContent,\r\n        setContent,\r\n        setProps,\r\n        destroy,\r\n        hide,\r\n        show,\r\n        disable,\r\n        enable,\r\n        unmount,\r\n        mount,\r\n        state,\r\n    };\r\n    if (settings.mount) {\r\n        if (vm) {\r\n            if (vm.isMounted) {\r\n                mount();\r\n            }\r\n            else {\r\n                onMounted(mount);\r\n            }\r\n        }\r\n        else {\r\n            mount();\r\n        }\r\n    }\r\n    if (vm) {\r\n        onUnmounted(() => {\r\n            destroy();\r\n        });\r\n    }\r\n    if (isRef(opts) || isReactive(opts)) {\r\n        watch(opts, refresh, { immediate: false });\r\n    }\r\n    else if (isRef(opts.content)) {\r\n        watch(opts.content, refreshContent, { immediate: false });\r\n    }\r\n    return response;\r\n}\n\nfunction useTippyComponent(opts = {}, children) {\r\n    const instance = ref();\r\n    return {\r\n        instance,\r\n        TippyComponent: h(TippyComponent, {\r\n            ...opts,\r\n            onVnodeMounted: (vnode) => {\r\n                //@ts-ignore\r\n                instance.value = vnode.component.ctx;\r\n            },\r\n        }, children),\r\n    };\r\n}\n\nfunction useSingleton(instances, optionalProps) {\r\n    const singleton = ref();\r\n    onMounted(() => {\r\n        const pendingTippyInstances = Array.isArray(instances)\r\n            ? instances.map(i => i.value)\r\n            : typeof instances === 'function'\r\n                ? instances()\r\n                : instances.value;\r\n        const tippyInstances = pendingTippyInstances\r\n            .map((instance) => {\r\n            if (instance instanceof Element) {\r\n                //@ts-ignore\r\n                return instance._tippy;\r\n            }\r\n            return instance;\r\n        })\r\n            .filter(Boolean);\r\n        singleton.value = createSingleton(tippyInstances, optionalProps\r\n            ? { allowHTML: true, ...optionalProps }\r\n            : { allowHTML: true });\r\n    });\r\n    return {\r\n        singleton,\r\n    };\r\n}\n\nfunction toValue(r) {\r\n    return typeof r === 'function'\r\n        ? r()\r\n        : unref(r);\r\n}\r\nfunction unrefElement(elRef) {\r\n    var _a, _b;\r\n    const plain = toValue(elRef);\r\n    return (_b = (_a = plain) === null || _a === void 0 ? void 0 : _a.$el) !== null && _b !== void 0 ? _b : plain;\r\n}\r\nconst TippyComponent = defineComponent({\r\n    props: {\r\n        to: {\r\n            type: [String, Function],\r\n        },\r\n        tag: {\r\n            type: [String, Object],\r\n            default: 'span'\r\n        },\r\n        contentTag: {\r\n            type: [String, Object],\r\n            default: 'span'\r\n        },\r\n        contentClass: {\r\n            type: String,\r\n            default: null\r\n        },\r\n        appendTo: { default: () => tippy.defaultProps['appendTo'] },\r\n        aria: { default: () => tippy.defaultProps['aria'] },\r\n        delay: { default: () => tippy.defaultProps['delay'] },\r\n        duration: { default: () => tippy.defaultProps['duration'] },\r\n        getReferenceClientRect: { default: () => tippy.defaultProps['getReferenceClientRect'] },\r\n        hideOnClick: { type: [Boolean, String], default: () => tippy.defaultProps['hideOnClick'] },\r\n        ignoreAttributes: { type: Boolean, default: () => tippy.defaultProps['ignoreAttributes'] },\r\n        interactive: { type: Boolean, default: () => tippy.defaultProps['interactive'] },\r\n        interactiveBorder: { default: () => tippy.defaultProps['interactiveBorder'] },\r\n        interactiveDebounce: { default: () => tippy.defaultProps['interactiveDebounce'] },\r\n        moveTransition: { default: () => tippy.defaultProps['moveTransition'] },\r\n        offset: { default: () => tippy.defaultProps['offset'] },\r\n        onAfterUpdate: { default: () => tippy.defaultProps['onAfterUpdate'] },\r\n        onBeforeUpdate: { default: () => tippy.defaultProps['onBeforeUpdate'] },\r\n        onCreate: { default: () => tippy.defaultProps['onCreate'] },\r\n        onDestroy: { default: () => tippy.defaultProps['onDestroy'] },\r\n        onHidden: { default: () => tippy.defaultProps['onHidden'] },\r\n        onHide: { default: () => tippy.defaultProps['onHide'] },\r\n        onMount: { default: () => tippy.defaultProps['onMount'] },\r\n        onShow: { default: () => tippy.defaultProps['onShow'] },\r\n        onShown: { default: () => tippy.defaultProps['onShown'] },\r\n        onTrigger: { default: () => tippy.defaultProps['onTrigger'] },\r\n        onUntrigger: { default: () => tippy.defaultProps['onUntrigger'] },\r\n        onClickOutside: { default: () => tippy.defaultProps['onClickOutside'] },\r\n        placement: { default: () => tippy.defaultProps['placement'] },\r\n        plugins: { default: () => tippy.defaultProps['plugins'] },\r\n        popperOptions: { default: () => tippy.defaultProps['popperOptions'] },\r\n        render: { default: () => tippy.defaultProps['render'] },\r\n        showOnCreate: { type: Boolean, default: () => tippy.defaultProps['showOnCreate'] },\r\n        touch: { type: [Boolean, String, Array], default: () => tippy.defaultProps['touch'] },\r\n        trigger: { default: () => tippy.defaultProps['trigger'] },\r\n        triggerTarget: { default: () => tippy.defaultProps['triggerTarget'] },\r\n        animateFill: { type: Boolean, default: () => tippy.defaultProps['animateFill'] },\r\n        followCursor: { type: [Boolean, String], default: () => tippy.defaultProps['followCursor'] },\r\n        inlinePositioning: { type: Boolean, default: () => tippy.defaultProps['inlinePositioning'] },\r\n        sticky: { type: [Boolean, String], default: () => tippy.defaultProps['sticky'] },\r\n        allowHTML: { type: Boolean, default: () => tippy.defaultProps['allowHTML'] },\r\n        animation: { default: () => tippy.defaultProps['animation'] },\r\n        arrow: { default: () => tippy.defaultProps['arrow'] },\r\n        content: { default: () => tippy.defaultProps['content'] },\r\n        inertia: { default: () => tippy.defaultProps['inertia'] },\r\n        maxWidth: { default: () => tippy.defaultProps['maxWidth'] },\r\n        role: { default: () => tippy.defaultProps['role'] },\r\n        theme: { default: () => tippy.defaultProps['theme'] },\r\n        zIndex: { default: () => tippy.defaultProps['zIndex'] }\r\n    },\r\n    emits: ['state'],\r\n    setup(props, { slots, emit, expose }) {\r\n        const elem = ref();\r\n        const findParentHelper = ref();\r\n        const contentElem = ref();\r\n        const mounted = ref(false);\r\n        const getOptions = () => {\r\n            let options = { ...props };\r\n            for (const prop of ['to', 'tag', 'contentTag', 'contentClass']) {\r\n                if (options.hasOwnProperty(prop)) {\r\n                    // @ts-ignore\r\n                    delete options[prop];\r\n                }\r\n            }\r\n            return options;\r\n        };\r\n        let target = () => unrefElement(elem);\r\n        if (props.to) {\r\n            if (typeof Element !== 'undefined' && props.to instanceof Element) {\r\n                target = () => props.to;\r\n            }\r\n            else if (props.to === 'parent') {\r\n                target = () => {\r\n                    let el = elem.value;\r\n                    if (!el) {\r\n                        el = elem.value = findParentHelper.value.parentElement;\r\n                    }\r\n                    return el;\r\n                };\r\n            }\r\n            else if (typeof props.to === 'string' || props.to instanceof String) {\r\n                target = () => document.querySelector(props.to);\r\n            }\r\n        }\r\n        const tippy = useTippy(target, getOptions());\r\n        let contentSlot = slots.content;\r\n        if (!contentSlot && props.to === 'parent') {\r\n            contentSlot = slots.default;\r\n        }\r\n        onMounted(() => {\r\n            mounted.value = true;\r\n            nextTick(() => {\r\n                if (contentSlot)\r\n                    tippy.setContent(() => contentElem.value);\r\n            });\r\n        });\r\n        watch(tippy.state, () => {\r\n            emit('state', unref(tippy.state));\r\n        }, { immediate: true, deep: true });\r\n        watch(() => props, () => {\r\n            tippy.setProps(getOptions());\r\n            if (contentSlot)\r\n                tippy.setContent(() => contentElem.value);\r\n        }, { deep: true });\r\n        let exposed = reactive({\r\n            elem,\r\n            contentElem,\r\n            mounted,\r\n            ...tippy\r\n        });\r\n        expose(exposed);\r\n        return () => {\r\n            const contentTag = typeof props.contentTag === 'string' ? props.contentTag : props.contentTag;\r\n            const content = contentSlot\r\n                ? h(contentTag, {\r\n                    ref: contentElem,\r\n                    style: { display: mounted.value ? 'inherit' : 'none' },\r\n                    class: props.contentClass,\r\n                }, contentSlot(exposed))\r\n                : null;\r\n            if (props.to === 'parent') {\r\n                const result = [];\r\n                if (!elem.value) {\r\n                    const findParentHelperNode = h('span', {\r\n                        ref: findParentHelper,\r\n                        'data-v-tippy': '',\r\n                        style: { display: 'none' },\r\n                    });\r\n                    result.push(findParentHelperNode);\r\n                }\r\n                if (content) {\r\n                    result.push(content);\r\n                }\r\n                return result;\r\n            }\r\n            const slot = slots.default ? slots.default(exposed) : [];\r\n            if (!props.tag) {\r\n                const trigger = h(slot[0], {\r\n                    ref: elem, 'data-v-tippy': ''\r\n                });\r\n                return content ? [trigger, content] : trigger;\r\n            }\r\n            const tag = typeof props.tag === 'string' ? props.tag : props.tag;\r\n            return h(tag, { ref: elem, 'data-v-tippy': '' }, content ? [slot, content] : slot);\r\n        };\r\n    },\r\n});\n\nconst booleanProps = [\r\n    'a11y',\r\n    'allowHTML',\r\n    'arrow',\r\n    'flip',\r\n    'flipOnUpdate',\r\n    'hideOnClick',\r\n    'ignoreAttributes',\r\n    'inertia',\r\n    'interactive',\r\n    'lazy',\r\n    'multiple',\r\n    'showOnInit',\r\n    'touch',\r\n    'touchHold',\r\n];\r\nlet props = {};\r\nObject.keys(tippy.defaultProps).forEach((prop) => {\r\n    if (booleanProps.includes(prop)) {\r\n        props[prop] = {\r\n            type: Boolean,\r\n            default: function () {\r\n                return tippy.defaultProps[prop];\r\n            },\r\n        };\r\n    }\r\n    else {\r\n        props[prop] = {\r\n            default: function () {\r\n                return tippy.defaultProps[prop];\r\n            },\r\n        };\r\n    }\r\n});\r\nconst TippySingleton = defineComponent({\r\n    props,\r\n    setup(props) {\r\n        const instances = ref([]);\r\n        const { singleton } = useSingleton(instances, props);\r\n        return { instances, singleton };\r\n    },\r\n    mounted() {\r\n        var _a;\r\n        const parent = this.$el.parentElement;\r\n        const elements = parent.querySelectorAll('[data-v-tippy]');\r\n        this.instances = Array.from(elements)\r\n            .map((el) => el._tippy)\r\n            .filter(Boolean);\r\n        (_a = this.singleton) === null || _a === void 0 ? void 0 : _a.setInstances(this.instances);\r\n    },\r\n    render() {\r\n        let slot = this.$slots.default ? this.$slots.default() : [];\r\n        return h(() => slot);\r\n    },\r\n});\n\nconst directive = {\r\n    mounted(el, binding, vnode) {\r\n        const opts = typeof binding.value === \"string\" ? { content: binding.value } : binding.value || {};\r\n        const modifiers = Object.keys(binding.modifiers || {});\r\n        const placement = modifiers.find(modifier => modifier !== 'arrow');\r\n        const withArrow = modifiers.findIndex(modifier => modifier === 'arrow') !== -1;\r\n        if (placement) {\r\n            opts.placement = opts.placement || placement;\r\n        }\r\n        if (withArrow) {\r\n            opts.arrow = opts.arrow !== undefined ? opts.arrow : true;\r\n        }\r\n        if (vnode.props && vnode.props.onTippyShow) {\r\n            opts.onShow = function (...args) {\r\n                var _a;\r\n                return (_a = vnode.props) === null || _a === void 0 ? void 0 : _a.onTippyShow(...args);\r\n            };\r\n        }\r\n        if (vnode.props && vnode.props.onTippyShown) {\r\n            opts.onShown = function (...args) {\r\n                var _a;\r\n                return (_a = vnode.props) === null || _a === void 0 ? void 0 : _a.onTippyShown(...args);\r\n            };\r\n        }\r\n        if (vnode.props && vnode.props.onTippyHidden) {\r\n            opts.onHidden = function (...args) {\r\n                var _a;\r\n                return (_a = vnode.props) === null || _a === void 0 ? void 0 : _a.onTippyHidden(...args);\r\n            };\r\n        }\r\n        if (vnode.props && vnode.props.onTippyHide) {\r\n            opts.onHide = function (...args) {\r\n                var _a;\r\n                return (_a = vnode.props) === null || _a === void 0 ? void 0 : _a.onTippyHide(...args);\r\n            };\r\n        }\r\n        if (vnode.props && vnode.props.onTippyMount) {\r\n            opts.onMount = function (...args) {\r\n                var _a;\r\n                return (_a = vnode.props) === null || _a === void 0 ? void 0 : _a.onTippyMount(...args);\r\n            };\r\n        }\r\n        if (el.getAttribute('title') && !opts.content) {\r\n            opts.content = el.getAttribute('title');\r\n            el.removeAttribute('title');\r\n        }\r\n        if (el.getAttribute('content') && !opts.content) {\r\n            opts.content = el.getAttribute('content');\r\n        }\r\n        useTippy(el, opts);\r\n    },\r\n    unmounted(el) {\r\n        if (el.$tippy) {\r\n            el.$tippy.destroy();\r\n        }\r\n        else if (el._tippy) {\r\n            el._tippy.destroy();\r\n        }\r\n    },\r\n    updated(el, binding) {\r\n        const opts = typeof binding.value === \"string\" ? { content: binding.value } : binding.value || {};\r\n        if (el.getAttribute('title') && !opts.content) {\r\n            opts.content = el.getAttribute('title');\r\n            el.removeAttribute('title');\r\n        }\r\n        if (el.getAttribute('content') && !opts.content) {\r\n            opts.content = el.getAttribute('content');\r\n        }\r\n        if (el.$tippy) {\r\n            el.$tippy.setProps(opts || {});\r\n        }\r\n        else if (el._tippy) {\r\n            el._tippy.setProps(opts || {});\r\n        }\r\n    },\r\n};\n\nconst plugin = {\r\n    install(app, options = {}) {\r\n        tippy.setDefaultProps(options.defaultProps || {});\r\n        app.directive(options.directive || 'tippy', directive);\r\n        app.component(options.component || 'tippy', TippyComponent);\r\n        app.component(options.componentSingleton || 'tippy-singleton', TippySingleton);\r\n    },\r\n};\n\nconst setDefaultProps$1 = tippy.setDefaultProps;\r\nsetDefaultProps$1({\r\n    ignoreAttributes: true,\r\n    plugins: [sticky, inlinePositioning, followCursor, animateFill],\r\n});\n\nexport default plugin;\nexport { TippyComponent as Tippy, TippySingleton, directive, hideAll, plugin, ROUND_ARROW as roundArrow, setDefaultProps$1 as setDefaultProps, tippy, useSingleton, useTippy, useTippyComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOA,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB,CAAC,KAAK,QAAQ,OAAO,IAAI;AAC9C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAmC,eAAe,OAAO,SAAU,KAAK,WAAW;AACrF,SAAO,IAAI,OAAO,CAAC,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AACpE,GAAG,CAAC,CAAC;AACL,IAAI,aAA0B,CAAC,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,SAAU,KAAK,WAAW;AAC/F,SAAO,IAAI,OAAO,CAAC,WAAW,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AAC/E,GAAG,CAAC,CAAC;AAEL,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB,CAAC,YAAY,MAAM,WAAW,YAAY,MAAM,WAAW,aAAa,OAAO,UAAU;AAE9G,SAAS,YAAY,SAAS;AAC5B,SAAO,WAAW,QAAQ,YAAY,IAAI,YAAY,IAAI;AAC5D;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,MAAM,mBAAmB;AACzC,QAAI,gBAAgB,KAAK;AACzB,WAAO,gBAAgB,cAAc,eAAe,SAAS;AAAA,EAC/D;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,MAAM;AAE1B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAIA,SAAS,YAAY,MAAM;AACzB,MAAI,QAAQ,KAAK;AACjB,SAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,QAAI,QAAQ,MAAM,OAAO,IAAI,KAAK,CAAC;AACnC,QAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,QAAI,UAAU,MAAM,SAAS,IAAI;AAEjC,QAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,IACF;AAKA,WAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAUA,OAAM;AAC9C,UAAI,QAAQ,WAAWA,KAAI;AAE3B,UAAI,UAAU,OAAO;AACnB,gBAAQ,gBAAgBA,KAAI;AAAA,MAC9B,OAAO;AACL,gBAAQ,aAAaA,OAAM,UAAU,OAAO,KAAK,KAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM;AAClB,MAAI,gBAAgB;AAAA,IAClB,QAAQ;AAAA,MACN,UAAU,MAAM,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,WAAW,CAAC;AAAA,EACd;AACA,SAAO,OAAO,MAAM,SAAS,OAAO,OAAO,cAAc,MAAM;AAC/D,QAAM,SAAS;AAEf,MAAI,MAAM,SAAS,OAAO;AACxB,WAAO,OAAO,MAAM,SAAS,MAAM,OAAO,cAAc,KAAK;AAAA,EAC/D;AAEA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,UAAI,UAAU,MAAM,SAAS,IAAI;AACjC,UAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,UAAI,kBAAkB,OAAO,KAAK,MAAM,OAAO,eAAe,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,cAAc,IAAI,CAAC;AAE9G,UAAI,QAAQ,gBAAgB,OAAO,SAAUC,QAAO,UAAU;AAC5D,QAAAA,OAAM,QAAQ,IAAI;AAClB,eAAOA;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,UAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,MACF;AAEA,aAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,WAAW;AACnD,gBAAQ,gBAAgB,SAAS;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,eAAe;AAC5B;AAEA,SAAS,iBAAiB,WAAW;AACnC,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AAEA,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,QAAQ,KAAK;AAEjB,SAAS,sBAAsB,SAAS,cAAc;AACpD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AAEA,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,cAAc,OAAO,KAAK,cAAc;AAC1C,QAAI,eAAe,QAAQ;AAC3B,QAAI,cAAc,QAAQ;AAG1B,QAAI,cAAc,GAAG;AACnB,eAAS,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,IAC9C;AAEA,QAAI,eAAe,GAAG;AACpB,eAAS,MAAM,KAAK,MAAM,IAAI,gBAAgB;AAAA,IAChD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ;AAAA,IACpB,QAAQ,KAAK,SAAS;AAAA,IACtB,KAAK,KAAK,MAAM;AAAA,IAChB,OAAO,KAAK,QAAQ;AAAA,IACpB,QAAQ,KAAK,SAAS;AAAA,IACtB,MAAM,KAAK,OAAO;AAAA,IAClB,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,MAAM;AAAA,EAChB;AACF;AAIA,SAAS,cAAc,SAAS;AAC9B,MAAI,aAAa,sBAAsB,OAAO;AAG9C,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAErB,MAAI,KAAK,IAAI,WAAW,QAAQ,KAAK,KAAK,GAAG;AAC3C,YAAQ,WAAW;AAAA,EACrB;AAEA,MAAI,KAAK,IAAI,WAAW,SAAS,MAAM,KAAK,GAAG;AAC7C,aAAS,WAAW;AAAA,EACtB;AAEA,SAAO;AAAA,IACL,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,QAAQ,OAAO;AAC/B,MAAI,WAAW,MAAM,eAAe,MAAM,YAAY;AAEtD,MAAI,OAAO,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT,WACS,YAAY,aAAa,QAAQ,GAAG;AACzC,QAAI,OAAO;AAEX,OAAG;AACD,UAAI,QAAQ,OAAO,WAAW,IAAI,GAAG;AACnC,eAAO;AAAA,MACT;AAGA,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC,SAAS;AAAA,EACX;AAGF,SAAO;AACT;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,QAAQ,YAAY,OAAO,CAAC,KAAK;AAChE;AAEA,SAAS,mBAAmB,SAAS;AAEnC,WAAS,UAAU,OAAO,IAAI,QAAQ;AAAA;AAAA,IACtC,QAAQ;AAAA,QAAa,OAAO,UAAU;AACxC;AAEA,SAAS,cAAc,SAAS;AAC9B,MAAI,YAAY,OAAO,MAAM,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA;AAAA;AAAA;AAAA;AAAA,IAGE,QAAQ;AAAA,IACR,QAAQ;AAAA,KACR,aAAa,OAAO,IAAI,QAAQ,OAAO;AAAA;AAAA,IAEvC,mBAAmB,OAAO;AAAA;AAG9B;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,CAAC,cAAc,OAAO;AAAA,EAC1B,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ;AACjB;AAIA,SAAS,mBAAmB,SAAS;AACnC,MAAI,YAAY,UAAU,UAAU,YAAY,EAAE,QAAQ,SAAS,MAAM;AACzE,MAAI,OAAO,UAAU,UAAU,QAAQ,SAAS,MAAM;AAEtD,MAAI,QAAQ,cAAc,OAAO,GAAG;AAElC,QAAI,aAAa,iBAAiB,OAAO;AAEzC,QAAI,WAAW,aAAa,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,cAAc,cAAc,OAAO;AAEvC,SAAO,cAAc,WAAW,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,YAAY,WAAW,CAAC,IAAI,GAAG;AAC3F,QAAI,MAAM,iBAAiB,WAAW;AAItC,QAAI,IAAI,cAAc,UAAU,IAAI,gBAAgB,UAAU,IAAI,YAAY,WAAW,CAAC,aAAa,aAAa,EAAE,QAAQ,IAAI,UAAU,MAAM,MAAM,aAAa,IAAI,eAAe,YAAY,aAAa,IAAI,UAAU,IAAI,WAAW,QAAQ;AACpP,aAAO;AAAA,IACT,OAAO;AACL,oBAAc,YAAY;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS;AAChC,MAAIC,UAAS,UAAU,OAAO;AAC9B,MAAI,eAAe,oBAAoB,OAAO;AAE9C,SAAO,gBAAgB,eAAe,YAAY,KAAK,iBAAiB,YAAY,EAAE,aAAa,UAAU;AAC3G,mBAAe,oBAAoB,YAAY;AAAA,EACjD;AAEA,MAAI,iBAAiB,YAAY,YAAY,MAAM,UAAU,YAAY,YAAY,MAAM,UAAU,iBAAiB,YAAY,EAAE,aAAa,WAAW;AAC1J,WAAOA;AAAA,EACT;AAEA,SAAO,gBAAgB,mBAAmB,OAAO,KAAKA;AACxD;AAEA,SAAS,yBAAyB,WAAW;AAC3C,SAAO,CAAC,OAAO,QAAQ,EAAE,QAAQ,SAAS,KAAK,IAAI,MAAM;AAC3D;AAEA,SAAS,OAAO,OAAO,OAAO,OAAO;AACnC,SAAO,IAAI,OAAO,IAAI,OAAO,KAAK,CAAC;AACrC;AACA,SAAS,eAAeC,MAAK,OAAOC,MAAK;AACvC,MAAI,IAAI,OAAOD,MAAK,OAAOC,IAAG;AAC9B,SAAO,IAAIA,OAAMA,OAAM;AACzB;AAEA,SAAS,qBAAqB;AAC5B,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AAEA,SAAS,mBAAmB,eAAe;AACzC,SAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG,aAAa;AAC9D;AAEA,SAAS,gBAAgB,OAAO,MAAM;AACpC,SAAO,KAAK,OAAO,SAAU,SAAS,KAAK;AACzC,YAAQ,GAAG,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,IAAI,kBAAkB,SAASC,iBAAgB,SAAS,OAAO;AAC7D,YAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IAC/E,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,SAAO,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AAC5G;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI;AAEJ,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK,MACZ,UAAU,KAAK;AACnB,MAAI,eAAe,MAAM,SAAS;AAClC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,OAAO,yBAAyB,aAAa;AACjD,MAAI,aAAa,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK;AACzD,MAAI,MAAM,aAAa,WAAW;AAElC,MAAI,CAAC,gBAAgB,CAACA,gBAAe;AACnC;AAAA,EACF;AAEA,MAAI,gBAAgB,gBAAgB,QAAQ,SAAS,KAAK;AAC1D,MAAI,YAAY,cAAc,YAAY;AAC1C,MAAI,UAAU,SAAS,MAAM,MAAM;AACnC,MAAI,UAAU,SAAS,MAAM,SAAS;AACtC,MAAI,UAAU,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM,UAAU,IAAI,IAAIA,eAAc,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG;AACrH,MAAI,YAAYA,eAAc,IAAI,IAAI,MAAM,MAAM,UAAU,IAAI;AAChE,MAAI,oBAAoB,gBAAgB,YAAY;AACpD,MAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,gBAAgB,IAAI,kBAAkB,eAAe,IAAI;AAC/H,MAAI,oBAAoB,UAAU,IAAI,YAAY;AAGlD,MAAIH,OAAM,cAAc,OAAO;AAC/B,MAAIC,OAAM,aAAa,UAAU,GAAG,IAAI,cAAc,OAAO;AAC7D,MAAI,SAAS,aAAa,IAAI,UAAU,GAAG,IAAI,IAAI;AACnD,MAAIG,UAAS,OAAOJ,MAAK,QAAQC,IAAG;AAEpC,MAAI,WAAW;AACf,QAAM,cAAc,IAAI,KAAK,wBAAwB,CAAC,GAAG,sBAAsB,QAAQ,IAAIG,SAAQ,sBAAsB,eAAeA,UAAS,QAAQ;AAC3J;AAEA,SAAS,SAAS,OAAO;AACvB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,wBAAwB;AAEzE,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AAGA,MAAI,OAAO,iBAAiB,UAAU;AACpC,mBAAe,MAAM,SAAS,OAAO,cAAc,YAAY;AAE/D,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM,SAAS,QAAQ,YAAY,GAAG;AAElD;AAAA,EACF;AAEA,QAAM,SAAS,QAAQ;AACzB;AAGA,IAAI,UAAU;AAAA,EACZ,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,QAAQ;AAAA,EACR,UAAU,CAAC,eAAe;AAAA,EAC1B,kBAAkB,CAAC,iBAAiB;AACtC;AAEA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AAEA,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAIA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,IAAI,KAAK,GACT,IAAI,KAAK;AACb,MAAI,MAAM;AACV,MAAI,MAAM,IAAI,oBAAoB;AAClC,SAAO;AAAA,IACL,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,IAC3B,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,EAC7B;AACF;AAEA,SAAS,YAAY,OAAO;AAC1B,MAAI;AAEJ,MAAIC,UAAS,MAAM,QACf,aAAa,MAAM,YACnB,YAAY,MAAM,WAClB,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,kBAAkB,MAAM,iBACxB,WAAW,MAAM,UACjB,eAAe,MAAM,cACrB,UAAU,MAAM;AAEpB,MAAI,QAAQ,iBAAiB,OAAO,kBAAkB,OAAO,IAAI,OAAO,iBAAiB,aAAa,aAAa,OAAO,IAAI,SAC1H,UAAU,MAAM,GAChB,IAAI,YAAY,SAAS,IAAI,SAC7B,UAAU,MAAM,GAChB,IAAI,YAAY,SAAS,IAAI;AAEjC,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,UAAU;AACZ,QAAI,eAAe,gBAAgBA,OAAM;AACzC,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,iBAAiB,UAAUA,OAAM,GAAG;AACtC,qBAAe,mBAAmBA,OAAM;AAExC,UAAI,iBAAiB,YAAY,EAAE,aAAa,YAAY,aAAa,YAAY;AACnF,qBAAa;AACb,oBAAY;AAAA,MACd;AAAA,IACF;AAGA,mBAAe;AAEf,QAAI,cAAc,QAAQ,cAAc,QAAQ,cAAc,UAAU,cAAc,KAAK;AACzF,cAAQ;AACR,UAAI,UAAU,WAAW,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACjE,aAAa,UAAU;AAAA;AACvB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAEA,QAAI,cAAc,SAAS,cAAc,OAAO,cAAc,WAAW,cAAc,KAAK;AAC1F,cAAQ;AACR,UAAI,UAAU,WAAW,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACjE,aAAa,SAAS;AAAA;AACtB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,eAAe,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF,GAAG,YAAY,UAAU;AAEzB,MAAI,iBAAiB;AACnB,QAAI;AAEJ,WAAO,OAAO,OAAO,CAAC,GAAG,eAAe,iBAAiB,CAAC,GAAG,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,aAAa,IAAI,oBAAoB,MAAM,IAAI,eAAe,IAAI,SAAS,IAAI,QAAQ,iBAAiB,IAAI,SAAS,IAAI,UAAU,eAAe;AAAA,EAClT;AAEA,SAAO,OAAO,OAAO,CAAC,GAAG,eAAe,kBAAkB,CAAC,GAAG,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,YAAY,IAAI,gBAAgB;AAC9M;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,wBAAwB,QAAQ,iBAChC,kBAAkB,0BAA0B,SAAS,OAAO,uBAC5D,oBAAoB,QAAQ,UAC5B,WAAW,sBAAsB,SAAS,OAAO,mBACjD,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,OAAO;AAE7D,MAAI,eAAe;AAAA,IACjB,WAAW,iBAAiB,MAAM,SAAS;AAAA,IAC3C,WAAW,aAAa,MAAM,SAAS;AAAA,IACvC,QAAQ,MAAM,SAAS;AAAA,IACvB,YAAY,MAAM,MAAM;AAAA,IACxB;AAAA,IACA,SAAS,MAAM,QAAQ,aAAa;AAAA,EACtC;AAEA,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,OAAO,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACvG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU,MAAM,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,MAAI,MAAM,cAAc,SAAS,MAAM;AACrC,UAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,OAAO,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACrG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,yBAAyB,MAAM;AAAA,EACjC,CAAC;AACH;AAGA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;AAEA,IAAI,UAAU;AAAA,EACZ,SAAS;AACX;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI,QAAQ,KAAK,OACb,WAAW,KAAK,UAChB,UAAU,KAAK;AACnB,MAAI,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO;AACjD,MAAIN,UAAS,UAAU,MAAM,SAAS,MAAM;AAC5C,MAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM,cAAc,WAAW,MAAM,cAAc,MAAM;AAEvF,MAAI,QAAQ;AACV,kBAAc,QAAQ,SAAU,cAAc;AAC5C,mBAAa,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAClE,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ;AACV,IAAAA,QAAO,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,EAC5D;AAEA,SAAO,WAAY;AACjB,QAAI,QAAQ;AACV,oBAAc,QAAQ,SAAU,cAAc;AAC5C,qBAAa,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,MACrE,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ;AACV,MAAAA,QAAO,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC/D;AAAA,EACF;AACF;AAGA,IAAI,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI,SAAS,KAAK;AAAA,EAAC;AAAA,EACnB,QAAQ;AAAA,EACR,MAAM,CAAC;AACT;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,SAAU,SAAS;AACpE,WAAO,KAAK,OAAO;AAAA,EACrB,CAAC;AACH;AAEA,IAAI,SAAS;AAAA,EACX,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,SAAU,SAAS;AACxD,WAAO,OAAO,OAAO;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,MAAM,UAAU,IAAI;AACxB,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,SAAS;AAQpC,SAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO,gBAAgB,OAAO,EAAE;AAC5F;AAEA,SAAS,gBAAgB,SAAS;AAChC,MAAI,MAAM,UAAU,OAAO;AAC3B,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,iBAAiB,IAAI;AACzB,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AAMR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AASxB,QAAI,CAAC,iCAAiC,KAAK,UAAU,SAAS,GAAG;AAC/D,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG,IAAI,oBAAoB,OAAO;AAAA,IAClC;AAAA,EACF;AACF;AAIA,SAAS,gBAAgB,SAAS;AAChC,MAAI;AAEJ,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,YAAY,gBAAgB,OAAO;AACvC,MAAI,QAAQ,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACpG,MAAI,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,cAAc,CAAC;AAC5G,MAAI,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,OAAO,KAAK,eAAe,GAAG,OAAO,KAAK,eAAe,CAAC;AACjH,MAAI,IAAI,CAAC,UAAU,aAAa,oBAAoB,OAAO;AAC3D,MAAI,IAAI,CAAC,UAAU;AAEnB,MAAI,iBAAiB,QAAQ,IAAI,EAAE,cAAc,OAAO;AACtD,SAAK,IAAI,KAAK,aAAa,OAAO,KAAK,cAAc,CAAC,IAAI;AAAA,EAC5D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,SAAS;AAE/B,MAAI,oBAAoB,iBAAiB,OAAO,GAC5C,WAAW,kBAAkB,UAC7B,YAAY,kBAAkB,WAC9B,YAAY,kBAAkB;AAElC,SAAO,6BAA6B,KAAK,WAAW,YAAY,SAAS;AAC3E;AAEA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,QAAQ,QAAQ,WAAW,EAAE,QAAQ,YAAY,IAAI,CAAC,KAAK,GAAG;AAEjE,WAAO,KAAK,cAAc;AAAA,EAC5B;AAEA,MAAI,cAAc,IAAI,KAAK,eAAe,IAAI,GAAG;AAC/C,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,cAAc,IAAI,CAAC;AAC5C;AASA,SAAS,kBAAkB,SAAS,MAAM;AACxC,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,eAAe,gBAAgB,OAAO;AAC1C,MAAI,SAAS,mBAAmB,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACxH,MAAI,MAAM,UAAU,YAAY;AAChC,MAAI,SAAS,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,kBAAkB,CAAC,GAAG,eAAe,YAAY,IAAI,eAAe,CAAC,CAAC,IAAI;AACjH,MAAI,cAAc,KAAK,OAAO,MAAM;AACpC,SAAO,SAAS;AAAA;AAAA,IAChB,YAAY,OAAO,kBAAkB,cAAc,MAAM,CAAC,CAAC;AAAA;AAC7D;AAEA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,IAC7B,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,OAAO,KAAK,IAAI,KAAK;AAAA,IACrB,QAAQ,KAAK,IAAI,KAAK;AAAA,EACxB,CAAC;AACH;AAEA,SAAS,2BAA2B,SAAS;AAC3C,MAAI,OAAO,sBAAsB,OAAO;AACxC,OAAK,MAAM,KAAK,MAAM,QAAQ;AAC9B,OAAK,OAAO,KAAK,OAAO,QAAQ;AAChC,OAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,OAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,SAAO;AACT;AAEA,SAAS,2BAA2B,SAAS,gBAAgB;AAC3D,SAAO,mBAAmB,WAAW,iBAAiB,gBAAgB,OAAO,CAAC,IAAI,UAAU,cAAc,IAAI,2BAA2B,cAAc,IAAI,iBAAiB,gBAAgB,mBAAmB,OAAO,CAAC,CAAC;AAC1N;AAKA,SAAS,mBAAmB,SAAS;AACnC,MAAIO,mBAAkB,kBAAkB,cAAc,OAAO,CAAC;AAC9D,MAAI,oBAAoB,CAAC,YAAY,OAAO,EAAE,QAAQ,iBAAiB,OAAO,EAAE,QAAQ,KAAK;AAC7F,MAAI,iBAAiB,qBAAqB,cAAc,OAAO,IAAI,gBAAgB,OAAO,IAAI;AAE9F,MAAI,CAAC,UAAU,cAAc,GAAG;AAC9B,WAAO,CAAC;AAAA,EACV;AAGA,SAAOA,iBAAgB,OAAO,SAAU,gBAAgB;AACtD,WAAO,UAAU,cAAc,KAAK,SAAS,gBAAgB,cAAc,KAAK,YAAY,cAAc,MAAM,WAAW,oBAAoB,iBAAiB,cAAc,EAAE,aAAa,WAAW;AAAA,EAC1M,CAAC;AACH;AAIA,SAAS,gBAAgB,SAAS,UAAU,cAAc;AACxD,MAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,OAAO,IAAI,CAAC,EAAE,OAAO,QAAQ;AAC3G,MAAIA,mBAAkB,CAAC,EAAE,OAAO,qBAAqB,CAAC,YAAY,CAAC;AACnE,MAAI,sBAAsBA,iBAAgB,CAAC;AAC3C,MAAI,eAAeA,iBAAgB,OAAO,SAAU,SAAS,gBAAgB;AAC3E,QAAI,OAAO,2BAA2B,SAAS,cAAc;AAC7D,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,2BAA2B,SAAS,mBAAmB,CAAC;AAC3D,eAAa,QAAQ,aAAa,QAAQ,aAAa;AACvD,eAAa,SAAS,aAAa,SAAS,aAAa;AACzD,eAAa,IAAI,aAAa;AAC9B,eAAa,IAAI,aAAa;AAC9B,SAAO;AACT;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAIC,aAAY,KAAK,WACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACrB,MAAI,gBAAgB,YAAY,iBAAiB,SAAS,IAAI;AAC9D,MAAI,YAAY,YAAY,aAAa,SAAS,IAAI;AACtD,MAAI,UAAUA,WAAU,IAAIA,WAAU,QAAQ,IAAI,QAAQ,QAAQ;AAClE,MAAI,UAAUA,WAAU,IAAIA,WAAU,SAAS,IAAI,QAAQ,SAAS;AACpE,MAAI;AAEJ,UAAQ,eAAe;AAAA,IACrB,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAI,QAAQ;AAAA,MAC3B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAIA,WAAU;AAAA,MAC7B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAIA,WAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAI,QAAQ;AAAA,QACzB,GAAG;AAAA,MACL;AACA;AAAA,IAEF;AACE,gBAAU;AAAA,QACR,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,MACf;AAAA,EACJ;AAEA,MAAI,WAAW,gBAAgB,yBAAyB,aAAa,IAAI;AAEzE,MAAI,YAAY,MAAM;AACpB,QAAI,MAAM,aAAa,MAAM,WAAW;AAExC,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,OAAO,SAAS;AACtC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,qBAAqB,SAAS,WAC9B,YAAY,uBAAuB,SAAS,MAAM,YAAY,oBAC9D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,kBAAkB,mBAC5D,wBAAwB,SAAS,cACjC,eAAe,0BAA0B,SAAS,WAAW,uBAC7D,wBAAwB,SAAS,gBACjC,iBAAiB,0BAA0B,SAAS,SAAS,uBAC7D,uBAAuB,SAAS,aAChC,cAAc,yBAAyB,SAAS,QAAQ,sBACxD,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,IAAI;AAChD,MAAI,gBAAgB,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AACvH,MAAI,aAAa,mBAAmB,SAAS,YAAY;AACzD,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,UAAU,MAAM,SAAS,cAAc,aAAa,cAAc;AACtE,MAAI,qBAAqB,gBAAgB,UAAU,OAAO,IAAI,UAAU,QAAQ,kBAAkB,mBAAmB,MAAM,SAAS,MAAM,GAAG,UAAU,YAAY;AACnK,MAAI,sBAAsB,sBAAsB,MAAM,SAAS,SAAS;AACxE,MAAIJ,iBAAgB,eAAe;AAAA,IACjC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,iBAAiB,OAAO,OAAO,CAAC,GAAG,YAAYA,cAAa,CAAC;AACpF,MAAI,oBAAoB,mBAAmB,SAAS,mBAAmB;AAGvE,MAAI,kBAAkB;AAAA,IACpB,KAAK,mBAAmB,MAAM,kBAAkB,MAAM,cAAc;AAAA,IACpE,QAAQ,kBAAkB,SAAS,mBAAmB,SAAS,cAAc;AAAA,IAC7E,MAAM,mBAAmB,OAAO,kBAAkB,OAAO,cAAc;AAAA,IACvE,OAAO,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,MAAM,cAAc;AAErC,MAAI,mBAAmB,UAAU,YAAY;AAC3C,QAAIC,UAAS,WAAW,SAAS;AACjC,WAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,KAAK;AAClD,UAAI,WAAW,CAAC,OAAO,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,IAAI;AACvD,UAAI,OAAO,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,MAAM;AACnD,sBAAgB,GAAG,KAAKA,QAAO,IAAI,IAAI;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,OAAO,SAAS;AAC5C,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,YAAY,SAAS,WACrB,WAAW,SAAS,UACpB,eAAe,SAAS,cACxB,UAAU,SAAS,SACnB,iBAAiB,SAAS,gBAC1B,wBAAwB,SAAS,uBACjC,wBAAwB,0BAA0B,SAAS,aAAa;AAC5E,MAAI,YAAY,aAAa,SAAS;AACtC,MAAI,eAAe,YAAY,iBAAiB,sBAAsB,oBAAoB,OAAO,SAAUI,YAAW;AACpH,WAAO,aAAaA,UAAS,MAAM;AAAA,EACrC,CAAC,IAAI;AACL,MAAI,oBAAoB,aAAa,OAAO,SAAUA,YAAW;AAC/D,WAAO,sBAAsB,QAAQA,UAAS,KAAK;AAAA,EACrD,CAAC;AAED,MAAI,kBAAkB,WAAW,GAAG;AAClC,wBAAoB;AAAA,EACtB;AAGA,MAAI,YAAY,kBAAkB,OAAO,SAAU,KAAKA,YAAW;AACjE,QAAIA,UAAS,IAAI,eAAe,OAAO;AAAA,MACrC,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,iBAAiBA,UAAS,CAAC;AAC9B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,KAAK,SAAS,EAAE,KAAK,SAAU,GAAG,GAAG;AACjD,WAAO,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,EACnC,CAAC;AACH;AAEA,SAAS,8BAA8B,WAAW;AAChD,MAAI,iBAAiB,SAAS,MAAM,MAAM;AACxC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,oBAAoB,qBAAqB,SAAS;AACtD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAEhB,MAAI,MAAM,cAAc,IAAI,EAAE,OAAO;AACnC;AAAA,EACF;AAEA,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,OAAO,kBACpD,8BAA8B,QAAQ,oBACtC,UAAU,QAAQ,SAClB,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,wBAAwB,QAAQ,gBAChC,iBAAiB,0BAA0B,SAAS,OAAO,uBAC3D,wBAAwB,QAAQ;AACpC,MAAI,qBAAqB,MAAM,QAAQ;AACvC,MAAI,gBAAgB,iBAAiB,kBAAkB;AACvD,MAAI,kBAAkB,kBAAkB;AACxC,MAAI,qBAAqB,gCAAgC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,kBAAkB,CAAC,IAAI,8BAA8B,kBAAkB;AAC3L,MAAIC,cAAa,CAAC,kBAAkB,EAAE,OAAO,kBAAkB,EAAE,OAAO,SAAU,KAAKD,YAAW;AAChG,WAAO,IAAI,OAAO,iBAAiBA,UAAS,MAAM,OAAO,qBAAqB,OAAO;AAAA,MACnF,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAIA,UAAS;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,qBAAqB;AACzB,MAAI,wBAAwBC,YAAW,CAAC;AAExC,WAAS,IAAI,GAAG,IAAIA,YAAW,QAAQ,KAAK;AAC1C,QAAI,YAAYA,YAAW,CAAC;AAE5B,QAAI,iBAAiB,iBAAiB,SAAS;AAE/C,QAAI,mBAAmB,aAAa,SAAS,MAAM;AACnD,QAAI,aAAa,CAAC,KAAK,MAAM,EAAE,QAAQ,cAAc,KAAK;AAC1D,QAAI,MAAM,aAAa,UAAU;AACjC,QAAI,WAAW,eAAe,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,oBAAoB,aAAa,mBAAmB,QAAQ,OAAO,mBAAmB,SAAS;AAEnG,QAAI,cAAc,GAAG,IAAI,WAAW,GAAG,GAAG;AACxC,0BAAoB,qBAAqB,iBAAiB;AAAA,IAC5D;AAEA,QAAI,mBAAmB,qBAAqB,iBAAiB;AAC7D,QAAI,SAAS,CAAC;AAEd,QAAI,eAAe;AACjB,aAAO,KAAK,SAAS,cAAc,KAAK,CAAC;AAAA,IAC3C;AAEA,QAAI,cAAc;AAChB,aAAO,KAAK,SAAS,iBAAiB,KAAK,GAAG,SAAS,gBAAgB,KAAK,CAAC;AAAA,IAC/E;AAEA,QAAI,OAAO,MAAM,SAAU,OAAO;AAChC,aAAO;AAAA,IACT,CAAC,GAAG;AACF,8BAAwB;AACxB,2BAAqB;AACrB;AAAA,IACF;AAEA,cAAU,IAAI,WAAW,MAAM;AAAA,EACjC;AAEA,MAAI,oBAAoB;AAEtB,QAAI,iBAAiB,iBAAiB,IAAI;AAE1C,QAAI,QAAQ,SAASC,OAAMC,KAAI;AAC7B,UAAI,mBAAmBF,YAAW,KAAK,SAAUD,YAAW;AAC1D,YAAII,UAAS,UAAU,IAAIJ,UAAS;AAEpC,YAAII,SAAQ;AACV,iBAAOA,QAAO,MAAM,GAAGD,GAAE,EAAE,MAAM,SAAU,OAAO;AAChD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,kBAAkB;AACpB,gCAAwB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,KAAK,gBAAgB,KAAK,GAAG,MAAM;AAC1C,UAAI,OAAO,MAAM,EAAE;AAEnB,UAAI,SAAS,QAAS;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,MAAM,cAAc,uBAAuB;AAC7C,UAAM,cAAc,IAAI,EAAE,QAAQ;AAClC,UAAM,YAAY;AAClB,UAAM,QAAQ;AAAA,EAChB;AACF;AAGA,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAAA,EAC3B,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,UAAU,MAAM,kBAAkB;AACxD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK,SAAS,iBAAiB;AAAA,IACnD,OAAO,SAAS,QAAQ,KAAK,QAAQ,iBAAiB;AAAA,IACtD,QAAQ,SAAS,SAAS,KAAK,SAAS,iBAAiB;AAAA,IACzD,MAAM,SAAS,OAAO,KAAK,QAAQ,iBAAiB;AAAA,EACtD;AACF;AAEA,SAAS,sBAAsB,UAAU;AACvC,SAAO,CAAC,KAAK,OAAO,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrD,WAAO,SAAS,IAAI,KAAK;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAChB,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,mBAAmB,MAAM,cAAc;AAC3C,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,aAAa;AAAA,EACf,CAAC;AACD,MAAI,2BAA2B,eAAe,mBAAmB,aAAa;AAC9E,MAAI,sBAAsB,eAAe,mBAAmB,YAAY,gBAAgB;AACxF,MAAI,oBAAoB,sBAAsB,wBAAwB;AACtE,MAAI,mBAAmB,sBAAsB,mBAAmB;AAChE,QAAM,cAAc,IAAI,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,gCAAgC;AAAA,IAChC,uBAAuB;AAAA,EACzB,CAAC;AACH;AAGA,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,kBAAkB,CAAC,iBAAiB;AAAA,EACpC,IAAI;AACN;AAEA,SAAS,wBAAwB,WAAW,OAAOP,SAAQ;AACzD,MAAI,gBAAgB,iBAAiB,SAAS;AAC9C,MAAI,iBAAiB,CAAC,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI,KAAK;AAEpE,MAAI,OAAO,OAAOA,YAAW,aAAaA,QAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IACxE;AAAA,EACF,CAAC,CAAC,IAAIA,SACF,WAAW,KAAK,CAAC,GACjB,WAAW,KAAK,CAAC;AAErB,aAAW,YAAY;AACvB,cAAY,YAAY,KAAK;AAC7B,SAAO,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK,IAAI;AAAA,IACjD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM,SAChB,OAAO,MAAM;AACjB,MAAI,kBAAkB,QAAQ,QAC1BA,UAAS,oBAAoB,SAAS,CAAC,GAAG,CAAC,IAAI;AACnD,MAAI,OAAO,WAAW,OAAO,SAAU,KAAK,WAAW;AACrD,QAAI,SAAS,IAAI,wBAAwB,WAAW,MAAM,OAAOA,OAAM;AACvE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,wBAAwB,KAAK,MAAM,SAAS,GAC5C,IAAI,sBAAsB,GAC1B,IAAI,sBAAsB;AAE9B,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,cAAc,cAAc,KAAK;AACvC,UAAM,cAAc,cAAc,KAAK;AAAA,EACzC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU,CAAC,eAAe;AAAA,EAC1B,IAAI;AACN;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAKhB,QAAM,cAAc,IAAI,IAAI,eAAe;AAAA,IACzC,WAAW,MAAM,MAAM;AAAA,IACvB,SAAS,MAAM,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW,MAAM;AAAA,EACnB,CAAC;AACH;AAGA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;AAEA,SAAS,WAAW,MAAM;AACxB,SAAO,SAAS,MAAM,MAAM;AAC9B;AAEA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAChB,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,QAAQ,kBACrD,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,UAAU,QAAQ,SAClB,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,IAAI;AAC1D,MAAI,WAAW,eAAe,OAAO;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,YAAY,aAAa,MAAM,SAAS;AAC5C,MAAI,kBAAkB,CAAC;AACvB,MAAI,WAAW,yBAAyB,aAAa;AACrD,MAAI,UAAU,WAAW,QAAQ;AACjC,MAAID,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IACvG,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,MAAI,8BAA8B,OAAO,sBAAsB,WAAW;AAAA,IACxE,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI,OAAO,OAAO;AAAA,IAChB,UAAU;AAAA,IACV,SAAS;AAAA,EACX,GAAG,iBAAiB;AACpB,MAAI,sBAAsB,MAAM,cAAc,SAAS,MAAM,cAAc,OAAO,MAAM,SAAS,IAAI;AACrG,MAAI,OAAO;AAAA,IACT,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,CAACA,gBAAe;AAClB;AAAA,EACF;AAEA,MAAI,eAAe;AACjB,QAAI;AAEJ,QAAI,WAAW,aAAa,MAAM,MAAM;AACxC,QAAI,UAAU,aAAa,MAAM,SAAS;AAC1C,QAAI,MAAM,aAAa,MAAM,WAAW;AACxC,QAAIC,UAASD,eAAc,QAAQ;AACnC,QAAI,QAAQC,UAAS,SAAS,QAAQ;AACtC,QAAI,QAAQA,UAAS,SAAS,OAAO;AACrC,QAAI,WAAW,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI;AAC/C,QAAI,SAAS,cAAc,QAAQ,cAAc,GAAG,IAAI,WAAW,GAAG;AACtE,QAAI,SAAS,cAAc,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG;AAGxE,QAAI,eAAe,MAAM,SAAS;AAClC,QAAI,YAAY,UAAU,eAAe,cAAc,YAAY,IAAI;AAAA,MACrE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,QAAI,qBAAqB,MAAM,cAAc,kBAAkB,IAAI,MAAM,cAAc,kBAAkB,EAAE,UAAU,mBAAmB;AACxI,QAAI,kBAAkB,mBAAmB,QAAQ;AACjD,QAAI,kBAAkB,mBAAmB,OAAO;AAMhD,QAAI,WAAW,OAAO,GAAG,cAAc,GAAG,GAAG,UAAU,GAAG,CAAC;AAC3D,QAAI,YAAY,kBAAkB,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC5M,QAAI,YAAY,kBAAkB,CAAC,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC7M,QAAI,oBAAoB,MAAM,SAAS,SAAS,gBAAgB,MAAM,SAAS,KAAK;AACpF,QAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,aAAa,IAAI,kBAAkB,cAAc,IAAI;AACjI,QAAI,uBAAuB,wBAAwB,uBAAuB,OAAO,SAAS,oBAAoB,QAAQ,MAAM,OAAO,wBAAwB;AAC3J,QAAI,YAAYA,UAAS,YAAY,sBAAsB;AAC3D,QAAI,YAAYA,UAAS,YAAY;AACrC,QAAI,kBAAkB,OAAO,SAAS,IAAI,OAAO,SAAS,IAAI,OAAOA,SAAQ,SAAS,IAAI,OAAO,SAAS,IAAI,KAAK;AACnH,IAAAD,eAAc,QAAQ,IAAI;AAC1B,SAAK,QAAQ,IAAI,kBAAkBC;AAAA,EACrC;AAEA,MAAI,cAAc;AAChB,QAAI;AAEJ,QAAI,YAAY,aAAa,MAAM,MAAM;AAEzC,QAAI,WAAW,aAAa,MAAM,SAAS;AAE3C,QAAI,UAAUD,eAAc,OAAO;AAEnC,QAAI,OAAO,YAAY,MAAM,WAAW;AAExC,QAAI,OAAO,UAAU,SAAS,SAAS;AAEvC,QAAI,OAAO,UAAU,SAAS,QAAQ;AAEtC,QAAI,eAAe,CAAC,KAAK,IAAI,EAAE,QAAQ,aAAa,MAAM;AAE1D,QAAI,wBAAwB,yBAAyB,uBAAuB,OAAO,SAAS,oBAAoB,OAAO,MAAM,OAAO,yBAAyB;AAE7J,QAAI,aAAa,eAAe,OAAO,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B;AAE7I,QAAI,aAAa,eAAe,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B,UAAU;AAEhJ,QAAI,mBAAmB,UAAU,eAAe,eAAe,YAAY,SAAS,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa,IAAI;AAExK,IAAAA,eAAc,OAAO,IAAI;AACzB,SAAK,OAAO,IAAI,mBAAmB;AAAA,EACrC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAC7B;AAEA,SAAS,qBAAqB,SAAS;AACrC,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,SAAS,UAAU,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AACpD,WAAO,gBAAgB,IAAI;AAAA,EAC7B,OAAO;AACL,WAAO,qBAAqB,IAAI;AAAA,EAClC;AACF;AAEA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,eAAe;AACxD,MAAI,SAAS,MAAM,KAAK,MAAM,IAAI,QAAQ,gBAAgB;AAC1D,SAAO,WAAW,KAAK,WAAW;AACpC;AAIA,SAAS,iBAAiB,yBAAyB,cAAc,SAAS;AACxE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAI,0BAA0B,cAAc,YAAY;AACxD,MAAI,uBAAuB,cAAc,YAAY,KAAK,gBAAgB,YAAY;AACtF,MAAI,kBAAkB,mBAAmB,YAAY;AACrD,MAAI,OAAO,sBAAsB,yBAAyB,oBAAoB;AAC9E,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM;AAAA,IAClC,eAAe,eAAe,GAAG;AAC/B,eAAS,cAAc,YAAY;AAAA,IACrC;AAEA,QAAI,cAAc,YAAY,GAAG;AAC/B,gBAAU,sBAAsB,cAAc,IAAI;AAClD,cAAQ,KAAK,aAAa;AAC1B,cAAQ,KAAK,aAAa;AAAA,IAC5B,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,OAAO,aAAa,QAAQ;AAAA,IAC3C,GAAG,KAAK,MAAM,OAAO,YAAY,QAAQ;AAAA,IACzC,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,MAAM,WAAW;AACxB,MAAI,MAAM,oBAAI,IAAI;AAClB,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,SAAS,CAAC;AACd,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,IAAI,SAAS,MAAM,QAAQ;AAAA,EACjC,CAAC;AAED,WAAS,KAAK,UAAU;AACtB,YAAQ,IAAI,SAAS,IAAI;AACzB,QAAI,WAAW,CAAC,EAAE,OAAO,SAAS,YAAY,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC;AACjF,aAAS,QAAQ,SAAU,KAAK;AAC9B,UAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACrB,YAAI,cAAc,IAAI,IAAI,GAAG;AAE7B,YAAI,aAAa;AACf,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,GAAG;AAE/B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,WAAW;AAEjC,MAAI,mBAAmB,MAAM,SAAS;AAEtC,SAAO,eAAe,OAAO,SAAU,KAAK,OAAO;AACjD,WAAO,IAAI,OAAO,iBAAiB,OAAO,SAAU,UAAU;AAC5D,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,SAASU,KAAI;AACpB,MAAI;AACJ,SAAO,WAAY;AACjB,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,QAAQ,SAAU,SAAS;AACvC,gBAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,oBAAU;AACV,kBAAQA,IAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,WAAW;AAC9B,MAAI,SAAS,UAAU,OAAO,SAAUC,SAAQ,SAAS;AACvD,QAAI,WAAWA,QAAO,QAAQ,IAAI;AAClC,IAAAA,QAAO,QAAQ,IAAI,IAAI,WAAW,OAAO,OAAO,CAAC,GAAG,UAAU,SAAS;AAAA,MACrE,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS,SAAS,QAAQ,OAAO;AAAA,MAC5D,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,MAAM,QAAQ,IAAI;AAAA,IACrD,CAAC,IAAI;AACL,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAC5C,WAAO,OAAO,GAAG;AAAA,EACnB,CAAC;AACH;AAEA,IAAI,kBAAkB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW,CAAC;AAAA,EACZ,UAAU;AACZ;AAEA,SAAS,mBAAmB;AAC1B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,SAAO,CAAC,KAAK,KAAK,SAAU,SAAS;AACnC,WAAO,EAAE,WAAW,OAAO,QAAQ,0BAA0B;AAAA,EAC/D,CAAC;AACH;AAEA,SAAS,gBAAgB,kBAAkB;AACzC,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB,CAAC;AAAA,EACtB;AAEA,MAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,kBAC1CC,oBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,yBAAyB,kBAAkB,gBAC3C,iBAAiB,2BAA2B,SAAS,kBAAkB;AAC3E,SAAO,SAASC,cAAaT,YAAWF,SAAQ,SAAS;AACvD,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ;AAAA,MACV,WAAW;AAAA,MACX,kBAAkB,CAAC;AAAA,MACnB,SAAS,OAAO,OAAO,CAAC,GAAG,iBAAiB,cAAc;AAAA,MAC1D,eAAe,CAAC;AAAA,MAChB,UAAU;AAAA,QACR,WAAWE;AAAA,QACX,QAAQF;AAAA,MACV;AAAA,MACA,YAAY,CAAC;AAAA,MACb,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,cAAc;AAClB,QAAI,WAAW;AAAA,MACb;AAAA,MACA,YAAY,SAAS,WAAW,kBAAkB;AAChD,YAAIY,WAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;AACzF,+BAAuB;AACvB,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,MAAM,SAASA,QAAO;AACxE,cAAM,gBAAgB;AAAA,UACpB,WAAW,UAAUV,UAAS,IAAI,kBAAkBA,UAAS,IAAIA,WAAU,iBAAiB,kBAAkBA,WAAU,cAAc,IAAI,CAAC;AAAA,UAC3I,QAAQ,kBAAkBF,OAAM;AAAA,QAClC;AAGA,YAAI,mBAAmB,eAAe,YAAY,CAAC,EAAE,OAAOU,mBAAkB,MAAM,QAAQ,SAAS,CAAC,CAAC;AAEvG,cAAM,mBAAmB,iBAAiB,OAAO,SAAU,GAAG;AAC5D,iBAAO,EAAE;AAAA,QACX,CAAC;AAED,2BAAmB;AACnB,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,SAAS,cAAc;AAClC,YAAI,aAAa;AACf;AAAA,QACF;AAEA,YAAI,kBAAkB,MAAM,UACxBR,aAAY,gBAAgB,WAC5BF,UAAS,gBAAgB;AAG7B,YAAI,CAAC,iBAAiBE,YAAWF,OAAM,GAAG;AAExC;AAAA,QACF;AAGA,cAAM,QAAQ;AAAA,UACZ,WAAW,iBAAiBE,YAAW,gBAAgBF,OAAM,GAAG,MAAM,QAAQ,aAAa,OAAO;AAAA,UAClG,QAAQ,cAAcA,OAAM;AAAA,QAC9B;AAMA,cAAM,QAAQ;AACd,cAAM,YAAY,MAAM,QAAQ;AAKhC,cAAM,iBAAiB,QAAQ,SAAU,UAAU;AACjD,iBAAO,MAAM,cAAc,SAAS,IAAI,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,QAC7E,CAAC;AAED,iBAAS,QAAQ,GAAG,QAAQ,MAAM,iBAAiB,QAAQ,SAAS;AAElE,cAAI,MAAM,UAAU,MAAM;AACxB,kBAAM,QAAQ;AACd,oBAAQ;AACR;AAAA,UACF;AAEA,cAAI,wBAAwB,MAAM,iBAAiB,KAAK,GACpDQ,MAAK,sBAAsB,IAC3B,yBAAyB,sBAAsB,SAC/C,WAAW,2BAA2B,SAAS,CAAC,IAAI,wBACpD,OAAO,sBAAsB;AAEjC,cAAI,OAAOA,QAAO,YAAY;AAC5B,oBAAQA,IAAG;AAAA,cACT;AAAA,cACA,SAAS;AAAA,cACT;AAAA,cACA;AAAA,YACF,CAAC,KAAK;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,MAGA,QAAQ,SAAS,WAAY;AAC3B,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,mBAAS,YAAY;AACrB,kBAAQ,KAAK;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAAA,MACD,SAAS,SAAS,UAAU;AAC1B,+BAAuB;AACvB,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,CAAC,iBAAiBN,YAAWF,OAAM,GAAG;AAExC,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,OAAO,EAAE,KAAK,SAAUa,QAAO;AACjD,UAAI,CAAC,eAAe,QAAQ,eAAe;AACzC,gBAAQ,cAAcA,MAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AAMD,aAAS,qBAAqB;AAC5B,YAAM,iBAAiB,QAAQ,SAAU,OAAO;AAC9C,YAAI,OAAO,MAAM,MACb,gBAAgB,MAAM,SACtBD,WAAU,kBAAkB,SAAS,CAAC,IAAI,eAC1CE,UAAS,MAAM;AAEnB,YAAI,OAAOA,YAAW,YAAY;AAChC,cAAI,YAAYA,QAAO;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAASF;AAAA,UACX,CAAC;AAED,cAAI,SAAS,SAASG,UAAS;AAAA,UAAC;AAEhC,2BAAiB,KAAK,aAAa,MAAM;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB;AAChC,uBAAiB,QAAQ,SAAUP,KAAI;AACrC,eAAOA,IAAG;AAAA,MACZ,CAAC;AACD,yBAAmB,CAAC;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,mBAAmB,CAAC,gBAAgB,iBAAiB,iBAAiB,eAAe,UAAU,QAAQ,mBAAmB,SAAS,MAAM;AAC7I,IAAI,eAA4B,gBAAgB;AAAA,EAC9C;AACF,CAAC;AAQD,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AAAA,EAClB,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAI,0BAA0B,SAASQ,2BAA0B;AAC/D,SAAO,SAAS;AAClB;AACA,SAAS,wBAAwB,OAAO,OAAO,cAAc;AAC3D,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,QAAI,IAAI,MAAM,KAAK;AACnB,WAAO,KAAK,OAAO,MAAM,QAAQ,YAAY,IAAI,aAAa,KAAK,IAAI,eAAe;AAAA,EACxF;AAEA,SAAO;AACT;AACA,SAAS,OAAO,OAAO,MAAM;AAC3B,MAAI,MAAM,CAAC,EAAE,SAAS,KAAK,KAAK;AAChC,SAAO,IAAI,QAAQ,SAAS,MAAM,KAAK,IAAI,QAAQ,OAAO,GAAG,IAAI;AACnE;AACA,SAAS,uBAAuB,OAAO,MAAM;AAC3C,SAAO,OAAO,UAAU,aAAa,MAAM,MAAM,QAAQ,IAAI,IAAI;AACnE;AACA,SAAS,WAAWR,KAAI,IAAI;AAE1B,MAAI,OAAO,GAAG;AACZ,WAAOA;AAAA,EACT;AAEA,MAAI;AACJ,SAAO,SAAU,KAAK;AACpB,iBAAa,OAAO;AACpB,cAAU,WAAW,WAAY;AAC/B,MAAAA,IAAG,GAAG;AAAA,IACR,GAAG,EAAE;AAAA,EACP;AACF;AACA,SAAS,iBAAiB,KAAK,MAAM;AACnC,MAAI,QAAQ,OAAO,OAAO,CAAC,GAAG,GAAG;AACjC,OAAK,QAAQ,SAAU,KAAK;AAC1B,WAAO,MAAM,GAAG;AAAA,EAClB,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,MAAM,MAAM,KAAK,EAAE,OAAO,OAAO;AAC1C;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,CAAC,EAAE,OAAO,KAAK;AACxB;AACA,SAAS,aAAa,KAAK,OAAO;AAChC,MAAI,IAAI,QAAQ,KAAK,MAAM,IAAI;AAC7B,QAAI,KAAK,KAAK;AAAA,EAChB;AACF;AACA,SAAS,OAAO,KAAK;AACnB,SAAO,IAAI,OAAO,SAAU,MAAM,OAAO;AACvC,WAAO,IAAI,QAAQ,IAAI,MAAM;AAAA,EAC/B,CAAC;AACH;AACA,SAAS,mBAAmB,WAAW;AACrC,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,CAAC,EAAE,MAAM,KAAK,KAAK;AAC5B;AACA,SAAS,qBAAqB,KAAK;AACjC,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAU,KAAK,KAAK;AACjD,QAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,UAAI,GAAG,IAAI,IAAI,GAAG;AAAA,IACpB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,MAAM;AACb,SAAO,SAAS,cAAc,KAAK;AACrC;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,CAAC,WAAW,UAAU,EAAE,KAAK,SAAU,MAAM;AAClD,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B,CAAC;AACH;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,OAAO,UAAU;AACjC;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,OAAO,OAAO,YAAY;AACnC;AACA,SAAS,mBAAmB,OAAO;AACjC,SAAO,CAAC,EAAE,SAAS,MAAM,UAAU,MAAM,OAAO,cAAc;AAChE;AACA,SAAS,mBAAmB,OAAO;AACjC,MAAI,YAAY,KAAK,GAAG;AACtB,WAAO,CAAC,KAAK;AAAA,EACf;AAEA,MAAI,WAAW,KAAK,GAAG;AACrB,WAAO,UAAU,KAAK;AAAA,EACxB;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,SAAS,iBAAiB,KAAK,CAAC;AACnD;AACA,SAAS,sBAAsB,KAAK,OAAO;AACzC,MAAI,QAAQ,SAAU,IAAI;AACxB,QAAI,IAAI;AACN,SAAG,MAAM,qBAAqB,QAAQ;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AACA,SAAS,mBAAmB,KAAK,OAAO;AACtC,MAAI,QAAQ,SAAU,IAAI;AACxB,QAAI,IAAI;AACN,SAAG,aAAa,cAAc,KAAK;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AACA,SAAS,iBAAiB,mBAAmB;AAC3C,MAAI;AAEJ,MAAI,oBAAoB,iBAAiB,iBAAiB,GACtD,UAAU,kBAAkB,CAAC;AAGjC,SAAO,WAAW,SAAS,wBAAwB,QAAQ,kBAAkB,QAAQ,sBAAsB,OAAO,QAAQ,gBAAgB;AAC5I;AACA,SAAS,iCAAiC,gBAAgB,OAAO;AAC/D,MAAI,UAAU,MAAM,SAChB,UAAU,MAAM;AACpB,SAAO,eAAe,MAAM,SAAU,MAAM;AAC1C,QAAI,aAAa,KAAK,YAClB,cAAc,KAAK,aACnBS,SAAQ,KAAK;AACjB,QAAI,oBAAoBA,OAAM;AAC9B,QAAI,gBAAgB,mBAAmB,YAAY,SAAS;AAC5D,QAAI,aAAa,YAAY,cAAc;AAE3C,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,kBAAkB,WAAW,WAAW,IAAI,IAAI;AAClE,QAAI,iBAAiB,kBAAkB,QAAQ,WAAW,OAAO,IAAI;AACrE,QAAI,eAAe,kBAAkB,UAAU,WAAW,KAAK,IAAI;AACnE,QAAI,gBAAgB,kBAAkB,SAAS,WAAW,MAAM,IAAI;AACpE,QAAI,aAAa,WAAW,MAAM,UAAU,cAAc;AAC1D,QAAI,gBAAgB,UAAU,WAAW,SAAS,iBAAiB;AACnE,QAAI,cAAc,WAAW,OAAO,UAAU,eAAe;AAC7D,QAAI,eAAe,UAAU,WAAW,QAAQ,gBAAgB;AAChE,WAAO,cAAc,iBAAiB,eAAe;AAAA,EACvD,CAAC;AACH;AACA,SAAS,4BAA4B,KAAK,QAAQ,UAAU;AAC1D,MAAI,SAAS,SAAS;AAGtB,GAAC,iBAAiB,qBAAqB,EAAE,QAAQ,SAAU,OAAO;AAChE,QAAI,MAAM,EAAE,OAAO,QAAQ;AAAA,EAC7B,CAAC;AACH;AAMA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,SAAS;AAEb,SAAO,QAAQ;AACb,QAAI;AAEJ,QAAI,OAAO,SAAS,MAAM,GAAG;AAC3B,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,eAAe,OAAO,UAAU,sBAAsB,OAAO,YAAY,MAAM,OAAO,SAAS,oBAAoB;AAAA,EACrI;AAEA,SAAO;AACT;AAEA,IAAI,eAAe;AAAA,EACjB,SAAS;AACX;AACA,IAAI,oBAAoB;AAQxB,SAAS,uBAAuB;AAC9B,MAAI,aAAa,SAAS;AACxB;AAAA,EACF;AAEA,eAAa,UAAU;AAEvB,MAAI,OAAO,aAAa;AACtB,aAAS,iBAAiB,aAAa,mBAAmB;AAAA,EAC5D;AACF;AAOA,SAAS,sBAAsB;AAC7B,MAAI,MAAM,YAAY,IAAI;AAE1B,MAAI,MAAM,oBAAoB,IAAI;AAChC,iBAAa,UAAU;AACvB,aAAS,oBAAoB,aAAa,mBAAmB;AAAA,EAC/D;AAEA,sBAAoB;AACtB;AAQA,SAAS,eAAe;AACtB,MAAI,gBAAgB,SAAS;AAE7B,MAAI,mBAAmB,aAAa,GAAG;AACrC,QAAI,WAAW,cAAc;AAE7B,QAAI,cAAc,QAAQ,CAAC,SAAS,MAAM,WAAW;AACnD,oBAAc,KAAK;AAAA,IACrB;AAAA,EACF;AACF;AACA,SAAS,2BAA2B;AAClC,WAAS,iBAAiB,cAAc,sBAAsB,aAAa;AAC3E,SAAO,iBAAiB,QAAQ,YAAY;AAC9C;AAEA,IAAI,YAAY,OAAO,WAAW,eAAe,OAAO,aAAa;AACrE,IAAI,SAAS;AAAA;AAAA,EACb,CAAC,CAAC,OAAO;AAAA,IAAW;AAEpB,IAAI,cAAc;AAAA,EAChB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,QAAQ;AACV;AACA,IAAI,cAAc;AAAA,EAChB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAI,eAAe,OAAO,OAAO;AAAA,EAC/B,UAAU;AAAA,EACV,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,EACP,UAAU,CAAC,KAAK,GAAG;AAAA,EACnB,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,QAAQ,CAAC,GAAG,EAAE;AAAA,EACd,eAAe,SAAS,gBAAgB;AAAA,EAAC;AAAA,EACzC,gBAAgB,SAAS,iBAAiB;AAAA,EAAC;AAAA,EAC3C,UAAU,SAAS,WAAW;AAAA,EAAC;AAAA,EAC/B,WAAW,SAAS,YAAY;AAAA,EAAC;AAAA,EACjC,UAAU,SAAS,WAAW;AAAA,EAAC;AAAA,EAC/B,QAAQ,SAAS,SAAS;AAAA,EAAC;AAAA,EAC3B,SAAS,SAAS,UAAU;AAAA,EAAC;AAAA,EAC7B,QAAQ,SAAS,SAAS;AAAA,EAAC;AAAA,EAC3B,SAAS,SAAS,UAAU;AAAA,EAAC;AAAA,EAC7B,WAAW,SAAS,YAAY;AAAA,EAAC;AAAA,EACjC,aAAa,SAAS,cAAc;AAAA,EAAC;AAAA,EACrC,gBAAgB,SAAS,iBAAiB;AAAA,EAAC;AAAA,EAC3C,WAAW;AAAA,EACX,SAAS,CAAC;AAAA,EACV,eAAe,CAAC;AAAA,EAChB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,OAAO;AAAA,EACP,SAAS;AAAA,EACT,eAAe;AACjB,GAAG,aAAa,WAAW;AAC3B,IAAI,cAAc,OAAO,KAAK,YAAY;AAC1C,IAAI,kBAAkB,SAASC,iBAAgB,cAAc;AAE3D,MAAI,OAAO,OAAO,KAAK,YAAY;AACnC,OAAK,QAAQ,SAAU,KAAK;AAC1B,iBAAa,GAAG,IAAI,aAAa,GAAG;AAAA,EACtC,CAAC;AACH;AACA,SAAS,uBAAuB,aAAa;AAC3C,MAAI,UAAU,YAAY,WAAW,CAAC;AACtC,MAAIC,eAAc,QAAQ,OAAO,SAAU,KAAKC,SAAQ;AACtD,QAAI,OAAOA,QAAO,MACd,eAAeA,QAAO;AAE1B,QAAI,MAAM;AACR,UAAI;AAEJ,UAAI,IAAI,IAAI,YAAY,IAAI,MAAM,SAAY,YAAY,IAAI,KAAK,QAAQ,aAAa,IAAI,MAAM,OAAO,QAAQ;AAAA,IACnH;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,OAAO,CAAC,GAAG,aAAaD,YAAW;AACnD;AACA,SAAS,sBAAsBjB,YAAW,SAAS;AACjD,MAAI,WAAW,UAAU,OAAO,KAAK,uBAAuB,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,IAC1F;AAAA,EACF,CAAC,CAAC,CAAC,IAAI;AACP,MAAIe,SAAQ,SAAS,OAAO,SAAU,KAAK,KAAK;AAC9C,QAAI,iBAAiBf,WAAU,aAAa,gBAAgB,GAAG,KAAK,IAAI,KAAK;AAE7E,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,WAAW;AACrB,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI;AACF,YAAI,GAAG,IAAI,KAAK,MAAM,aAAa;AAAA,MACrC,SAAS,GAAG;AACV,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,IACF;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAOe;AACT;AACA,SAAS,cAAcf,YAAWe,QAAO;AACvC,MAAI,MAAM,OAAO,OAAO,CAAC,GAAGA,QAAO;AAAA,IACjC,SAAS,uBAAuBA,OAAM,SAAS,CAACf,UAAS,CAAC;AAAA,EAC5D,GAAGe,OAAM,mBAAmB,CAAC,IAAI,sBAAsBf,YAAWe,OAAM,OAAO,CAAC;AAChF,MAAI,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,MAAM,IAAI,IAAI;AACxD,MAAI,OAAO;AAAA,IACT,UAAU,IAAI,KAAK,aAAa,SAASA,OAAM,cAAc,IAAI,KAAK;AAAA,IACtE,SAAS,IAAI,KAAK,YAAY,SAASA,OAAM,cAAc,OAAO,gBAAgB,IAAI,KAAK;AAAA,EAC7F;AACA,SAAO;AACT;AAEA,IAAI,YAAY,SAASI,aAAY;AACnC,SAAO;AACT;AAEA,SAAS,wBAAwB,SAAS,MAAM;AAC9C,UAAQ,UAAU,CAAC,IAAI;AACzB;AAEA,SAAS,mBAAmB,OAAO;AACjC,MAAIC,SAAQ,IAAI;AAEhB,MAAI,UAAU,MAAM;AAClB,IAAAA,OAAM,YAAY;AAAA,EACpB,OAAO;AACL,IAAAA,OAAM,YAAY;AAElB,QAAI,YAAY,KAAK,GAAG;AACtB,MAAAA,OAAM,YAAY,KAAK;AAAA,IACzB,OAAO;AACL,8BAAwBA,QAAO,KAAK;AAAA,IACtC;AAAA,EACF;AAEA,SAAOA;AACT;AAEA,SAAS,WAAW,SAASL,QAAO;AAClC,MAAI,YAAYA,OAAM,OAAO,GAAG;AAC9B,4BAAwB,SAAS,EAAE;AACnC,YAAQ,YAAYA,OAAM,OAAO;AAAA,EACnC,WAAW,OAAOA,OAAM,YAAY,YAAY;AAC9C,QAAIA,OAAM,WAAW;AACnB,8BAAwB,SAASA,OAAM,OAAO;AAAA,IAChD,OAAO;AACL,cAAQ,cAAcA,OAAM;AAAA,IAC9B;AAAA,EACF;AACF;AACA,SAAS,YAAYjB,SAAQ;AAC3B,MAAI,MAAMA,QAAO;AACjB,MAAI,cAAc,UAAU,IAAI,QAAQ;AACxC,SAAO;AAAA,IACL;AAAA,IACA,SAAS,YAAY,KAAK,SAAU,MAAM;AACxC,aAAO,KAAK,UAAU,SAAS,aAAa;AAAA,IAC9C,CAAC;AAAA,IACD,OAAO,YAAY,KAAK,SAAU,MAAM;AACtC,aAAO,KAAK,UAAU,SAAS,WAAW,KAAK,KAAK,UAAU,SAAS,eAAe;AAAA,IACxF,CAAC;AAAA,IACD,UAAU,YAAY,KAAK,SAAU,MAAM;AACzC,aAAO,KAAK,UAAU,SAAS,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AACF;AACA,SAAS,OAAO,UAAU;AACxB,MAAIA,UAAS,IAAI;AACjB,MAAI,MAAM,IAAI;AACd,MAAI,YAAY;AAChB,MAAI,aAAa,cAAc,QAAQ;AACvC,MAAI,aAAa,YAAY,IAAI;AACjC,MAAI,UAAU,IAAI;AAClB,UAAQ,YAAY;AACpB,UAAQ,aAAa,cAAc,QAAQ;AAC3C,aAAW,SAAS,SAAS,KAAK;AAClC,EAAAA,QAAO,YAAY,GAAG;AACtB,MAAI,YAAY,OAAO;AACvB,WAAS,SAAS,OAAO,SAAS,KAAK;AAEvC,WAAS,SAAS,WAAW,WAAW;AACtC,QAAI,eAAe,YAAYA,OAAM,GACjCuB,OAAM,aAAa,KACnBC,WAAU,aAAa,SACvBF,SAAQ,aAAa;AAEzB,QAAI,UAAU,OAAO;AACnB,MAAAC,KAAI,aAAa,cAAc,UAAU,KAAK;AAAA,IAChD,OAAO;AACL,MAAAA,KAAI,gBAAgB,YAAY;AAAA,IAClC;AAEA,QAAI,OAAO,UAAU,cAAc,UAAU;AAC3C,MAAAA,KAAI,aAAa,kBAAkB,UAAU,SAAS;AAAA,IACxD,OAAO;AACL,MAAAA,KAAI,gBAAgB,gBAAgB;AAAA,IACtC;AAEA,QAAI,UAAU,SAAS;AACrB,MAAAA,KAAI,aAAa,gBAAgB,EAAE;AAAA,IACrC,OAAO;AACL,MAAAA,KAAI,gBAAgB,cAAc;AAAA,IACpC;AAEA,IAAAA,KAAI,MAAM,WAAW,OAAO,UAAU,aAAa,WAAW,UAAU,WAAW,OAAO,UAAU;AAEpG,QAAI,UAAU,MAAM;AAClB,MAAAA,KAAI,aAAa,QAAQ,UAAU,IAAI;AAAA,IACzC,OAAO;AACL,MAAAA,KAAI,gBAAgB,MAAM;AAAA,IAC5B;AAEA,QAAI,UAAU,YAAY,UAAU,WAAW,UAAU,cAAc,UAAU,WAAW;AAC1F,iBAAWC,UAAS,SAAS,KAAK;AAAA,IACpC;AAEA,QAAI,UAAU,OAAO;AACnB,UAAI,CAACF,QAAO;AACV,QAAAC,KAAI,YAAY,mBAAmB,UAAU,KAAK,CAAC;AAAA,MACrD,WAAW,UAAU,UAAU,UAAU,OAAO;AAC9C,QAAAA,KAAI,YAAYD,MAAK;AACrB,QAAAC,KAAI,YAAY,mBAAmB,UAAU,KAAK,CAAC;AAAA,MACrD;AAAA,IACF,WAAWD,QAAO;AAChB,MAAAC,KAAI,YAAYD,MAAK;AAAA,IACvB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQtB;AAAA,IACR;AAAA,EACF;AACF;AAGA,OAAO,UAAU;AAEjB,IAAI,YAAY;AAChB,IAAI,qBAAqB,CAAC;AAE1B,IAAI,mBAAmB,CAAC;AACxB,SAAS,YAAYE,YAAW,aAAa;AAC3C,MAAIe,SAAQ,cAAcf,YAAW,OAAO,OAAO,CAAC,GAAG,cAAc,uBAAuB,qBAAqB,WAAW,CAAC,CAAC,CAAC;AAI/H,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,qBAAqB;AACzB,MAAI,gCAAgC;AACpC,MAAI,eAAe;AACnB,MAAI,sBAAsB;AAC1B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,CAAC;AACjB,MAAI,uBAAuB,WAAW,aAAae,OAAM,mBAAmB;AAC5E,MAAI;AAIJ,MAAI,KAAK;AACT,MAAI,iBAAiB;AACrB,MAAI,UAAU,OAAOA,OAAM,OAAO;AAClC,MAAI,QAAQ;AAAA;AAAA,IAEV,WAAW;AAAA;AAAA,IAEX,WAAW;AAAA;AAAA,IAEX,aAAa;AAAA;AAAA,IAEb,WAAW;AAAA;AAAA,IAEX,SAAS;AAAA,EACX;AACA,MAAI,WAAW;AAAA;AAAA,IAEb;AAAA,IACA,WAAWf;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA,OAAOe;AAAA,IACP;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA,YAAYQ;AAAA,IACZ;AAAA,IACA,MAAMC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAKA,MAAI,CAACT,OAAM,QAAQ;AAEjB,WAAO;AAAA,EACT;AAKA,MAAI,gBAAgBA,OAAM,OAAO,QAAQ,GACrCjB,UAAS,cAAc,QACvB,WAAW,cAAc;AAE7B,EAAAA,QAAO,aAAa,mBAAmB,EAAE;AACzC,EAAAA,QAAO,KAAK,WAAW,SAAS;AAChC,WAAS,SAASA;AAClB,EAAAE,WAAU,SAAS;AACnB,EAAAF,QAAO,SAAS;AAChB,MAAI,eAAe,QAAQ,IAAI,SAAUoB,SAAQ;AAC/C,WAAOA,QAAO,GAAG,QAAQ;AAAA,EAC3B,CAAC;AACD,MAAI,kBAAkBlB,WAAU,aAAa,eAAe;AAC5D,eAAa;AACb,8BAA4B;AAC5B,eAAa;AACb,aAAW,YAAY,CAAC,QAAQ,CAAC;AAEjC,MAAIe,OAAM,cAAc;AACtB,iBAAa;AAAA,EACf;AAIA,EAAAjB,QAAO,iBAAiB,cAAc,WAAY;AAChD,QAAI,SAAS,MAAM,eAAe,SAAS,MAAM,WAAW;AAC1D,eAAS,mBAAmB;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,EAAAA,QAAO,iBAAiB,cAAc,WAAY;AAChD,QAAI,SAAS,MAAM,eAAe,SAAS,MAAM,QAAQ,QAAQ,YAAY,KAAK,GAAG;AACnF,kBAAY,EAAE,iBAAiB,aAAa,oBAAoB;AAAA,IAClE;AAAA,EACF,CAAC;AACD,SAAO;AAIP,WAAS,6BAA6B;AACpC,QAAI,QAAQ,SAAS,MAAM;AAC3B,WAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,OAAO,CAAC;AAAA,EACjD;AAEA,WAAS,2BAA2B;AAClC,WAAO,2BAA2B,EAAE,CAAC,MAAM;AAAA,EAC7C;AAEA,WAAS,uBAAuB;AAC9B,QAAI;AAGJ,WAAO,CAAC,GAAG,wBAAwB,SAAS,MAAM,WAAW,QAAQ,sBAAsB;AAAA,EAC7F;AAEA,WAAS,mBAAmB;AAC1B,WAAO,iBAAiBE;AAAA,EAC1B;AAEA,WAAS,cAAc;AACrB,QAAI,SAAS,iBAAiB,EAAE;AAChC,WAAO,SAAS,iBAAiB,MAAM,IAAI;AAAA,EAC7C;AAEA,WAAS,6BAA6B;AACpC,WAAO,YAAYF,OAAM;AAAA,EAC3B;AAEA,WAAS,SAAS,QAAQ;AAIxB,QAAI,SAAS,MAAM,aAAa,CAAC,SAAS,MAAM,aAAa,aAAa,WAAW,oBAAoB,iBAAiB,SAAS,SAAS;AAC1I,aAAO;AAAA,IACT;AAEA,WAAO,wBAAwB,SAAS,MAAM,OAAO,SAAS,IAAI,GAAG,aAAa,KAAK;AAAA,EACzF;AAEA,WAAS,aAAa,UAAU;AAC9B,QAAI,aAAa,QAAQ;AACvB,iBAAW;AAAA,IACb;AAEA,IAAAA,QAAO,MAAM,gBAAgB,SAAS,MAAM,eAAe,CAAC,WAAW,KAAK;AAC5E,IAAAA,QAAO,MAAM,SAAS,KAAK,SAAS,MAAM;AAAA,EAC5C;AAEA,WAAS,WAAW,MAAM,MAAM,uBAAuB;AACrD,QAAI,0BAA0B,QAAQ;AACpC,8BAAwB;AAAA,IAC1B;AAEA,iBAAa,QAAQ,SAAU,aAAa;AAC1C,UAAI,YAAY,IAAI,GAAG;AACrB,oBAAY,IAAI,EAAE,MAAM,aAAa,IAAI;AAAA,MAC3C;AAAA,IACF,CAAC;AAED,QAAI,uBAAuB;AACzB,UAAI;AAEJ,OAAC,kBAAkB,SAAS,OAAO,IAAI,EAAE,MAAM,iBAAiB,IAAI;AAAA,IACtE;AAAA,EACF;AAEA,WAAS,6BAA6B;AACpC,QAAI,OAAO,SAAS,MAAM;AAE1B,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AAEA,QAAI,OAAO,UAAU,KAAK;AAC1B,QAAI2B,MAAK3B,QAAO;AAChB,QAAI,QAAQ,iBAAiB,SAAS,MAAM,iBAAiBE,UAAS;AACtE,UAAM,QAAQ,SAAU,MAAM;AAC5B,UAAI,eAAe,KAAK,aAAa,IAAI;AAEzC,UAAI,SAAS,MAAM,WAAW;AAC5B,aAAK,aAAa,MAAM,eAAe,eAAe,MAAMyB,MAAKA,GAAE;AAAA,MACrE,OAAO;AACL,YAAI,YAAY,gBAAgB,aAAa,QAAQA,KAAI,EAAE,EAAE,KAAK;AAElE,YAAI,WAAW;AACb,eAAK,aAAa,MAAM,SAAS;AAAA,QACnC,OAAO;AACL,eAAK,gBAAgB,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,8BAA8B;AACrC,QAAI,mBAAmB,CAAC,SAAS,MAAM,KAAK,UAAU;AACpD;AAAA,IACF;AAEA,QAAI,QAAQ,iBAAiB,SAAS,MAAM,iBAAiBzB,UAAS;AACtE,UAAM,QAAQ,SAAU,MAAM;AAC5B,UAAI,SAAS,MAAM,aAAa;AAC9B,aAAK,aAAa,iBAAiB,SAAS,MAAM,aAAa,SAAS,iBAAiB,IAAI,SAAS,OAAO;AAAA,MAC/G,OAAO;AACL,aAAK,gBAAgB,eAAe;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,mCAAmC;AAC1C,gBAAY,EAAE,oBAAoB,aAAa,oBAAoB;AACnE,yBAAqB,mBAAmB,OAAO,SAAU,UAAU;AACjE,aAAO,aAAa;AAAA,IACtB,CAAC;AAAA,EACH;AAEA,WAAS,gBAAgB,OAAO;AAE9B,QAAI,aAAa,SAAS;AACxB,UAAI,gBAAgB,MAAM,SAAS,aAAa;AAC9C;AAAA,MACF;AAAA,IACF;AAEA,QAAI,eAAe,MAAM,gBAAgB,MAAM,aAAa,EAAE,CAAC,KAAK,MAAM;AAE1E,QAAI,SAAS,MAAM,eAAe,eAAeF,SAAQ,YAAY,GAAG;AACtE;AAAA,IACF;AAGA,QAAI,iBAAiB,SAAS,MAAM,iBAAiBE,UAAS,EAAE,KAAK,SAAU,IAAI;AACjF,aAAO,eAAe,IAAI,YAAY;AAAA,IACxC,CAAC,GAAG;AACF,UAAI,aAAa,SAAS;AACxB;AAAA,MACF;AAEA,UAAI,SAAS,MAAM,aAAa,SAAS,MAAM,QAAQ,QAAQ,OAAO,KAAK,GAAG;AAC5E;AAAA,MACF;AAAA,IACF,OAAO;AACL,iBAAW,kBAAkB,CAAC,UAAU,KAAK,CAAC;AAAA,IAChD;AAEA,QAAI,SAAS,MAAM,gBAAgB,MAAM;AACvC,eAAS,mBAAmB;AAC5B,eAAS,KAAK;AAId,sCAAgC;AAChC,iBAAW,WAAY;AACrB,wCAAgC;AAAA,MAClC,CAAC;AAID,UAAI,CAAC,SAAS,MAAM,WAAW;AAC7B,4BAAoB;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,cAAc;AACrB,mBAAe;AAAA,EACjB;AAEA,WAAS,eAAe;AACtB,mBAAe;AAAA,EACjB;AAEA,WAAS,mBAAmB;AAC1B,QAAI,MAAM,YAAY;AACtB,QAAI,iBAAiB,aAAa,iBAAiB,IAAI;AACvD,QAAI,iBAAiB,YAAY,iBAAiB,aAAa;AAC/D,QAAI,iBAAiB,cAAc,cAAc,aAAa;AAC9D,QAAI,iBAAiB,aAAa,aAAa,aAAa;AAAA,EAC9D;AAEA,WAAS,sBAAsB;AAC7B,QAAI,MAAM,YAAY;AACtB,QAAI,oBAAoB,aAAa,iBAAiB,IAAI;AAC1D,QAAI,oBAAoB,YAAY,iBAAiB,aAAa;AAClE,QAAI,oBAAoB,cAAc,cAAc,aAAa;AACjE,QAAI,oBAAoB,aAAa,aAAa,aAAa;AAAA,EACjE;AAEA,WAAS,kBAAkB,UAAU,UAAU;AAC7C,oBAAgB,UAAU,WAAY;AACpC,UAAI,CAAC,SAAS,MAAM,aAAaF,QAAO,cAAcA,QAAO,WAAW,SAASA,OAAM,GAAG;AACxF,iBAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,iBAAiB,UAAU,UAAU;AAC5C,oBAAgB,UAAU,QAAQ;AAAA,EACpC;AAEA,WAAS,gBAAgB,UAAU,UAAU;AAC3C,QAAI,MAAM,2BAA2B,EAAE;AAEvC,aAAS,SAAS,OAAO;AACvB,UAAI,MAAM,WAAW,KAAK;AACxB,oCAA4B,KAAK,UAAU,QAAQ;AACnD,iBAAS;AAAA,MACX;AAAA,IACF;AAIA,QAAI,aAAa,GAAG;AAClB,aAAO,SAAS;AAAA,IAClB;AAEA,gCAA4B,KAAK,UAAU,4BAA4B;AACvE,gCAA4B,KAAK,OAAO,QAAQ;AAChD,mCAA+B;AAAA,EACjC;AAEA,WAAS,GAAG,WAAW,SAAS,SAAS;AACvC,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ,iBAAiB,SAAS,MAAM,iBAAiBE,UAAS;AACtE,UAAM,QAAQ,SAAU,MAAM;AAC5B,WAAK,iBAAiB,WAAW,SAAS,OAAO;AACjD,gBAAU,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,eAAe;AACtB,QAAI,yBAAyB,GAAG;AAC9B,SAAG,cAAc0B,YAAW;AAAA,QAC1B,SAAS;AAAA,MACX,CAAC;AACD,SAAG,YAAY,cAAc;AAAA,QAC3B,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,kBAAc,SAAS,MAAM,OAAO,EAAE,QAAQ,SAAU,WAAW;AACjE,UAAI,cAAc,UAAU;AAC1B;AAAA,MACF;AAEA,SAAG,WAAWA,UAAS;AAEvB,cAAQ,WAAW;AAAA,QACjB,KAAK;AACH,aAAG,cAAc,YAAY;AAC7B;AAAA,QAEF,KAAK;AACH,aAAG,SAAS,aAAa,QAAQ,gBAAgB;AACjD;AAAA,QAEF,KAAK;AACH,aAAG,YAAY,gBAAgB;AAC/B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,kBAAkB;AACzB,cAAU,QAAQ,SAAU,MAAM;AAChC,UAAI,OAAO,KAAK,MACZ,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,UAAU,KAAK;AACnB,WAAK,oBAAoB,WAAW,SAAS,OAAO;AAAA,IACtD,CAAC;AACD,gBAAY,CAAC;AAAA,EACf;AAEA,WAASA,WAAU,OAAO;AACxB,QAAI;AAEJ,QAAI,0BAA0B;AAE9B,QAAI,CAAC,SAAS,MAAM,aAAa,uBAAuB,KAAK,KAAK,+BAA+B;AAC/F;AAAA,IACF;AAEA,QAAI,eAAe,oBAAoB,qBAAqB,OAAO,SAAS,kBAAkB,UAAU;AACxG,uBAAmB;AACnB,oBAAgB,MAAM;AACtB,gCAA4B;AAE5B,QAAI,CAAC,SAAS,MAAM,aAAa,aAAa,KAAK,GAAG;AAKpD,yBAAmB,QAAQ,SAAU,UAAU;AAC7C,eAAO,SAAS,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AAGA,QAAI,MAAM,SAAS,YAAY,SAAS,MAAM,QAAQ,QAAQ,YAAY,IAAI,KAAK,uBAAuB,SAAS,MAAM,gBAAgB,SAAS,SAAS,MAAM,WAAW;AAC1K,gCAA0B;AAAA,IAC5B,OAAO;AACL,mBAAa,KAAK;AAAA,IACpB;AAEA,QAAI,MAAM,SAAS,SAAS;AAC1B,2BAAqB,CAAC;AAAA,IACxB;AAEA,QAAI,2BAA2B,CAAC,YAAY;AAC1C,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,YAAY,OAAO;AAC1B,QAAI,SAAS,MAAM;AACnB,QAAI,gCAAgC,iBAAiB,EAAE,SAAS,MAAM,KAAK5B,QAAO,SAAS,MAAM;AAEjG,QAAI,MAAM,SAAS,eAAe,+BAA+B;AAC/D;AAAA,IACF;AAEA,QAAI,iBAAiB,oBAAoB,EAAE,OAAOA,OAAM,EAAE,IAAI,SAAUA,SAAQ;AAC9E,UAAI;AAEJ,UAAI6B,YAAW7B,QAAO;AACtB,UAAIa,UAAS,wBAAwBgB,UAAS,mBAAmB,OAAO,SAAS,sBAAsB;AAEvG,UAAIhB,QAAO;AACT,eAAO;AAAA,UACL,YAAYb,QAAO,sBAAsB;AAAA,UACzC,aAAaa;AAAA,UACb,OAAOI;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT,CAAC,EAAE,OAAO,OAAO;AAEjB,QAAI,iCAAiC,gBAAgB,KAAK,GAAG;AAC3D,uCAAiC;AACjC,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,aAAa,OAAO;AAC3B,QAAI,aAAa,uBAAuB,KAAK,KAAK,SAAS,MAAM,QAAQ,QAAQ,OAAO,KAAK,KAAK;AAElG,QAAI,YAAY;AACd;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,aAAa;AAC9B,eAAS,sBAAsB,KAAK;AACpC;AAAA,IACF;AAEA,iBAAa,KAAK;AAAA,EACpB;AAEA,WAAS,iBAAiB,OAAO;AAC/B,QAAI,SAAS,MAAM,QAAQ,QAAQ,SAAS,IAAI,KAAK,MAAM,WAAW,iBAAiB,GAAG;AACxF;AAAA,IACF;AAGA,QAAI,SAAS,MAAM,eAAe,MAAM,iBAAiBjB,QAAO,SAAS,MAAM,aAAa,GAAG;AAC7F;AAAA,IACF;AAEA,iBAAa,KAAK;AAAA,EACpB;AAEA,WAAS,uBAAuB,OAAO;AACrC,WAAO,aAAa,UAAU,yBAAyB,MAAM,MAAM,KAAK,QAAQ,OAAO,KAAK,IAAI;AAAA,EAClG;AAEA,WAAS,uBAAuB;AAC9B,0BAAsB;AACtB,QAAI,mBAAmB,SAAS,OAC5B,gBAAgB,iBAAiB,eACjC,YAAY,iBAAiB,WAC7BD,UAAS,iBAAiB,QAC1B,yBAAyB,iBAAiB,wBAC1C,iBAAiB,iBAAiB;AACtC,QAAIuB,SAAQ,qBAAqB,IAAI,YAAYtB,OAAM,EAAE,QAAQ;AACjE,QAAI,oBAAoB,yBAAyB;AAAA,MAC/C,uBAAuB;AAAA,MACvB,gBAAgB,uBAAuB,kBAAkB,iBAAiB;AAAA,IAC5E,IAAIE;AACJ,QAAI,gBAAgB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,UAAU,CAAC,eAAe;AAAA,MAC1B,IAAI,SAASM,IAAG,OAAO;AACrB,YAAIK,SAAQ,MAAM;AAElB,YAAI,qBAAqB,GAAG;AAC1B,cAAI,wBAAwB,2BAA2B,GACnD,MAAM,sBAAsB;AAEhC,WAAC,aAAa,oBAAoB,SAAS,EAAE,QAAQ,SAAU,MAAM;AACnE,gBAAI,SAAS,aAAa;AACxB,kBAAI,aAAa,kBAAkBA,OAAM,SAAS;AAAA,YACpD,OAAO;AACL,kBAAIA,OAAM,WAAW,OAAO,iBAAiB,IAAI,GAAG;AAClD,oBAAI,aAAa,UAAU,MAAM,EAAE;AAAA,cACrC,OAAO;AACL,oBAAI,gBAAgB,UAAU,IAAI;AAAA,cACpC;AAAA,YACF;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,WAAW,SAAS,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY,CAAC;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,QACP,QAAQd;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,QACP,SAAS;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,QACP,UAAU,CAAC;AAAA,MACb;AAAA,IACF,GAAG,aAAa;AAEhB,QAAI,qBAAqB,KAAKuB,QAAO;AACnC,gBAAU,KAAK;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,UACP,SAASA;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AAEA,cAAU,KAAK,MAAM,YAAY,iBAAiB,OAAO,SAAS,cAAc,cAAc,CAAC,CAAC;AAChG,aAAS,iBAAiB,aAAa,mBAAmBtB,SAAQ,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA,MACjG;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAEA,WAAS,wBAAwB;AAC/B,QAAI,SAAS,gBAAgB;AAC3B,eAAS,eAAe,QAAQ;AAChC,eAAS,iBAAiB;AAAA,IAC5B;AAAA,EACF;AAEA,WAAS,QAAQ;AACf,QAAI,WAAW,SAAS,MAAM;AAC9B,QAAI;AAMJ,QAAI,OAAO,iBAAiB;AAE5B,QAAI,SAAS,MAAM,eAAe,aAAa,2BAA2B,aAAa,UAAU;AAC/F,mBAAa,KAAK;AAAA,IACpB,OAAO;AACL,mBAAa,uBAAuB,UAAU,CAAC,IAAI,CAAC;AAAA,IACtD;AAIA,QAAI,CAAC,WAAW,SAASA,OAAM,GAAG;AAChC,iBAAW,YAAYA,OAAM;AAAA,IAC/B;AAEA,aAAS,MAAM,YAAY;AAC3B,yBAAqB;AAAA,EACvB;AAEA,WAAS,sBAAsB;AAC7B,WAAO,UAAUA,QAAO,iBAAiB,mBAAmB,CAAC;AAAA,EAC/D;AAEA,WAAS,aAAa,OAAO;AAC3B,aAAS,mBAAmB;AAE5B,QAAI,OAAO;AACT,iBAAW,aAAa,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C;AAEA,qBAAiB;AACjB,QAAI,QAAQ,SAAS,IAAI;AAEzB,QAAI,wBAAwB,2BAA2B,GACnD,aAAa,sBAAsB,CAAC,GACpC,aAAa,sBAAsB,CAAC;AAExC,QAAI,aAAa,WAAW,eAAe,UAAU,YAAY;AAC/D,cAAQ;AAAA,IACV;AAEA,QAAI,OAAO;AACT,oBAAc,WAAW,WAAY;AACnC,iBAAS,KAAK;AAAA,MAChB,GAAG,KAAK;AAAA,IACV,OAAO;AACL,eAAS,KAAK;AAAA,IAChB;AAAA,EACF;AAEA,WAAS,aAAa,OAAO;AAC3B,aAAS,mBAAmB;AAC5B,eAAW,eAAe,CAAC,UAAU,KAAK,CAAC;AAE3C,QAAI,CAAC,SAAS,MAAM,WAAW;AAC7B,0BAAoB;AACpB;AAAA,IACF;AAMA,QAAI,SAAS,MAAM,QAAQ,QAAQ,YAAY,KAAK,KAAK,SAAS,MAAM,QAAQ,QAAQ,OAAO,KAAK,KAAK,CAAC,cAAc,WAAW,EAAE,QAAQ,MAAM,IAAI,KAAK,KAAK,oBAAoB;AACnL;AAAA,IACF;AAEA,QAAI,QAAQ,SAAS,KAAK;AAE1B,QAAI,OAAO;AACT,oBAAc,WAAW,WAAY;AACnC,YAAI,SAAS,MAAM,WAAW;AAC5B,mBAAS,KAAK;AAAA,QAChB;AAAA,MACF,GAAG,KAAK;AAAA,IACV,OAAO;AAGL,mCAA6B,sBAAsB,WAAY;AAC7D,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AAKA,WAAS,SAAS;AAChB,aAAS,MAAM,YAAY;AAAA,EAC7B;AAEA,WAAS,UAAU;AAGjB,aAAS,KAAK;AACd,aAAS,MAAM,YAAY;AAAA,EAC7B;AAEA,WAAS,qBAAqB;AAC5B,iBAAa,WAAW;AACxB,iBAAa,WAAW;AACxB,yBAAqB,0BAA0B;AAAA,EACjD;AAEA,WAAS,SAAS,cAAc;AAE9B,QAAI,SAAS,MAAM,aAAa;AAC9B;AAAA,IACF;AAEA,eAAW,kBAAkB,CAAC,UAAU,YAAY,CAAC;AACrD,oBAAgB;AAChB,QAAI,YAAY,SAAS;AACzB,QAAI,YAAY,cAAcE,YAAW,OAAO,OAAO,CAAC,GAAG,WAAW,qBAAqB,YAAY,GAAG;AAAA,MACxG,kBAAkB;AAAA,IACpB,CAAC,CAAC;AACF,aAAS,QAAQ;AACjB,iBAAa;AAEb,QAAI,UAAU,wBAAwB,UAAU,qBAAqB;AACnE,uCAAiC;AACjC,6BAAuB,WAAW,aAAa,UAAU,mBAAmB;AAAA,IAC9E;AAGA,QAAI,UAAU,iBAAiB,CAAC,UAAU,eAAe;AACvD,uBAAiB,UAAU,aAAa,EAAE,QAAQ,SAAU,MAAM;AAChE,aAAK,gBAAgB,eAAe;AAAA,MACtC,CAAC;AAAA,IACH,WAAW,UAAU,eAAe;AAClC,MAAAA,WAAU,gBAAgB,eAAe;AAAA,IAC3C;AAEA,gCAA4B;AAC5B,iBAAa;AAEb,QAAI,UAAU;AACZ,eAAS,WAAW,SAAS;AAAA,IAC/B;AAEA,QAAI,SAAS,gBAAgB;AAC3B,2BAAqB;AAKrB,0BAAoB,EAAE,QAAQ,SAAU,cAAc;AAGpD,8BAAsB,aAAa,OAAO,eAAe,WAAW;AAAA,MACtE,CAAC;AAAA,IACH;AAEA,eAAW,iBAAiB,CAAC,UAAU,YAAY,CAAC;AAAA,EACtD;AAEA,WAASuB,YAAW,SAAS;AAC3B,aAAS,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,OAAO;AAGd,QAAI,mBAAmB,SAAS,MAAM;AACtC,QAAI,cAAc,SAAS,MAAM;AACjC,QAAI,aAAa,CAAC,SAAS,MAAM;AACjC,QAAI,0BAA0B,aAAa,WAAW,CAAC,SAAS,MAAM;AACtE,QAAI,WAAW,wBAAwB,SAAS,MAAM,UAAU,GAAG,aAAa,QAAQ;AAExF,QAAI,oBAAoB,eAAe,cAAc,yBAAyB;AAC5E;AAAA,IACF;AAKA,QAAI,iBAAiB,EAAE,aAAa,UAAU,GAAG;AAC/C;AAAA,IACF;AAEA,eAAW,UAAU,CAAC,QAAQ,GAAG,KAAK;AAEtC,QAAI,SAAS,MAAM,OAAO,QAAQ,MAAM,OAAO;AAC7C;AAAA,IACF;AAEA,aAAS,MAAM,YAAY;AAE3B,QAAI,qBAAqB,GAAG;AAC1B,MAAAzB,QAAO,MAAM,aAAa;AAAA,IAC5B;AAEA,iBAAa;AACb,qBAAiB;AAEjB,QAAI,CAAC,SAAS,MAAM,WAAW;AAC7B,MAAAA,QAAO,MAAM,aAAa;AAAA,IAC5B;AAIA,QAAI,qBAAqB,GAAG;AAC1B,UAAI,yBAAyB,2BAA2B,GACpD,MAAM,uBAAuB,KAC7B,UAAU,uBAAuB;AAErC,4BAAsB,CAAC,KAAK,OAAO,GAAG,CAAC;AAAA,IACzC;AAEA,oBAAgB,SAAS8B,iBAAgB;AACvC,UAAI;AAEJ,UAAI,CAAC,SAAS,MAAM,aAAa,qBAAqB;AACpD;AAAA,MACF;AAEA,4BAAsB;AAEtB,WAAK9B,QAAO;AACZ,MAAAA,QAAO,MAAM,aAAa,SAAS,MAAM;AAEzC,UAAI,qBAAqB,KAAK,SAAS,MAAM,WAAW;AACtD,YAAI,yBAAyB,2BAA2B,GACpD,OAAO,uBAAuB,KAC9B,WAAW,uBAAuB;AAEtC,8BAAsB,CAAC,MAAM,QAAQ,GAAG,QAAQ;AAChD,2BAAmB,CAAC,MAAM,QAAQ,GAAG,SAAS;AAAA,MAChD;AAEA,iCAA2B;AAC3B,kCAA4B;AAC5B,mBAAa,kBAAkB,QAAQ;AAGvC,OAAC,yBAAyB,SAAS,mBAAmB,OAAO,SAAS,uBAAuB,YAAY;AACzG,iBAAW,WAAW,CAAC,QAAQ,CAAC;AAEhC,UAAI,SAAS,MAAM,aAAa,qBAAqB,GAAG;AACtD,yBAAiB,UAAU,WAAY;AACrC,mBAAS,MAAM,UAAU;AACzB,qBAAW,WAAW,CAAC,QAAQ,CAAC;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AAEA,WAAS0B,QAAO;AAGd,QAAI,kBAAkB,CAAC,SAAS,MAAM;AACtC,QAAI,cAAc,SAAS,MAAM;AACjC,QAAI,aAAa,CAAC,SAAS,MAAM;AACjC,QAAI,WAAW,wBAAwB,SAAS,MAAM,UAAU,GAAG,aAAa,QAAQ;AAExF,QAAI,mBAAmB,eAAe,YAAY;AAChD;AAAA,IACF;AAEA,eAAW,UAAU,CAAC,QAAQ,GAAG,KAAK;AAEtC,QAAI,SAAS,MAAM,OAAO,QAAQ,MAAM,OAAO;AAC7C;AAAA,IACF;AAEA,aAAS,MAAM,YAAY;AAC3B,aAAS,MAAM,UAAU;AACzB,0BAAsB;AACtB,yBAAqB;AAErB,QAAI,qBAAqB,GAAG;AAC1B,MAAA1B,QAAO,MAAM,aAAa;AAAA,IAC5B;AAEA,qCAAiC;AACjC,wBAAoB;AACpB,iBAAa,IAAI;AAEjB,QAAI,qBAAqB,GAAG;AAC1B,UAAI,yBAAyB,2BAA2B,GACpD,MAAM,uBAAuB,KAC7B,UAAU,uBAAuB;AAErC,UAAI,SAAS,MAAM,WAAW;AAC5B,8BAAsB,CAAC,KAAK,OAAO,GAAG,QAAQ;AAC9C,2BAAmB,CAAC,KAAK,OAAO,GAAG,QAAQ;AAAA,MAC7C;AAAA,IACF;AAEA,+BAA2B;AAC3B,gCAA4B;AAE5B,QAAI,SAAS,MAAM,WAAW;AAC5B,UAAI,qBAAqB,GAAG;AAC1B,0BAAkB,UAAU,SAAS,OAAO;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AAEA,WAAS,sBAAsB,OAAO;AAEpC,gBAAY,EAAE,iBAAiB,aAAa,oBAAoB;AAChE,iBAAa,oBAAoB,oBAAoB;AACrD,yBAAqB,KAAK;AAAA,EAC5B;AAEA,WAAS,UAAU;AAEjB,QAAI,SAAS,MAAM,WAAW;AAC5B,eAAS,KAAK;AAAA,IAChB;AAEA,QAAI,CAAC,SAAS,MAAM,WAAW;AAC7B;AAAA,IACF;AAEA,0BAAsB;AAItB,wBAAoB,EAAE,QAAQ,SAAU,cAAc;AACpD,mBAAa,OAAO,QAAQ;AAAA,IAC9B,CAAC;AAED,QAAIA,QAAO,YAAY;AACrB,MAAAA,QAAO,WAAW,YAAYA,OAAM;AAAA,IACtC;AAEA,uBAAmB,iBAAiB,OAAO,SAAU,GAAG;AACtD,aAAO,MAAM;AAAA,IACf,CAAC;AACD,aAAS,MAAM,YAAY;AAC3B,eAAW,YAAY,CAAC,QAAQ,CAAC;AAAA,EACnC;AAEA,WAAS,UAAU;AAEjB,QAAI,SAAS,MAAM,aAAa;AAC9B;AAAA,IACF;AAEA,aAAS,mBAAmB;AAC5B,aAAS,QAAQ;AACjB,oBAAgB;AAChB,WAAOE,WAAU;AACjB,aAAS,MAAM,cAAc;AAC7B,eAAW,aAAa,CAAC,QAAQ,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,MAAM,SAAS,eAAe;AACrC,MAAI,kBAAkB,QAAQ;AAC5B,oBAAgB,CAAC;AAAA,EACnB;AAEA,MAAI,UAAU,aAAa,QAAQ,OAAO,cAAc,WAAW,CAAC,CAAC;AAErE,2BAAyB;AACzB,MAAI,cAAc,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA,IACjD;AAAA,EACF,CAAC;AACD,MAAI,WAAW,mBAAmB,OAAO;AAEzC,MAAI,YAAY,SAAS,OAAO,SAAU,KAAKA,YAAW;AACxD,QAAI,WAAWA,cAAa,YAAYA,YAAW,WAAW;AAE9D,QAAI,UAAU;AACZ,UAAI,KAAK,QAAQ;AAAA,IACnB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,YAAY,OAAO,IAAI,UAAU,CAAC,IAAI;AAC/C;AAEA,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,eAAe;AACrB,IAAI,UAAU,SAAS6B,SAAQ,OAAO;AACpC,MAAI,OAAO,UAAU,SAAS,CAAC,IAAI,OAC/B,8BAA8B,KAAK,SACnC,WAAW,KAAK;AAEpB,mBAAiB,QAAQ,SAAU,UAAU;AAC3C,QAAI,aAAa;AAEjB,QAAI,6BAA6B;AAC/B,mBAAa,mBAAmB,2BAA2B,IAAI,SAAS,cAAc,8BAA8B,SAAS,WAAW,4BAA4B;AAAA,IACtK;AAEA,QAAI,CAAC,YAAY;AACf,UAAI,mBAAmB,SAAS,MAAM;AACtC,eAAS,SAAS;AAAA,QAChB;AAAA,MACF,CAAC;AACD,eAAS,KAAK;AAEd,UAAI,CAAC,SAAS,MAAM,aAAa;AAC/B,iBAAS,SAAS;AAAA,UAChB,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAMA,IAAI,sBAAsB,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA,EACzD,QAAQ,SAASjB,QAAO,MAAM;AAC5B,QAAI,QAAQ,KAAK;AACjB,QAAI,gBAAgB;AAAA,MAClB,QAAQ;AAAA,QACN,UAAU,MAAM,QAAQ;AAAA,QACxB,MAAM;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC;AAAA,IACd;AACA,WAAO,OAAO,MAAM,SAAS,OAAO,OAAO,cAAc,MAAM;AAC/D,UAAM,SAAS;AAEf,QAAI,MAAM,SAAS,OAAO;AACxB,aAAO,OAAO,MAAM,SAAS,MAAM,OAAO,cAAc,KAAK;AAAA,IAC/D;AAAA,EAGF;AACF,CAAC;AAED,IAAI,kBAAkB,SAASkB,iBAAgB,gBAAgB,eAAe;AAC5E,MAAI;AAEJ,MAAI,kBAAkB,QAAQ;AAC5B,oBAAgB,CAAC;AAAA,EACnB;AAEA,MAAI,sBAAsB;AAC1B,MAAI,aAAa,CAAC;AAClB,MAAI,iBAAiB,CAAC;AACtB,MAAI;AACJ,MAAI,YAAY,cAAc;AAC9B,MAAI,4BAA4B,CAAC;AACjC,MAAI,gBAAgB;AAEpB,WAAS,oBAAoB;AAC3B,qBAAiB,oBAAoB,IAAI,SAAU,UAAU;AAC3D,aAAO,iBAAiB,SAAS,MAAM,iBAAiB,SAAS,SAAS;AAAA,IAC5E,CAAC,EAAE,OAAO,SAAU,KAAK,MAAM;AAC7B,aAAO,IAAI,OAAO,IAAI;AAAA,IACxB,GAAG,CAAC,CAAC;AAAA,EACP;AAEA,WAAS,gBAAgB;AACvB,iBAAa,oBAAoB,IAAI,SAAU,UAAU;AACvD,aAAO,SAAS;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,WAAS,gBAAgB,WAAW;AAClC,wBAAoB,QAAQ,SAAU,UAAU;AAC9C,UAAI,WAAW;AACb,iBAAS,OAAO;AAAA,MAClB,OAAO;AACL,iBAAS,QAAQ;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,kBAAkBC,YAAW;AACpC,WAAO,oBAAoB,IAAI,SAAU,UAAU;AACjD,UAAIC,oBAAmB,SAAS;AAEhC,eAAS,WAAW,SAAUjB,QAAO;AACnC,QAAAiB,kBAAiBjB,MAAK;AAEtB,YAAI,SAAS,cAAc,eAAe;AACxC,UAAAgB,WAAU,SAAShB,MAAK;AAAA,QAC1B;AAAA,MACF;AAEA,aAAO,WAAY;AACjB,iBAAS,WAAWiB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAGA,WAAS,gBAAgBD,YAAW,QAAQ;AAC1C,QAAI,QAAQ,eAAe,QAAQ,MAAM;AAEzC,QAAI,WAAW,eAAe;AAC5B;AAAA,IACF;AAEA,oBAAgB;AAChB,QAAI,iBAAiB,aAAa,CAAC,GAAG,OAAO,SAAS,EAAE,OAAO,SAAU,KAAK,MAAM;AAClF,UAAI,IAAI,IAAI,oBAAoB,KAAK,EAAE,MAAM,IAAI;AACjD,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,IAAAA,WAAU,SAAS,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA,MAClD,wBAAwB,OAAO,cAAc,2BAA2B,aAAa,cAAc,yBAAyB,WAAY;AACtI,YAAI;AAEJ,gBAAQ,oBAAoB,WAAW,KAAK,MAAM,OAAO,SAAS,kBAAkB,sBAAsB;AAAA,MAC5G;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAEA,kBAAgB,KAAK;AACrB,gBAAc;AACd,oBAAkB;AAClB,MAAIb,UAAS;AAAA,IACX,IAAI,SAASZ,MAAK;AAChB,aAAO;AAAA,QACL,WAAW,SAAS2B,aAAY;AAC9B,0BAAgB,IAAI;AAAA,QACtB;AAAA,QACA,UAAU,SAASC,YAAW;AAC5B,0BAAgB;AAAA,QAClB;AAAA,QACA,gBAAgB,SAASC,gBAAe,UAAU;AAChD,cAAI,SAAS,MAAM,gBAAgB,CAAC,eAAe;AACjD,4BAAgB;AAChB,4BAAgB;AAAA,UAClB;AAAA,QACF;AAAA,QACA,QAAQ,SAASC,QAAO,UAAU;AAChC,cAAI,SAAS,MAAM,gBAAgB,CAAC,eAAe;AACjD,4BAAgB;AAChB,4BAAgB,UAAU,WAAW,CAAC,CAAC;AAAA,UACzC;AAAA,QACF;AAAA,QACA,WAAW,SAASV,WAAU,UAAU,OAAO;AAC7C,0BAAgB,UAAU,MAAM,aAAa;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,GAAG,iBAAiB,eAAe,CAAC,WAAW,CAAC,GAAG;AAAA,IAC7F,SAAS,CAACR,OAAM,EAAE,OAAO,cAAc,WAAW,CAAC,CAAC;AAAA,IACpD,eAAe;AAAA,IACf,eAAe,OAAO,OAAO,CAAC,GAAG,cAAc,eAAe;AAAA,MAC5D,WAAW,CAAC,EAAE,SAAS,wBAAwB,cAAc,kBAAkB,OAAO,SAAS,sBAAsB,cAAc,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAAA,IAC9J,CAAC;AAAA,EACH,CAAC,CAAC;AACF,MAAI,eAAe,UAAU;AAE7B,YAAU,OAAO,SAAU,QAAQ;AACjC,iBAAa;AAGb,QAAI,CAAC,iBAAiB,UAAU,MAAM;AACpC,aAAO,gBAAgB,WAAW,WAAW,CAAC,CAAC;AAAA,IACjD;AAIA,QAAI,iBAAiB,UAAU,MAAM;AACnC;AAAA,IACF;AAGA,QAAI,OAAO,WAAW,UAAU;AAC9B,aAAO,WAAW,MAAM,KAAK,gBAAgB,WAAW,WAAW,MAAM,CAAC;AAAA,IAC5E;AAGA,QAAI,oBAAoB,QAAQ,MAAM,KAAK,GAAG;AAC5C,UAAImB,OAAM,OAAO;AACjB,aAAO,gBAAgB,WAAWA,IAAG;AAAA,IACvC;AAGA,QAAI,WAAW,QAAQ,MAAM,KAAK,GAAG;AACnC,aAAO,gBAAgB,WAAW,MAAM;AAAA,IAC1C;AAAA,EACF;AAEA,YAAU,WAAW,WAAY;AAC/B,QAAI,QAAQ,WAAW,CAAC;AAExB,QAAI,CAAC,eAAe;AAClB,aAAO,UAAU,KAAK,CAAC;AAAA,IACzB;AAEA,QAAI,QAAQ,WAAW,QAAQ,aAAa;AAC5C,cAAU,KAAK,WAAW,QAAQ,CAAC,KAAK,KAAK;AAAA,EAC/C;AAEA,YAAU,eAAe,WAAY;AACnC,QAAI,OAAO,WAAW,WAAW,SAAS,CAAC;AAE3C,QAAI,CAAC,eAAe;AAClB,aAAO,UAAU,KAAK,IAAI;AAAA,IAC5B;AAEA,QAAI,QAAQ,WAAW,QAAQ,aAAa;AAC5C,QAAI,SAAS,WAAW,QAAQ,CAAC,KAAK;AACtC,cAAU,KAAK,MAAM;AAAA,EACvB;AAEA,MAAI,mBAAmB,UAAU;AAEjC,YAAU,WAAW,SAAUtB,QAAO;AACpC,gBAAYA,OAAM,aAAa;AAC/B,qBAAiBA,MAAK;AAAA,EACxB;AAEA,YAAU,eAAe,SAAU,eAAe;AAChD,oBAAgB,IAAI;AACpB,8BAA0B,QAAQ,SAAUT,KAAI;AAC9C,aAAOA,IAAG;AAAA,IACZ,CAAC;AACD,0BAAsB;AACtB,oBAAgB,KAAK;AACrB,kBAAc;AACd,sBAAkB;AAClB,gCAA4B,kBAAkB,SAAS;AACvD,cAAU,SAAS;AAAA,MACjB,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,8BAA4B,kBAAkB,SAAS;AACvD,SAAO;AACT;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,IAAI,SAASA,IAAG,UAAU;AACxB,QAAI;AAGJ,QAAI,GAAG,wBAAwB,SAAS,MAAM,WAAW,QAAQ,sBAAsB,UAAU;AAE/F,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,eAAe,YAAY,SAAS,MAAM,GAC1C,MAAM,aAAa,KACnB,UAAU,aAAa;AAE3B,QAAI,WAAW,SAAS,MAAM,cAAc,sBAAsB,IAAI;AACtE,WAAO;AAAA,MACL,UAAU,SAASgC,YAAW;AAC5B,YAAI,UAAU;AACZ,cAAI,aAAa,UAAU,IAAI,iBAAiB;AAChD,cAAI,aAAa,oBAAoB,EAAE;AACvC,cAAI,MAAM,WAAW;AACrB,mBAAS,SAAS;AAAA,YAChB,OAAO;AAAA,YACP,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,SAAS,SAASC,WAAU;AAC1B,YAAI,UAAU;AACZ,cAAI,qBAAqB,IAAI,MAAM;AACnC,cAAI,WAAW,OAAO,mBAAmB,QAAQ,MAAM,EAAE,CAAC;AAI1D,kBAAQ,MAAM,kBAAkB,KAAK,MAAM,WAAW,EAAE,IAAI;AAC5D,mBAAS,MAAM,qBAAqB;AACpC,6BAAmB,CAAC,QAAQ,GAAG,SAAS;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,QAAQ,SAASH,UAAS;AACxB,YAAI,UAAU;AACZ,mBAAS,MAAM,qBAAqB;AAAA,QACtC;AAAA,MACF;AAAA,MACA,QAAQ,SAASI,UAAS;AACxB,YAAI,UAAU;AACZ,6BAAmB,CAAC,QAAQ,GAAG,QAAQ;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB;AAC/B,MAAI,WAAW,IAAI;AACnB,WAAS,YAAY;AACrB,qBAAmB,CAAC,QAAQ,GAAG,QAAQ;AACvC,SAAO;AACT;AAEA,IAAI,cAAc;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAI,kBAAkB,CAAC;AAEvB,SAAS,iBAAiB,MAAM;AAC9B,MAAI,UAAU,KAAK,SACf,UAAU,KAAK;AACnB,gBAAc;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,uBAAuB,KAAK;AACnC,MAAI,iBAAiB,aAAa,gBAAgB;AACpD;AAEA,SAAS,0BAA0B,KAAK;AACtC,MAAI,oBAAoB,aAAa,gBAAgB;AACvD;AAEA,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,IAAI,SAASlC,IAAG,UAAU;AACxB,QAAIN,aAAY,SAAS;AACzB,QAAI,MAAM,iBAAiB,SAAS,MAAM,iBAAiBA,UAAS;AACpE,QAAI,mBAAmB;AACvB,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAClB,QAAI,YAAY,SAAS;AAEzB,aAAS,uBAAuB;AAC9B,aAAO,SAAS,MAAM,iBAAiB,aAAa,SAAS,MAAM;AAAA,IACrE;AAEA,aAAS,cAAc;AACrB,UAAI,iBAAiB,aAAa,WAAW;AAAA,IAC/C;AAEA,aAAS,iBAAiB;AACxB,UAAI,oBAAoB,aAAa,WAAW;AAAA,IAClD;AAEA,aAAS,8BAA8B;AACrC,yBAAmB;AACnB,eAAS,SAAS;AAAA,QAChB,wBAAwB;AAAA,MAC1B,CAAC;AACD,yBAAmB;AAAA,IACrB;AAEA,aAAS,YAAY,OAAO;AAG1B,UAAI,wBAAwB,MAAM,SAASA,WAAU,SAAS,MAAM,MAAM,IAAI;AAC9E,UAAIyC,gBAAe,SAAS,MAAM;AAClC,UAAI,UAAU,MAAM,SAChB,UAAU,MAAM;AACpB,UAAI,OAAOzC,WAAU,sBAAsB;AAC3C,UAAI,YAAY,UAAU,KAAK;AAC/B,UAAI,YAAY,UAAU,KAAK;AAE/B,UAAI,yBAAyB,CAAC,SAAS,MAAM,aAAa;AACxD,iBAAS,SAAS;AAAA;AAAA,UAEhB,wBAAwB,SAAS,yBAAyB;AACxD,gBAAI0C,QAAO1C,WAAU,sBAAsB;AAC3C,gBAAI,IAAI;AACR,gBAAI,IAAI;AAER,gBAAIyC,kBAAiB,WAAW;AAC9B,kBAAIC,MAAK,OAAO;AAChB,kBAAIA,MAAK,MAAM;AAAA,YACjB;AAEA,gBAAIC,OAAMF,kBAAiB,eAAeC,MAAK,MAAM;AACrD,gBAAIE,SAAQH,kBAAiB,aAAaC,MAAK,QAAQ;AACvD,gBAAIG,UAASJ,kBAAiB,eAAeC,MAAK,SAAS;AAC3D,gBAAII,QAAOL,kBAAiB,aAAaC,MAAK,OAAO;AACrD,mBAAO;AAAA,cACL,OAAOE,SAAQE;AAAA,cACf,QAAQD,UAASF;AAAA,cACjB,KAAKA;AAAA,cACL,OAAOC;AAAA,cACP,QAAQC;AAAA,cACR,MAAMC;AAAA,YACR;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS,SAAS;AAChB,UAAI,SAAS,MAAM,cAAc;AAC/B,wBAAgB,KAAK;AAAA,UACnB;AAAA,UACA;AAAA,QACF,CAAC;AACD,+BAAuB,GAAG;AAAA,MAC5B;AAAA,IACF;AAEA,aAAS,UAAU;AACjB,wBAAkB,gBAAgB,OAAO,SAAU,MAAM;AACvD,eAAO,KAAK,aAAa;AAAA,MAC3B,CAAC;AAED,UAAI,gBAAgB,OAAO,SAAU,MAAM;AACzC,eAAO,KAAK,QAAQ;AAAA,MACtB,CAAC,EAAE,WAAW,GAAG;AACf,kCAA0B,GAAG;AAAA,MAC/B;AAAA,IACF;AAEA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,SAASC,kBAAiB;AACxC,oBAAY,SAAS;AAAA,MACvB;AAAA,MACA,eAAe,SAASC,eAAc,GAAG,OAAO;AAC9C,YAAIP,gBAAe,MAAM;AAEzB,YAAI,kBAAkB;AACpB;AAAA,QACF;AAEA,YAAIA,kBAAiB,UAAa,UAAU,iBAAiBA,eAAc;AACzE,kBAAQ;AAER,cAAIA,eAAc;AAChB,mBAAO;AAEP,gBAAI,SAAS,MAAM,aAAa,CAAC,iBAAiB,CAAC,qBAAqB,GAAG;AACzE,0BAAY;AAAA,YACd;AAAA,UACF,OAAO;AACL,2BAAe;AACf,wCAA4B;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS,SAASF,WAAU;AAC1B,YAAI,SAAS,MAAM,gBAAgB,CAAC,eAAe;AACjD,cAAI,aAAa;AACf,wBAAY,WAAW;AACvB,0BAAc;AAAA,UAChB;AAEA,cAAI,CAAC,qBAAqB,GAAG;AAC3B,wBAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,WAAW,SAASb,WAAU,GAAG,OAAO;AACtC,YAAI,aAAa,KAAK,GAAG;AACvB,wBAAc;AAAA,YACZ,SAAS,MAAM;AAAA,YACf,SAAS,MAAM;AAAA,UACjB;AAAA,QACF;AAEA,wBAAgB,MAAM,SAAS;AAAA,MACjC;AAAA,MACA,UAAU,SAASQ,YAAW;AAC5B,YAAI,SAAS,MAAM,cAAc;AAC/B,sCAA4B;AAC5B,yBAAe;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,SAASnB,QAAO,UAAU;AACjC,MAAI;AAEJ,SAAO;AAAA,IACL,eAAe,OAAO,OAAO,CAAC,GAAGA,OAAM,eAAe;AAAA,MACpD,WAAW,CAAC,EAAE,UAAU,uBAAuBA,OAAM,kBAAkB,OAAO,SAAS,qBAAqB,cAAc,CAAC,GAAG,OAAO,SAAU,MAAM;AACnJ,YAAI,OAAO,KAAK;AAChB,eAAO,SAAS,SAAS;AAAA,MAC3B,CAAC,GAAG,CAAC,QAAQ,CAAC;AAAA,IAChB,CAAC;AAAA,EACH;AACF;AAEA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,IAAI,SAAST,IAAG,UAAU;AACxB,QAAIN,aAAY,SAAS;AAEzB,aAAS,YAAY;AACnB,aAAO,CAAC,CAAC,SAAS,MAAM;AAAA,IAC1B;AAEA,QAAI;AACJ,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AACvB,QAAI,kBAAkB,CAAC;AACvB,QAAI,WAAW;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,IAAI,SAASM,IAAG,OAAO;AACrB,YAAI,QAAQ,MAAM;AAElB,YAAI,UAAU,GAAG;AACf,cAAI,gBAAgB,QAAQ,MAAM,SAAS,MAAM,IAAI;AACnD,8BAAkB,CAAC;AAAA,UACrB;AAEA,cAAI,cAAc,MAAM,aAAa,gBAAgB,QAAQ,MAAM,SAAS,MAAM,IAAI;AACpF,4BAAgB,KAAK,MAAM,SAAS;AACpC,qBAAS,SAAS;AAAA;AAAA,cAEhB,wBAAwB,SAAS,yBAAyB;AACxD,uBAAO,wBAAwB,MAAM,SAAS;AAAA,cAChD;AAAA,YACF,CAAC;AAAA,UACH;AAEA,sBAAY,MAAM;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,wBAAwBL,YAAW;AAC1C,aAAO,4BAA4B,mBAAmBA,UAAS,GAAGD,WAAU,sBAAsB,GAAG,UAAUA,WAAU,eAAe,CAAC,GAAG,eAAe;AAAA,IAC7J;AAEA,aAAS,iBAAiB,cAAc;AACtC,yBAAmB;AACnB,eAAS,SAAS,YAAY;AAC9B,yBAAmB;AAAA,IACrB;AAEA,aAAS,cAAc;AACrB,UAAI,CAAC,kBAAkB;AACrB,yBAAiB,SAAS,SAAS,OAAO,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF;AAEA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,WAAW,SAAS0B,WAAU,GAAG,OAAO;AACtC,YAAI,aAAa,KAAK,GAAG;AACvB,cAAI,QAAQ,UAAU,SAAS,UAAU,eAAe,CAAC;AACzD,cAAI,aAAa,MAAM,KAAK,SAAU,MAAM;AAC1C,mBAAO,KAAK,OAAO,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,WAAW,KAAK,SAAS,KAAK,MAAM;AAAA,UACxI,CAAC;AACD,cAAI,QAAQ,MAAM,QAAQ,UAAU;AACpC,4BAAkB,QAAQ,KAAK,QAAQ;AAAA,QACzC;AAAA,MACF;AAAA,MACA,UAAU,SAASQ,YAAW;AAC5B,0BAAkB;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,4BAA4B,sBAAsB,cAAc,aAAa,iBAAiB;AAErG,MAAI,YAAY,SAAS,KAAK,yBAAyB,MAAM;AAC3D,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,WAAW,KAAK,mBAAmB,KAAK,YAAY,CAAC,EAAE,OAAO,YAAY,CAAC,EAAE,OAAO;AAClG,WAAO,YAAY,eAAe,KAAK;AAAA,EACzC;AAEA,UAAQ,sBAAsB;AAAA,IAC5B,KAAK;AAAA,IACL,KAAK,UACH;AACE,UAAI,YAAY,YAAY,CAAC;AAC7B,UAAI,WAAW,YAAY,YAAY,SAAS,CAAC;AACjD,UAAI,QAAQ,yBAAyB;AACrC,UAAIS,OAAM,UAAU;AACpB,UAAIE,UAAS,SAAS;AACtB,UAAIC,QAAO,QAAQ,UAAU,OAAO,SAAS;AAC7C,UAAIF,SAAQ,QAAQ,UAAU,QAAQ,SAAS;AAC/C,UAAI,QAAQA,SAAQE;AACpB,UAAI,SAASD,UAASF;AACtB,aAAO;AAAA,QACL,KAAKA;AAAA,QACL,QAAQE;AAAA,QACR,MAAMC;AAAA,QACN,OAAOF;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEF,KAAK;AAAA,IACL,KAAK,SACH;AACE,UAAI,UAAU,KAAK,IAAI,MAAM,MAAM,YAAY,IAAI,SAAU,OAAO;AAClE,eAAO,MAAM;AAAA,MACf,CAAC,CAAC;AACF,UAAI,WAAW,KAAK,IAAI,MAAM,MAAM,YAAY,IAAI,SAAU,OAAO;AACnE,eAAO,MAAM;AAAA,MACf,CAAC,CAAC;AACF,UAAI,eAAe,YAAY,OAAO,SAAU,MAAM;AACpD,eAAO,yBAAyB,SAAS,KAAK,SAAS,UAAU,KAAK,UAAU;AAAA,MAClF,CAAC;AACD,UAAI,OAAO,aAAa,CAAC,EAAE;AAC3B,UAAI,UAAU,aAAa,aAAa,SAAS,CAAC,EAAE;AACpD,UAAI,QAAQ;AACZ,UAAI,SAAS;AAEb,UAAI,SAAS,SAAS;AAEtB,UAAI,UAAU,UAAU;AAExB,aAAO;AAAA,QACL,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,IAEF,SACE;AACE,aAAO;AAAA,IACT;AAAA,EACJ;AACF;AAEA,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,cAAc;AAAA,EACd,IAAI,SAAStC,IAAG,UAAU;AACxB,QAAIN,aAAY,SAAS,WACrBF,UAAS,SAAS;AAEtB,aAAS,eAAe;AACtB,aAAO,SAAS,iBAAiB,SAAS,eAAe,MAAM,SAAS,YAAYE;AAAA,IACtF;AAEA,aAAS,YAAY,OAAO;AAC1B,aAAO,SAAS,MAAM,WAAW,QAAQ,SAAS,MAAM,WAAW;AAAA,IACrE;AAEA,QAAI,cAAc;AAClB,QAAI,cAAc;AAElB,aAAS,iBAAiB;AACxB,UAAI,iBAAiB,YAAY,WAAW,IAAI,aAAa,EAAE,sBAAsB,IAAI;AACzF,UAAI,iBAAiB,YAAY,QAAQ,IAAIF,QAAO,sBAAsB,IAAI;AAE9E,UAAI,kBAAkB,kBAAkB,aAAa,cAAc,KAAK,kBAAkB,kBAAkB,aAAa,cAAc,GAAG;AACxI,YAAI,SAAS,gBAAgB;AAC3B,mBAAS,eAAe,OAAO;AAAA,QACjC;AAAA,MACF;AAEA,oBAAc;AACd,oBAAc;AAEd,UAAI,SAAS,MAAM,WAAW;AAC5B,8BAAsB,cAAc;AAAA,MACtC;AAAA,IACF;AAEA,WAAO;AAAA,MACL,SAAS,SAASyC,WAAU;AAC1B,YAAI,SAAS,MAAM,QAAQ;AACzB,yBAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,OAAO,OAAO;AACvC,MAAI,SAAS,OAAO;AAClB,WAAO,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU,MAAM,SAAS,MAAM,WAAW,MAAM,UAAU,MAAM,SAAS,MAAM;AAAA,EACzH;AAEA,SAAO;AACT;AAEA,MAAM,gBAAgB;AAAA,EACpB;AACF,CAAC;AAED,MAAM,gBAAgB;AAAA;AAAA,EAElB,QAAQ,cAAY;AAChB,QAAI,CAAC,SAAS,MAAM;AAChB,aAAO;AAAA,EACf;AACJ,CAAC;AACD,IAAM,sBAAsB,CAAC,UAAU;AACnC,SAAO,iBAAiB,UAAU,OAAO,SAAS,SAAS;AAC/D;AACA,SAAS,SAAS,IAAI,OAAO,CAAC,GAAG,WAAW,EAAE,OAAO,MAAM,SAAS,QAAQ,GAAG;AAC3E,aAAW,OAAO,OAAO,EAAE,OAAO,MAAM,SAAS,QAAQ,GAAG,QAAQ;AACpE,QAAM,KAAK,mBAAmB;AAC9B,QAAM,WAAW,IAAI;AACrB,QAAM,QAAQ,IAAI;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,SAAS;AAAA,EACb,CAAC;AACD,QAAM,cAAc,WAAW;AAC/B,MAAI,YAAY;AAChB,QAAM,eAAe,MAAM;AACvB,QAAI;AACA,aAAO;AACX,gBAAY,SAAS,uBAAuB;AAC5C,WAAO;AAAA,EACX;AACA,QAAM,aAAa,CAAC,YAAY;AAC5B,QAAI;AACJ,QAAI,mBAAmB,MAAM,OAAO,IAC9B,QAAQ,QACR;AACN,QAAI,QAAQ,gBAAgB,GAAG;AAC3B,UAAI,CAAC,YAAY,OAAO;AACpB,oBAAY,QAAQ,UAAU;AAAA,UAC1B,MAAM,SAAS;AAAA,UACf,OAAO,MAAM;AACT,mBAAO,MAAM,MAAM,OAAO,IAAI,QAAQ,QAAQ;AAAA,UAClD;AAAA,QACJ,CAAC;AACD,YAAI,IAAI;AACJ,iBAAO,OAAO,YAAY,MAAM,UAAU,GAAG,UAAU;AAAA,QAC3D;AACA,oBAAY,MAAM,MAAM,aAAa,CAAC;AAAA,MAC1C;AACA,mBAAa,MAAM,aAAa;AAAA,IACpC,WACS,OAAO,qBAAqB,UAAU;AAC3C,UAAI,CAAC,YAAY,OAAO;AACpB,oBAAY,QAAQ,UAAU;AAAA,UAC1B,MAAM,SAAS;AAAA,UACf,OAAO,MAAM;AACT,mBAAO,MAAM,EAAE,MAAM,OAAO,IAAI,QAAQ,QAAQ,OAAO;AAAA,UAC3D;AAAA,QACJ,CAAC;AACD,YAAI,IAAI;AACJ,iBAAO,OAAO,YAAY,MAAM,UAAU,GAAG,UAAU;AAAA,QAC3D;AACA,oBAAY,MAAM,MAAM,aAAa,CAAC;AAAA,MAC1C;AACA,mBAAa,MAAM,aAAa;AAAA,IACpC,OACK;AACD,mBAAa;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AACA,QAAMU,YAAW,CAACC,UAAS;AACvB,QAAI,UAAU,CAAC;AACf,QAAI,MAAMA,KAAI,GAAG;AACb,gBAAUA,MAAK,SAAS,CAAC;AAAA,IAC7B,WACS,WAAWA,KAAI,GAAG;AACvB,gBAAU,EAAE,GAAGA,MAAK;AAAA,IACxB,OACK;AACD,gBAAU,EAAE,GAAGA,MAAK;AAAA,IACxB;AACA,QAAI,QAAQ,SAAS;AACjB,cAAQ,UAAU,WAAW,QAAQ,OAAO;AAAA,IAChD;AACA,QAAI,QAAQ,eAAe;AACvB,cAAQ,gBAAgB,MAAM,QAAQ,aAAa,IAC7C,QAAQ,cAAc,QACtB,QAAQ;AAAA,IAClB;AACA,QAAI,CAAC,QAAQ,WAAW,CAAC,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACrD,cAAQ,UAAU,CAAC;AAAA,IACvB;AACA,YAAQ,UAAU,QAAQ,QAAQ,OAAO,CAAChC,YAAWA,QAAO,SAAS,uBAAuB;AAC5F,YAAQ,QAAQ,KAAK;AAAA,MACjB,MAAM;AAAA,MACN,IAAI,MAAM;AACN,eAAO;AAAA,UACH,WAAW;AACP,kBAAM,MAAM,YAAY;AAAA,UAC5B;AAAA,UACA,UAAU;AACN,kBAAM,MAAM,YAAY;AAAA,UAC5B;AAAA,UACA,SAAS;AACL,kBAAM,MAAM,YAAY;AACxB,kBAAM,MAAM,YAAY;AAAA,UAC5B;AAAA,UACA,UAAU;AACN,kBAAM,MAAM,UAAU;AAAA,UAC1B;AAAA,UACA,SAAS;AACL,kBAAM,MAAM,YAAY;AACxB,kBAAM,MAAM,YAAY;AAAA,UAC5B;AAAA,UACA,WAAW;AACP,kBAAM,MAAM,UAAU;AAAA,UAC1B;AAAA,UACA,cAAc;AACV,kBAAM,MAAM,YAAY;AAAA,UAC5B;AAAA,UACA,YAAY;AACR,kBAAM,MAAM,cAAc;AAAA,UAC9B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACA,QAAM,UAAU,MAAM;AAClB,QAAI,CAAC,SAAS;AACV;AACJ,aAAS,MAAM,SAAS+B,UAAS,IAAI,CAAC;AAAA,EAC1C;AACA,QAAM,iBAAiB,MAAM;AACzB,QAAI,CAAC,SAAS,SAAS,CAAC,KAAK;AACzB;AACJ,aAAS,MAAM,WAAW,WAAW,KAAK,OAAO,CAAC;AAAA,EACtD;AACA,QAAM1B,cAAa,CAAC,UAAU;AAC1B,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,WAAW,KAAK,CAAC;AAAA,EAC9F;AACA,QAAM,WAAW,CAAC,UAAU;AACxB,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS0B,UAAS,KAAK,CAAC;AAAA,EAC1F;AACA,QAAM,UAAU,MAAM;AAClB,QAAI;AACJ,QAAI,SAAS,OAAO;AAChB,eAAS,MAAM,QAAQ;AACvB,eAAS,QAAQ;AAAA,IACrB;AACA,gBAAY;AACZ,KAAC,KAAK,YAAY,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACzE,gBAAY,QAAQ;AAAA,EACxB;AACA,QAAM,OAAO,MAAM;AACf,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,EACvE;AACA,QAAMzB,QAAO,MAAM;AACf,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,EACvE;AACA,QAAM,UAAU,MAAM;AAClB,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACtE,UAAM,MAAM,YAAY;AAAA,EAC5B;AACA,QAAM,SAAS,MAAM;AACjB,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACrE,UAAM,MAAM,YAAY;AAAA,EAC5B;AACA,QAAM,UAAU,MAAM;AAClB,QAAI;AACJ,KAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAAA,EAC1E;AACA,QAAM,QAAQ,MAAM;AAChB,QAAI,CAAC;AACD;AACJ,QAAI,SAAS,MAAM,EAAE,IAAI,GAAG,QAAQ;AACpC,QAAI,OAAO,WAAW;AAClB,eAAS,OAAO;AACpB,QAAI,oBAAoB,MAAM,GAAG;AAC7B,eAAS,OAAO;AAAA,IACpB;AACA,QAAI,QAAQ;AAER,eAAS,QAAQ,MAAM,QAAQyB,UAAS,IAAI,CAAC;AAE7C,aAAO,SAAS;AAAA,IACpB;AAAA,EACJ;AACA,QAAM,WAAW;AAAA,IACb,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,YAAA1B;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAI,SAAS,OAAO;AAChB,QAAI,IAAI;AACJ,UAAI,GAAG,WAAW;AACd,cAAM;AAAA,MACV,OACK;AACD,kBAAU,KAAK;AAAA,MACnB;AAAA,IACJ,OACK;AACD,YAAM;AAAA,IACV;AAAA,EACJ;AACA,MAAI,IAAI;AACJ,gBAAY,MAAM;AACd,cAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AACA,MAAI,MAAM,IAAI,KAAK,WAAW,IAAI,GAAG;AACjC,UAAM,MAAM,SAAS,EAAE,WAAW,MAAM,CAAC;AAAA,EAC7C,WACS,MAAM,KAAK,OAAO,GAAG;AAC1B,UAAM,KAAK,SAAS,gBAAgB,EAAE,WAAW,MAAM,CAAC;AAAA,EAC5D;AACA,SAAO;AACX;AAEA,SAAS,kBAAkB,OAAO,CAAC,GAAG,UAAU;AAC5C,QAAM,WAAW,IAAI;AACrB,SAAO;AAAA,IACH;AAAA,IACA,gBAAgB,EAAE,gBAAgB;AAAA,MAC9B,GAAG;AAAA,MACH,gBAAgB,CAAC,UAAU;AAEvB,iBAAS,QAAQ,MAAM,UAAU;AAAA,MACrC;AAAA,IACJ,GAAG,QAAQ;AAAA,EACf;AACJ;AAEA,SAAS,aAAa,WAAW,eAAe;AAC5C,QAAM,YAAY,IAAI;AACtB,YAAU,MAAM;AACZ,UAAM,wBAAwB,MAAM,QAAQ,SAAS,IAC/C,UAAU,IAAI,OAAK,EAAE,KAAK,IAC1B,OAAO,cAAc,aACjB,UAAU,IACV,UAAU;AACpB,UAAM,iBAAiB,sBAClB,IAAI,CAAC,aAAa;AACnB,UAAI,oBAAoB,SAAS;AAE7B,eAAO,SAAS;AAAA,MACpB;AACA,aAAO;AAAA,IACX,CAAC,EACI,OAAO,OAAO;AACnB,cAAU,QAAQ,gBAAgB,gBAAgB,gBAC5C,EAAE,WAAW,MAAM,GAAG,cAAc,IACpC,EAAE,WAAW,KAAK,CAAC;AAAA,EAC7B,CAAC;AACD,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;AAEA,SAAS,QAAQ,GAAG;AAChB,SAAO,OAAO,MAAM,aACd,EAAE,IACF,MAAM,CAAC;AACjB;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,IAAI;AACR,QAAM,QAAQ,QAAQ,KAAK;AAC3B,UAAQ,MAAM,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,QAAQ,OAAO,SAAS,KAAK;AAC5G;AACA,IAAM,iBAAiB,gBAAgB;AAAA,EACnC,OAAO;AAAA,IACH,IAAI;AAAA,MACA,MAAM,CAAC,QAAQ,QAAQ;AAAA,IAC3B;AAAA,IACA,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACb;AAAA,IACA,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACb;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,UAAU,EAAE,SAAS,MAAM,MAAM,aAAa,UAAU,EAAE;AAAA,IAC1D,MAAM,EAAE,SAAS,MAAM,MAAM,aAAa,MAAM,EAAE;AAAA,IAClD,OAAO,EAAE,SAAS,MAAM,MAAM,aAAa,OAAO,EAAE;AAAA,IACpD,UAAU,EAAE,SAAS,MAAM,MAAM,aAAa,UAAU,EAAE;AAAA,IAC1D,wBAAwB,EAAE,SAAS,MAAM,MAAM,aAAa,wBAAwB,EAAE;AAAA,IACtF,aAAa,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM,MAAM,aAAa,aAAa,EAAE;AAAA,IACzF,kBAAkB,EAAE,MAAM,SAAS,SAAS,MAAM,MAAM,aAAa,kBAAkB,EAAE;AAAA,IACzF,aAAa,EAAE,MAAM,SAAS,SAAS,MAAM,MAAM,aAAa,aAAa,EAAE;AAAA,IAC/E,mBAAmB,EAAE,SAAS,MAAM,MAAM,aAAa,mBAAmB,EAAE;AAAA,IAC5E,qBAAqB,EAAE,SAAS,MAAM,MAAM,aAAa,qBAAqB,EAAE;AAAA,IAChF,gBAAgB,EAAE,SAAS,MAAM,MAAM,aAAa,gBAAgB,EAAE;AAAA,IACtE,QAAQ,EAAE,SAAS,MAAM,MAAM,aAAa,QAAQ,EAAE;AAAA,IACtD,eAAe,EAAE,SAAS,MAAM,MAAM,aAAa,eAAe,EAAE;AAAA,IACpE,gBAAgB,EAAE,SAAS,MAAM,MAAM,aAAa,gBAAgB,EAAE;AAAA,IACtE,UAAU,EAAE,SAAS,MAAM,MAAM,aAAa,UAAU,EAAE;AAAA,IAC1D,WAAW,EAAE,SAAS,MAAM,MAAM,aAAa,WAAW,EAAE;AAAA,IAC5D,UAAU,EAAE,SAAS,MAAM,MAAM,aAAa,UAAU,EAAE;AAAA,IAC1D,QAAQ,EAAE,SAAS,MAAM,MAAM,aAAa,QAAQ,EAAE;AAAA,IACtD,SAAS,EAAE,SAAS,MAAM,MAAM,aAAa,SAAS,EAAE;AAAA,IACxD,QAAQ,EAAE,SAAS,MAAM,MAAM,aAAa,QAAQ,EAAE;AAAA,IACtD,SAAS,EAAE,SAAS,MAAM,MAAM,aAAa,SAAS,EAAE;AAAA,IACxD,WAAW,EAAE,SAAS,MAAM,MAAM,aAAa,WAAW,EAAE;AAAA,IAC5D,aAAa,EAAE,SAAS,MAAM,MAAM,aAAa,aAAa,EAAE;AAAA,IAChE,gBAAgB,EAAE,SAAS,MAAM,MAAM,aAAa,gBAAgB,EAAE;AAAA,IACtE,WAAW,EAAE,SAAS,MAAM,MAAM,aAAa,WAAW,EAAE;AAAA,IAC5D,SAAS,EAAE,SAAS,MAAM,MAAM,aAAa,SAAS,EAAE;AAAA,IACxD,eAAe,EAAE,SAAS,MAAM,MAAM,aAAa,eAAe,EAAE;AAAA,IACpE,QAAQ,EAAE,SAAS,MAAM,MAAM,aAAa,QAAQ,EAAE;AAAA,IACtD,cAAc,EAAE,MAAM,SAAS,SAAS,MAAM,MAAM,aAAa,cAAc,EAAE;AAAA,IACjF,OAAO,EAAE,MAAM,CAAC,SAAS,QAAQ,KAAK,GAAG,SAAS,MAAM,MAAM,aAAa,OAAO,EAAE;AAAA,IACpF,SAAS,EAAE,SAAS,MAAM,MAAM,aAAa,SAAS,EAAE;AAAA,IACxD,eAAe,EAAE,SAAS,MAAM,MAAM,aAAa,eAAe,EAAE;AAAA,IACpE,aAAa,EAAE,MAAM,SAAS,SAAS,MAAM,MAAM,aAAa,aAAa,EAAE;AAAA,IAC/E,cAAc,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM,MAAM,aAAa,cAAc,EAAE;AAAA,IAC3F,mBAAmB,EAAE,MAAM,SAAS,SAAS,MAAM,MAAM,aAAa,mBAAmB,EAAE;AAAA,IAC3F,QAAQ,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM,MAAM,aAAa,QAAQ,EAAE;AAAA,IAC/E,WAAW,EAAE,MAAM,SAAS,SAAS,MAAM,MAAM,aAAa,WAAW,EAAE;AAAA,IAC3E,WAAW,EAAE,SAAS,MAAM,MAAM,aAAa,WAAW,EAAE;AAAA,IAC5D,OAAO,EAAE,SAAS,MAAM,MAAM,aAAa,OAAO,EAAE;AAAA,IACpD,SAAS,EAAE,SAAS,MAAM,MAAM,aAAa,SAAS,EAAE;AAAA,IACxD,SAAS,EAAE,SAAS,MAAM,MAAM,aAAa,SAAS,EAAE;AAAA,IACxD,UAAU,EAAE,SAAS,MAAM,MAAM,aAAa,UAAU,EAAE;AAAA,IAC1D,MAAM,EAAE,SAAS,MAAM,MAAM,aAAa,MAAM,EAAE;AAAA,IAClD,OAAO,EAAE,SAAS,MAAM,MAAM,aAAa,OAAO,EAAE;AAAA,IACpD,QAAQ,EAAE,SAAS,MAAM,MAAM,aAAa,QAAQ,EAAE;AAAA,EAC1D;AAAA,EACA,OAAO,CAAC,OAAO;AAAA,EACf,MAAMT,QAAO,EAAE,OAAO,MAAM,OAAO,GAAG;AAClC,UAAM,OAAO,IAAI;AACjB,UAAM,mBAAmB,IAAI;AAC7B,UAAM,cAAc,IAAI;AACxB,UAAM,UAAU,IAAI,KAAK;AACzB,UAAM,aAAa,MAAM;AACrB,UAAI,UAAU,EAAE,GAAGA,OAAM;AACzB,iBAAW,QAAQ,CAAC,MAAM,OAAO,cAAc,cAAc,GAAG;AAC5D,YAAI,QAAQ,eAAe,IAAI,GAAG;AAE9B,iBAAO,QAAQ,IAAI;AAAA,QACvB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAI,SAAS,MAAM,aAAa,IAAI;AACpC,QAAIA,OAAM,IAAI;AACV,UAAI,OAAO,YAAY,eAAeA,OAAM,cAAc,SAAS;AAC/D,iBAAS,MAAMA,OAAM;AAAA,MACzB,WACSA,OAAM,OAAO,UAAU;AAC5B,iBAAS,MAAM;AACX,cAAI,KAAK,KAAK;AACd,cAAI,CAAC,IAAI;AACL,iBAAK,KAAK,QAAQ,iBAAiB,MAAM;AAAA,UAC7C;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,WACS,OAAOA,OAAM,OAAO,YAAYA,OAAM,cAAc,QAAQ;AACjE,iBAAS,MAAM,SAAS,cAAcA,OAAM,EAAE;AAAA,MAClD;AAAA,IACJ;AACA,UAAMoC,SAAQ,SAAS,QAAQ,WAAW,CAAC;AAC3C,QAAI,cAAc,MAAM;AACxB,QAAI,CAAC,eAAepC,OAAM,OAAO,UAAU;AACvC,oBAAc,MAAM;AAAA,IACxB;AACA,cAAU,MAAM;AACZ,cAAQ,QAAQ;AAChB,eAAS,MAAM;AACX,YAAI;AACA,UAAAoC,OAAM,WAAW,MAAM,YAAY,KAAK;AAAA,MAChD,CAAC;AAAA,IACL,CAAC;AACD,UAAMA,OAAM,OAAO,MAAM;AACrB,WAAK,SAAS,MAAMA,OAAM,KAAK,CAAC;AAAA,IACpC,GAAG,EAAE,WAAW,MAAM,MAAM,KAAK,CAAC;AAClC,UAAM,MAAMpC,QAAO,MAAM;AACrB,MAAAoC,OAAM,SAAS,WAAW,CAAC;AAC3B,UAAI;AACA,QAAAA,OAAM,WAAW,MAAM,YAAY,KAAK;AAAA,IAChD,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,QAAI,UAAU,SAAS;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAGA;AAAA,IACP,CAAC;AACD,WAAO,OAAO;AACd,WAAO,MAAM;AACT,YAAM,aAAa,OAAOpC,OAAM,eAAe,WAAWA,OAAM,aAAaA,OAAM;AACnF,YAAM,UAAU,cACV,EAAE,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,OAAO,EAAE,SAAS,QAAQ,QAAQ,YAAY,OAAO;AAAA,QACrD,OAAOA,OAAM;AAAA,MACjB,GAAG,YAAY,OAAO,CAAC,IACrB;AACN,UAAIA,OAAM,OAAO,UAAU;AACvB,cAAM,SAAS,CAAC;AAChB,YAAI,CAAC,KAAK,OAAO;AACb,gBAAM,uBAAuB,EAAE,QAAQ;AAAA,YACnC,KAAK;AAAA,YACL,gBAAgB;AAAA,YAChB,OAAO,EAAE,SAAS,OAAO;AAAA,UAC7B,CAAC;AACD,iBAAO,KAAK,oBAAoB;AAAA,QACpC;AACA,YAAI,SAAS;AACT,iBAAO,KAAK,OAAO;AAAA,QACvB;AACA,eAAO;AAAA,MACX;AACA,YAAM,OAAO,MAAM,UAAU,MAAM,QAAQ,OAAO,IAAI,CAAC;AACvD,UAAI,CAACA,OAAM,KAAK;AACZ,cAAM,UAAU,EAAE,KAAK,CAAC,GAAG;AAAA,UACvB,KAAK;AAAA,UAAM,gBAAgB;AAAA,QAC/B,CAAC;AACD,eAAO,UAAU,CAAC,SAAS,OAAO,IAAI;AAAA,MAC1C;AACA,YAAM,MAAM,OAAOA,OAAM,QAAQ,WAAWA,OAAM,MAAMA,OAAM;AAC9D,aAAO,EAAE,KAAK,EAAE,KAAK,MAAM,gBAAgB,GAAG,GAAG,UAAU,CAAC,MAAM,OAAO,IAAI,IAAI;AAAA,IACrF;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAI,QAAQ,CAAC;AACb,OAAO,KAAK,MAAM,YAAY,EAAE,QAAQ,CAAC,SAAS;AAC9C,MAAI,aAAa,SAAS,IAAI,GAAG;AAC7B,UAAM,IAAI,IAAI;AAAA,MACV,MAAM;AAAA,MACN,SAAS,WAAY;AACjB,eAAO,MAAM,aAAa,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ,OACK;AACD,UAAM,IAAI,IAAI;AAAA,MACV,SAAS,WAAY;AACjB,eAAO,MAAM,aAAa,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AACD,IAAM,iBAAiB,gBAAgB;AAAA,EACnC;AAAA,EACA,MAAMA,QAAO;AACT,UAAM,YAAY,IAAI,CAAC,CAAC;AACxB,UAAM,EAAE,UAAU,IAAI,aAAa,WAAWA,MAAK;AACnD,WAAO,EAAE,WAAW,UAAU;AAAA,EAClC;AAAA,EACA,UAAU;AACN,QAAI;AACJ,UAAM,SAAS,KAAK,IAAI;AACxB,UAAM,WAAW,OAAO,iBAAiB,gBAAgB;AACzD,SAAK,YAAY,MAAM,KAAK,QAAQ,EAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,EACrB,OAAO,OAAO;AACnB,KAAC,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,KAAK,SAAS;AAAA,EAC7F;AAAA,EACA,SAAS;AACL,QAAI,OAAO,KAAK,OAAO,UAAU,KAAK,OAAO,QAAQ,IAAI,CAAC;AAC1D,WAAO,EAAE,MAAM,IAAI;AAAA,EACvB;AACJ,CAAC;AAED,IAAM,YAAY;AAAA,EACd,QAAQ,IAAI,SAAS,OAAO;AACxB,UAAM,OAAO,OAAO,QAAQ,UAAU,WAAW,EAAE,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,CAAC;AAChG,UAAM,YAAY,OAAO,KAAK,QAAQ,aAAa,CAAC,CAAC;AACrD,UAAM,YAAY,UAAU,KAAK,cAAY,aAAa,OAAO;AACjE,UAAM,YAAY,UAAU,UAAU,cAAY,aAAa,OAAO,MAAM;AAC5E,QAAI,WAAW;AACX,WAAK,YAAY,KAAK,aAAa;AAAA,IACvC;AACA,QAAI,WAAW;AACX,WAAK,QAAQ,KAAK,UAAU,SAAY,KAAK,QAAQ;AAAA,IACzD;AACA,QAAI,MAAM,SAAS,MAAM,MAAM,aAAa;AACxC,WAAK,SAAS,YAAa,MAAM;AAC7B,YAAI;AACJ,gBAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,GAAG,IAAI;AAAA,MACzF;AAAA,IACJ;AACA,QAAI,MAAM,SAAS,MAAM,MAAM,cAAc;AACzC,WAAK,UAAU,YAAa,MAAM;AAC9B,YAAI;AACJ,gBAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,GAAG,IAAI;AAAA,MAC1F;AAAA,IACJ;AACA,QAAI,MAAM,SAAS,MAAM,MAAM,eAAe;AAC1C,WAAK,WAAW,YAAa,MAAM;AAC/B,YAAI;AACJ,gBAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,GAAG,IAAI;AAAA,MAC3F;AAAA,IACJ;AACA,QAAI,MAAM,SAAS,MAAM,MAAM,aAAa;AACxC,WAAK,SAAS,YAAa,MAAM;AAC7B,YAAI;AACJ,gBAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,GAAG,IAAI;AAAA,MACzF;AAAA,IACJ;AACA,QAAI,MAAM,SAAS,MAAM,MAAM,cAAc;AACzC,WAAK,UAAU,YAAa,MAAM;AAC9B,YAAI;AACJ,gBAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,GAAG,IAAI;AAAA,MAC1F;AAAA,IACJ;AACA,QAAI,GAAG,aAAa,OAAO,KAAK,CAAC,KAAK,SAAS;AAC3C,WAAK,UAAU,GAAG,aAAa,OAAO;AACtC,SAAG,gBAAgB,OAAO;AAAA,IAC9B;AACA,QAAI,GAAG,aAAa,SAAS,KAAK,CAAC,KAAK,SAAS;AAC7C,WAAK,UAAU,GAAG,aAAa,SAAS;AAAA,IAC5C;AACA,aAAS,IAAI,IAAI;AAAA,EACrB;AAAA,EACA,UAAU,IAAI;AACV,QAAI,GAAG,QAAQ;AACX,SAAG,OAAO,QAAQ;AAAA,IACtB,WACS,GAAG,QAAQ;AAChB,SAAG,OAAO,QAAQ;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,QAAQ,IAAI,SAAS;AACjB,UAAM,OAAO,OAAO,QAAQ,UAAU,WAAW,EAAE,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,CAAC;AAChG,QAAI,GAAG,aAAa,OAAO,KAAK,CAAC,KAAK,SAAS;AAC3C,WAAK,UAAU,GAAG,aAAa,OAAO;AACtC,SAAG,gBAAgB,OAAO;AAAA,IAC9B;AACA,QAAI,GAAG,aAAa,SAAS,KAAK,CAAC,KAAK,SAAS;AAC7C,WAAK,UAAU,GAAG,aAAa,SAAS;AAAA,IAC5C;AACA,QAAI,GAAG,QAAQ;AACX,SAAG,OAAO,SAAS,QAAQ,CAAC,CAAC;AAAA,IACjC,WACS,GAAG,QAAQ;AAChB,SAAG,OAAO,SAAS,QAAQ,CAAC,CAAC;AAAA,IACjC;AAAA,EACJ;AACJ;AAEA,IAAM,SAAS;AAAA,EACX,QAAQ,KAAK,UAAU,CAAC,GAAG;AACvB,UAAM,gBAAgB,QAAQ,gBAAgB,CAAC,CAAC;AAChD,QAAI,UAAU,QAAQ,aAAa,SAAS,SAAS;AACrD,QAAI,UAAU,QAAQ,aAAa,SAAS,cAAc;AAC1D,QAAI,UAAU,QAAQ,sBAAsB,mBAAmB,cAAc;AAAA,EACjF;AACJ;AAEA,IAAM,oBAAoB,MAAM;AAChC,kBAAkB;AAAA,EACd,kBAAkB;AAAA,EAClB,SAAS,CAAC,QAAQ,mBAAmB,cAAc,WAAW;AAClE,CAAC;AAED,IAAO,gCAAQ;", "names": ["name", "style", "window", "min", "max", "toPaddingObject", "popperOffsets", "offset", "popper", "clippingParents", "reference", "placement", "placements", "_loop", "_i", "checks", "fn", "merged", "defaultModifiers", "createPopper", "options", "state", "effect", "noopFn", "TIPPY_DEFAULT_APPEND_TO", "props", "setDefaultProps", "pluginProps", "plugin", "innerHTML", "arrow", "box", "content", "<PERSON><PERSON><PERSON><PERSON>", "hide", "id", "onTrigger", "instance", "onFirstUpdate", "hide<PERSON>ll", "createSingleton", "singleton", "originalSetProps", "onDestroy", "onHidden", "onClickOutside", "onShow", "ref", "onCreate", "onMount", "onHide", "followCursor", "rect", "top", "right", "bottom", "left", "onBeforeUpdate", "onAfterUpdate", "getProps", "opts", "tippy"]}