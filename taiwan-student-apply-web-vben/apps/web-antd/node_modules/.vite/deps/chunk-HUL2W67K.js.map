{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/form/FormItemContext.js"], "sourcesContent": ["import { watch, computed, inject, provide, ref, onBeforeUnmount, getCurrentInstance, defineComponent } from 'vue';\nimport devWarning from '../vc-util/devWarning';\nimport createContext from '../_util/createContext';\nconst ContextKey = Symbol('ContextProps');\nconst InternalContextKey = Symbol('InternalContextProps');\nexport const useProvideFormItemContext = function (props) {\n  let useValidation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : computed(() => true);\n  const formItemFields = ref(new Map());\n  const addFormItemField = (key, type) => {\n    formItemFields.value.set(key, type);\n    formItemFields.value = new Map(formItemFields.value);\n  };\n  const removeFormItemField = key => {\n    formItemFields.value.delete(key);\n    formItemFields.value = new Map(formItemFields.value);\n  };\n  const instance = getCurrentInstance();\n  watch([useValidation, formItemFields], () => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (useValidation.value && formItemFields.value.size > 1) {\n        devWarning(false, 'Form.Item', `FormItem can only collect one field item, you haved set ${[...formItemFields.value.values()].map(v => `\\`${v.name}\\``).join(', ')} ${formItemFields.value.size} field items.\n        You can set not need to be collected fields into \\`a-form-item-rest\\``);\n        let cur = instance;\n        while (cur.parent) {\n          console.warn('at', cur.type);\n          cur = cur.parent;\n        }\n      }\n    }\n  });\n  provide(ContextKey, props);\n  provide(InternalContextKey, {\n    addFormItemField,\n    removeFormItemField\n  });\n};\nconst defaultContext = {\n  id: computed(() => undefined),\n  onFieldBlur: () => {},\n  onFieldChange: () => {},\n  clearValidate: () => {}\n};\nconst defaultInternalContext = {\n  addFormItemField: () => {},\n  removeFormItemField: () => {}\n};\nexport const useInjectFormItemContext = () => {\n  const internalContext = inject(InternalContextKey, defaultInternalContext);\n  const formItemFieldKey = Symbol('FormItemFieldKey');\n  const instance = getCurrentInstance();\n  internalContext.addFormItemField(formItemFieldKey, instance.type);\n  onBeforeUnmount(() => {\n    internalContext.removeFormItemField(formItemFieldKey);\n  });\n  // We should prevent the passing of context for children\n  provide(InternalContextKey, defaultInternalContext);\n  provide(ContextKey, defaultContext);\n  return inject(ContextKey, defaultContext);\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AFormItemRest',\n  setup(_, _ref) {\n    let {\n      slots\n    } = _ref;\n    provide(InternalContextKey, defaultInternalContext);\n    provide(ContextKey, defaultContext);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport const FormItemInputContext = createContext({});\nexport const NoFormStatus = defineComponent({\n  name: 'NoFormStatus',\n  setup(_, _ref2) {\n    let {\n      slots\n    } = _ref2;\n    FormItemInputContext.useProvide({});\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,IAAM,aAAa,OAAO,cAAc;AACxC,IAAM,qBAAqB,OAAO,sBAAsB;AACjD,IAAM,4BAA4B,SAAU,OAAO;AACxD,MAAI,gBAAgB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,SAAS,MAAM,IAAI;AAC3G,QAAM,iBAAiB,IAAI,oBAAI,IAAI,CAAC;AACpC,QAAM,mBAAmB,CAAC,KAAK,SAAS;AACtC,mBAAe,MAAM,IAAI,KAAK,IAAI;AAClC,mBAAe,QAAQ,IAAI,IAAI,eAAe,KAAK;AAAA,EACrD;AACA,QAAM,sBAAsB,SAAO;AACjC,mBAAe,MAAM,OAAO,GAAG;AAC/B,mBAAe,QAAQ,IAAI,IAAI,eAAe,KAAK;AAAA,EACrD;AACA,QAAM,WAAW,mBAAmB;AACpC,QAAM,CAAC,eAAe,cAAc,GAAG,MAAM;AAC3C,QAAI,MAAuC;AACzC,UAAI,cAAc,SAAS,eAAe,MAAM,OAAO,GAAG;AACxD,2BAAW,OAAO,aAAa,2DAA2D,CAAC,GAAG,eAAe,MAAM,OAAO,CAAC,EAAE,IAAI,OAAK,KAAK,EAAE,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,eAAe,MAAM,IAAI;AAAA,8EACxH;AACtE,YAAI,MAAM;AACV,eAAO,IAAI,QAAQ;AACjB,kBAAQ,KAAK,MAAM,IAAI,IAAI;AAC3B,gBAAM,IAAI;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,UAAQ,YAAY,KAAK;AACzB,UAAQ,oBAAoB;AAAA,IAC1B;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAM,iBAAiB;AAAA,EACrB,IAAI,SAAS,MAAM,MAAS;AAAA,EAC5B,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,eAAe,MAAM;AAAA,EAAC;AACxB;AACA,IAAM,yBAAyB;AAAA,EAC7B,kBAAkB,MAAM;AAAA,EAAC;AAAA,EACzB,qBAAqB,MAAM;AAAA,EAAC;AAC9B;AACO,IAAM,2BAA2B,MAAM;AAC5C,QAAM,kBAAkB,OAAO,oBAAoB,sBAAsB;AACzE,QAAM,mBAAmB,OAAO,kBAAkB;AAClD,QAAM,WAAW,mBAAmB;AACpC,kBAAgB,iBAAiB,kBAAkB,SAAS,IAAI;AAChE,kBAAgB,MAAM;AACpB,oBAAgB,oBAAoB,gBAAgB;AAAA,EACtD,CAAC;AAED,UAAQ,oBAAoB,sBAAsB;AAClD,UAAQ,YAAY,cAAc;AAClC,SAAO,OAAO,YAAY,cAAc;AAC1C;AACA,IAAO,0BAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,MAAM,GAAG,MAAM;AACb,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,YAAQ,oBAAoB,sBAAsB;AAClD,YAAQ,YAAY,cAAc;AAClC,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AACM,IAAM,uBAAuB,sBAAc,CAAC,CAAC;AAC7C,IAAM,eAAe,gBAAgB;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM,GAAG,OAAO;AACd,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,yBAAqB,WAAW,CAAC,CAAC;AAClC,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;", "names": []}