import {
  getFuncText
} from "./chunk-PLEPSOXJ.js";
import {
  dynamicApp
} from "./chunk-TLDIGKI7.js";
import {
  VxeUI,
  createEvent,
  getConfig,
  getIcon,
  require_xe_utils,
  useSize
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import {
  computed,
  defineComponent,
  h,
  inject,
  reactive
} from "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/checkbox/src/checkbox.js
var import_xe_utils = __toESM(require_xe_utils());
var checkbox_default = defineComponent({
  name: "VxeCheckbox",
  props: {
    modelValue: [String, Number, Boolean],
    label: {
      type: [String, Number],
      default: null
    },
    indeterminate: Boolean,
    title: [String, Number],
    checkedValue: {
      type: [String, Number, Boolean],
      default: true
    },
    uncheckedValue: {
      type: [String, Number, Boolean],
      default: false
    },
    content: [String, Number],
    disabled: {
      type: Boolean,
      default: null
    },
    size: {
      type: String,
      default: () => getConfig().checkbox.size || getConfig().size
    }
  },
  emits: [
    "update:modelValue",
    "change"
  ],
  setup(props, context) {
    const { slots, emit } = context;
    const $xeForm = inject("$xeForm", null);
    const formItemInfo = inject("xeFormItemInfo", null);
    const $xeCheckboxGroup = inject("$xeCheckboxGroup", null);
    const xID = import_xe_utils.default.uniqueId();
    const reactData = reactive({});
    const $xeCheckbox = {
      xID,
      props,
      context,
      reactData
    };
    let checkboxMethods = {};
    const { computeSize } = useSize(props);
    const computeIsChecked = computed(() => {
      if ($xeCheckboxGroup) {
        return import_xe_utils.default.includes($xeCheckboxGroup.props.modelValue, props.label);
      }
      return props.modelValue === props.checkedValue;
    });
    const computeIsDisabled = computed(() => {
      const { disabled } = props;
      const isChecked = computeIsChecked.value;
      if (disabled === null) {
        if ($xeCheckboxGroup) {
          const { computeIsDisabled: computeIsGroupDisabled, computeIsMaximize: computeIsGroupMaximize } = $xeCheckboxGroup.getComputeMaps();
          const isGroupDisabled = computeIsGroupDisabled.value;
          const isGroupMaximize = computeIsGroupMaximize.value;
          return isGroupDisabled || isGroupMaximize && !isChecked;
        }
      }
      return disabled;
    });
    const changeEvent = (evnt) => {
      const { checkedValue, uncheckedValue } = props;
      const isDisabled = computeIsDisabled.value;
      if (!isDisabled) {
        const checked = evnt.target.checked;
        const value = checked ? checkedValue : uncheckedValue;
        const params = { checked, value, label: props.label };
        if ($xeCheckboxGroup) {
          $xeCheckboxGroup.handleChecked(params, evnt);
        } else {
          emit("update:modelValue", value);
          checkboxMethods.dispatchEvent("change", params, evnt);
          if ($xeForm && formItemInfo) {
            $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, value);
          }
        }
      }
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $checkbox: $xeCheckbox }, params));
    };
    checkboxMethods = {
      dispatchEvent
    };
    Object.assign($xeCheckbox, checkboxMethods);
    const renderVN = () => {
      const { label } = props;
      const vSize = computeSize.value;
      const isDisabled = computeIsDisabled.value;
      const isChecked = computeIsChecked.value;
      const indeterminate = !isChecked && props.indeterminate;
      return h("label", {
        key: label,
        class: ["vxe-checkbox", {
          [`size--${vSize}`]: vSize,
          "is--indeterminate": indeterminate,
          "is--disabled": isDisabled,
          "is--checked": isChecked
        }],
        title: props.title
      }, [
        h("input", {
          class: "vxe-checkbox--input",
          type: "checkbox",
          disabled: isDisabled,
          checked: isChecked,
          onChange: changeEvent
        }),
        h("span", {
          class: ["vxe-checkbox--icon", indeterminate ? getIcon().CHECKBOX_INDETERMINATE : isChecked ? getIcon().CHECKBOX_CHECKED : getIcon().CHECKBOX_UNCHECKED]
        }),
        h("span", {
          class: "vxe-checkbox--label"
        }, slots.default ? slots.default({}) : getFuncText(props.content))
      ]);
    };
    $xeCheckbox.renderVN = renderVN;
    return $xeCheckbox;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/checkbox/index.js
var VxeCheckbox = Object.assign(checkbox_default, {
  install(app) {
    app.component(checkbox_default.name, checkbox_default);
  }
});
dynamicApp.use(VxeCheckbox);
VxeUI.component(checkbox_default);
var Checkbox = VxeCheckbox;
var checkbox_default2 = VxeCheckbox;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-checkbox/index.js
var vxe_checkbox_default = checkbox_default2;
export {
  Checkbox,
  VxeCheckbox,
  vxe_checkbox_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-checkbox_index__js.js.map
