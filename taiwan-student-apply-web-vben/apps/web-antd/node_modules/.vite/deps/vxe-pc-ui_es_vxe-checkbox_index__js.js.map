{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/checkbox/src/checkbox.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/checkbox/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-checkbox/index.js"], "sourcesContent": ["import { defineComponent, h, computed, inject, reactive } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { getFuncText } from '../../ui/src/utils';\nimport { getConfig, createEvent, useSize, getIcon } from '../../ui';\nexport default defineComponent({\n    name: 'VxeCheckbox',\n    props: {\n        modelValue: [String, Number, Boolean],\n        label: {\n            type: [String, Number],\n            default: null\n        },\n        indeterminate: Boolean,\n        title: [String, Number],\n        checkedValue: {\n            type: [String, Number, Boolean],\n            default: true\n        },\n        uncheckedValue: {\n            type: [String, Number, Boolean],\n            default: false\n        },\n        content: [String, Number],\n        disabled: {\n            type: Boolean,\n            default: null\n        },\n        size: {\n            type: String,\n            default: () => getConfig().checkbox.size || getConfig().size\n        }\n    },\n    emits: [\n        'update:modelValue',\n        'change'\n    ],\n    setup(props, context) {\n        const { slots, emit } = context;\n        const $xeForm = inject('$xeForm', null);\n        const formItemInfo = inject('xeFormItemInfo', null);\n        const $xeCheckboxGroup = inject('$xeCheckboxGroup', null);\n        const xID = XEUtils.uniqueId();\n        const reactData = reactive({});\n        const $xeCheckbox = {\n            xID,\n            props,\n            context,\n            reactData\n        };\n        let checkboxMethods = {};\n        const { computeSize } = useSize(props);\n        const computeIsChecked = computed(() => {\n            if ($xeCheckboxGroup) {\n                return XEUtils.includes($xeCheckboxGroup.props.modelValue, props.label);\n            }\n            return props.modelValue === props.checkedValue;\n        });\n        const computeIsDisabled = computed(() => {\n            const { disabled } = props;\n            const isChecked = computeIsChecked.value;\n            if (disabled === null) {\n                if ($xeCheckboxGroup) {\n                    const { computeIsDisabled: computeIsGroupDisabled, computeIsMaximize: computeIsGroupMaximize } = $xeCheckboxGroup.getComputeMaps();\n                    const isGroupDisabled = computeIsGroupDisabled.value;\n                    const isGroupMaximize = computeIsGroupMaximize.value;\n                    return isGroupDisabled || (isGroupMaximize && !isChecked);\n                }\n            }\n            return disabled;\n        });\n        const changeEvent = (evnt) => {\n            const { checkedValue, uncheckedValue } = props;\n            const isDisabled = computeIsDisabled.value;\n            if (!isDisabled) {\n                const checked = evnt.target.checked;\n                const value = checked ? checkedValue : uncheckedValue;\n                const params = { checked, value, label: props.label };\n                if ($xeCheckboxGroup) {\n                    $xeCheckboxGroup.handleChecked(params, evnt);\n                }\n                else {\n                    emit('update:modelValue', value);\n                    checkboxMethods.dispatchEvent('change', params, evnt);\n                    // 自动更新校验状态\n                    if ($xeForm && formItemInfo) {\n                        $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, value);\n                    }\n                }\n            }\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $checkbox: $xeCheckbox }, params));\n        };\n        checkboxMethods = {\n            dispatchEvent\n        };\n        Object.assign($xeCheckbox, checkboxMethods);\n        const renderVN = () => {\n            const { label } = props;\n            const vSize = computeSize.value;\n            const isDisabled = computeIsDisabled.value;\n            const isChecked = computeIsChecked.value;\n            const indeterminate = !isChecked && props.indeterminate;\n            return h('label', {\n                key: label,\n                class: ['vxe-checkbox', {\n                        [`size--${vSize}`]: vSize,\n                        'is--indeterminate': indeterminate,\n                        'is--disabled': isDisabled,\n                        'is--checked': isChecked\n                    }],\n                title: props.title\n            }, [\n                h('input', {\n                    class: 'vxe-checkbox--input',\n                    type: 'checkbox',\n                    disabled: isDisabled,\n                    checked: isChecked,\n                    onChange: changeEvent\n                }),\n                h('span', {\n                    class: ['vxe-checkbox--icon', indeterminate ? getIcon().CHECKBOX_INDETERMINATE : (isChecked ? getIcon().CHECKBOX_CHECKED : getIcon().CHECKBOX_UNCHECKED)]\n                }),\n                h('span', {\n                    class: 'vxe-checkbox--label'\n                }, slots.default ? slots.default({}) : getFuncText(props.content))\n            ]);\n        };\n        $xeCheckbox.renderVN = renderVN;\n        return $xeCheckbox;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n", "import { VxeUI } from '@vxe-ui/core';\nimport VxeCheckboxComponent from './src/checkbox';\nimport { dynamicApp } from '../dynamics';\nexport const VxeCheckbox = Object.assign(VxeCheckboxComponent, {\n    install(app) {\n        app.component(VxeCheckboxComponent.name, VxeCheckboxComponent);\n    }\n});\ndynamicApp.use(VxeCheckbox);\nVxeUI.component(VxeCheckboxComponent);\nexport const Checkbox = VxeCheckbox;\nexport default VxeCheckbox;\n", "import VxeCheckbox from '../checkbox';\nexport * from '../checkbox';\nexport default VxeCheckbox;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,sBAAoB;AAGpB,IAAO,mBAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,YAAY,CAAC,QAAQ,QAAQ,OAAO;AAAA,IACpC,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACb;AAAA,IACA,eAAe;AAAA,IACf,OAAO,CAAC,QAAQ,MAAM;AAAA,IACtB,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,QAAQ,OAAO;AAAA,MAC9B,SAAS;AAAA,IACb;AAAA,IACA,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,QAAQ,OAAO;AAAA,MAC9B,SAAS;AAAA,IACb;AAAA,IACA,SAAS,CAAC,QAAQ,MAAM;AAAA,IACxB,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,SAAS,QAAQ,UAAU,EAAE;AAAA,IAC5D;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,UAAU,OAAO,WAAW,IAAI;AACtC,UAAM,eAAe,OAAO,kBAAkB,IAAI;AAClD,UAAM,mBAAmB,OAAO,oBAAoB,IAAI;AACxD,UAAM,MAAM,gBAAAA,QAAQ,SAAS;AAC7B,UAAM,YAAY,SAAS,CAAC,CAAC;AAC7B,UAAM,cAAc;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAI,kBAAkB,CAAC;AACvB,UAAM,EAAE,YAAY,IAAI,QAAQ,KAAK;AACrC,UAAM,mBAAmB,SAAS,MAAM;AACpC,UAAI,kBAAkB;AAClB,eAAO,gBAAAA,QAAQ,SAAS,iBAAiB,MAAM,YAAY,MAAM,KAAK;AAAA,MAC1E;AACA,aAAO,MAAM,eAAe,MAAM;AAAA,IACtC,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,YAAY,iBAAiB;AACnC,UAAI,aAAa,MAAM;AACnB,YAAI,kBAAkB;AAClB,gBAAM,EAAE,mBAAmB,wBAAwB,mBAAmB,uBAAuB,IAAI,iBAAiB,eAAe;AACjI,gBAAM,kBAAkB,uBAAuB;AAC/C,gBAAM,kBAAkB,uBAAuB;AAC/C,iBAAO,mBAAoB,mBAAmB,CAAC;AAAA,QACnD;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,cAAc,CAAC,SAAS;AAC1B,YAAM,EAAE,cAAc,eAAe,IAAI;AACzC,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,YAAY;AACb,cAAM,UAAU,KAAK,OAAO;AAC5B,cAAM,QAAQ,UAAU,eAAe;AACvC,cAAM,SAAS,EAAE,SAAS,OAAO,OAAO,MAAM,MAAM;AACpD,YAAI,kBAAkB;AAClB,2BAAiB,cAAc,QAAQ,IAAI;AAAA,QAC/C,OACK;AACD,eAAK,qBAAqB,KAAK;AAC/B,0BAAgB,cAAc,UAAU,QAAQ,IAAI;AAEpD,cAAI,WAAW,cAAc;AACzB,oBAAQ,iBAAiB,MAAM,aAAa,WAAW,OAAO,KAAK;AAAA,UACvE;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,WAAW,YAAY,GAAG,MAAM,CAAC;AAAA,IACpE;AACA,sBAAkB;AAAA,MACd;AAAA,IACJ;AACA,WAAO,OAAO,aAAa,eAAe;AAC1C,UAAM,WAAW,MAAM;AACnB,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,QAAQ,YAAY;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,gBAAgB,CAAC,aAAa,MAAM;AAC1C,aAAO,EAAE,SAAS;AAAA,QACd,KAAK;AAAA,QACL,OAAO,CAAC,gBAAgB;AAAA,UAChB,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,qBAAqB;AAAA,UACrB,gBAAgB;AAAA,UAChB,eAAe;AAAA,QACnB,CAAC;AAAA,QACL,OAAO,MAAM;AAAA,MACjB,GAAG;AAAA,QACC,EAAE,SAAS;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,QACd,CAAC;AAAA,QACD,EAAE,QAAQ;AAAA,UACN,OAAO,CAAC,sBAAsB,gBAAgB,QAAQ,EAAE,yBAA0B,YAAY,QAAQ,EAAE,mBAAmB,QAAQ,EAAE,kBAAmB;AAAA,QAC5J,CAAC;AAAA,QACD,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,MAAM,UAAU,MAAM,QAAQ,CAAC,CAAC,IAAI,YAAY,MAAM,OAAO,CAAC;AAAA,MACrE,CAAC;AAAA,IACL;AACA,gBAAY,WAAW;AACvB,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;;;ACnIM,IAAM,cAAc,OAAO,OAAO,kBAAsB;AAAA,EAC3D,QAAQ,KAAK;AACT,QAAI,UAAU,iBAAqB,MAAM,gBAAoB;AAAA,EACjE;AACJ,CAAC;AACD,WAAW,IAAI,WAAW;AAC1B,MAAM,UAAU,gBAAoB;AAC7B,IAAM,WAAW;AACxB,IAAOC,oBAAQ;;;ACTf,IAAO,uBAAQC;", "names": ["XEUtils", "checkbox_default", "checkbox_default"]}