import { createJiti } from "../../../node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/jiti.mjs";

const jiti = createJiti(import.meta.url, {
  "interopDefault": true,
  "alias": {
    "@vben/tailwind-config": "/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/internal/tailwind-config"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/internal/tailwind-config/src/postcss.config.js")} */
const _module = await jiti.import("/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/internal/tailwind-config/src/postcss.config.ts");

export default _module?.default ?? _module;