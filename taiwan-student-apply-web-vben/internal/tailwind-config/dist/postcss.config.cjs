const { createJiti } = require("../../../node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/jiti.cjs")

const jiti = createJiti(__filename, {
  "interopDefault": true,
  "alias": {
    "@vben/tailwind-config": "/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/internal/tailwind-config"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/internal/tailwind-config/src/postcss.config.js")} */
module.exports = jiti("/Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben/internal/tailwind-config/src/postcss.config.ts")